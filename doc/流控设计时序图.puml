@startuml 流控设计时序图
participant 创作者平台 as pt
participant web_ocean_wave as wave
participant 罗马推送 as roma
database redis
database kafka

wave -> kafka: 拉取消息
activate kafka
return 拉取结果

wave -> redis: 获取liveId级别分布式锁
activate redis
return 获取结果

note over of wave: 获取不到就等待

== 流量控制 ==
wave -> redis: 查询Hash集合中liveId对应的信息
activate redis
return 查询结果

alt Hash集合中不存在liveId对应信息
    wave -> redis: 添加Hash集合中liveId、评论时间戳信息
    activate redis
    return 添加结果

    wave -> redis: 设置该liveId下消息数量为1
    activate redis
    return 设置结果

else Hash集合中存在liveId对应信息
    wave -> redis: 累加该liveId下消息数量
    activate redis
    return 累加结果

    alt #skyblue 开启到达指定量即推送开关以及消息数量大于等于80
        wave -> wave: 构建推送消息体
        wave -> roma: 发送消息到消息队列
        activate roma
        return 推送结果

        wave -> redis: lua脚本删除zSet集合中liveId对应的信息和消息累加器的值
        activate redis
        return 删除结果
    end
end

note over of wave: 失败了不提交kafka消息的offset


== 定时任务推送消息变更（1s执行一次） ==
wave -> wave: 使用线程池一个业务一个线程池
wave -> redis: 从zSet集合中查询出所有超时的liveId \n （超时时间可配置）
activate redis
return 扫描结果

loop 遍历直播间
    group 加liveId级别的锁
        wave --> wave: 构建推送消息体
        wave --> roma: 发送消息到消息队列
        activate roma
        return 推送结果
        alt 推送成功
            wave --> redis: lua脚本删除集合中liveId对应的信息和消息累加器的值
            activate redis
            return 删除结果
        end
    end
end

@enduml