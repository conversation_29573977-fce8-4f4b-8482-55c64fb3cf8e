@startuml 流控设计时序图
participant 创作者平台 as pt
participant web_ocean_wave as wave
participant lz_xx_comment as comment
participant 罗马推送 as roma
database redis
database kafka

== 业务发评论 ==
alt 如果是白名单评论类型
    comment -> kafka: 发送完整评论消息到消息队列
    activate kafka
    return 发送结果
else 不是白名单评论类型
    comment -> kafka: 发送简单的评论消息到消息队列
    activate kafka
    return 发送结果
end

note over of comment: 发送简单评论消息到消息队列逻辑已有，简单改造一下即可

== 监听评论消息 ==
wave -> kafka: 拉取评论消息
activate kafka
return 获取结果

wave -> wave: 根据评论类型获取卡片消息处理器
alt 获取不到处理器
    note over of wave: 结束流程
end

wave -> wave: 解析并构建评论消息-卡片数据

wave -> wave: 构建评论其他信息

wave -> redis: 保存构建好的评论
activate redis
return 保存结果

wave -> roma: 推送卡片消息给PC端
activate roma
return 推送结果

@enduml