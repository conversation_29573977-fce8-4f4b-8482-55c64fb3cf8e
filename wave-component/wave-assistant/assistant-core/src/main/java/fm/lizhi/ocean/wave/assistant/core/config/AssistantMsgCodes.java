package fm.lizhi.ocean.wave.assistant.core.config;

import fm.lizhi.ocean.wave.server.common.constant.IRCodes;
import lombok.Getter;

/**
 * description: 助手功能code
 *
 * <AUTHOR>
 * @date 2023/08/16
 */
@Getter
public enum AssistantMsgCodes implements IRCodes {
    //-------------------CheckInController 11000xxx-------------------
    UPDATE_CHECK_IN_EXAM_CHARM_FAIL(11000001, "修改考核魅力值失败"),

    GET_CHECK_IN_DETAIL_PARAM(11000002, "参数异常"),
    GET_CHECK_IN_DETAIL_NO_SCHEDULE(11000003, "档期不存在"),
    CHECK_IN_PARAM(11000004, "参数异常"),
    CHECK_IN_FAIL(11000005, "保存失败，请稍候重试"),
    CHECK_IN_LIVE_NO_EXIST(11000006, "直播间不存在"),
    CHECK_IN_NO_PERMISSION(11000007, "您没有权限打卡，请上到1号麦位后再操作"),
    CHECK_IN_NO_PERMISSION_RETRY(11000008, "当前档期的主持人不是您，您无权限保存"),
    CHECK_IN_NO_SCHEDULE(11000009, "档期不存在"),
    CHECK_IN_NO_ADJUST_CHARM(11000010, "当前档期未结束，无法调账"),
    CHECK_IN_REMARK_TOO_LONG(11000011, "备注信息太长，不要超过#{length}字"),
    CHECK_IN_DATA_ERROR(11000012, "魅力值发生变化，请刷新后再打卡"),
    CHECK_IN_CHARM_EXIST_UN_ALLOCATE(11000013, "仍有魅力值差额，请调整后重试"),
    CHECK_IN_USER_IS_HOST(11000014, "已经是主持人"),
    CHECK_IN_NO_IN_ROOM_CREATOR_ROLE_ERROR(11000015, "获取不在房生产者角色失败"),
    CHECK_IN_OP_HOST_ERROR(11000016, "操作档期主持人失败！"),
    NO_IN_ROOM_CREATOR_ROLE_ERROR(11000017, "获取不在房生产者角色失败"),
    CHECK_IN_NO_SIGN_PLAYER(11000018, "非本厅签约成员，不可设置为主持"),
    CHECK_IN_OP_HOST_NO_ROLE(11000019, "你并非房主、本厅签约成员，无权设定本档主持"),
    SCHEDULE_ADD_HOST_PARAM(11000021, "参数异常"),
    SCHEDULE_ADD_HOST_FAIL(11000022, "添加主持失败"),
    CHECK_IN_NO_HOST(11000023, "无法保存！\n请先设定本场主持后，再记录麦序福利信息"),
    CHECK_IN_NO_RECORD_DEL(11000024, "主播记录不存在，请刷新后重试"),
    CHECK_IN_NJ_HAS_INCOME(11000025, "主播有收入或是定排主播，无法删除，请刷新后重试"),
    CHECK_IN_OP_HOST_NO_PERMISSION(11000026, "你并非厅主或本档主持人，转让失败"),
    CHECK_IN_GET_CONFIG_FAIL(11000027, "获取麦序福利配置失败"),
    CHECK_IN_SAVE_VERSION_FAIL(11000028, "保存失败，该档期已被更新，请刷新后重试"),
    CHECK_IN_OPERATE_TASK_PROCESS_FAIL(11000029, "任务分为0，不允许修改任务进度"),
    CHECK_IN_CANNOT_OPERATE_TASK_PROCESS(11000030, "暂不允许修改任务进度"),
    CHECK_IN_OP_HOST_IS_HOST(11000031, "不可转让给原主持"),
    CHECK_IN_CANNOT_VISIT(11000032, "该功能已禁用，请升级最新版本"),
    CHECK_IN_NOT_PERMISSION_OP_HOST(11000033, "无操作权限"),
    CHECK_IN_GET_LIGHT_GIFT_LIST_FAIL(11000034, "获取收光记录失败"),
    CHECK_IN_CANNOT_OPERATE_SCHEDULE(11000035, "超过24小时的档期无法调整"),
    CHECK_IN_GET_MY_TASK_FAIL(11000036, "获取我的任务列表失败"),
    CHECK_IN_CANNOT_OPERATE_TASK_SCORE(11000037, "档期已结束，不允许修改任务分"),
    CHECK_IN_CANNOT_OPERATE_TASK_SCORE_BY_NOT_SCHEDULE(11000038, "非定排主播不允许修改任务分"),
    CHECK_IN_PROCESS_GTE_CHARM(11000039, "保存失败:任务进度不允许大于魅力值"),
    CHECK_IN_IS_CLOSE(11000040, "功能维护中"),
    CHECK_IN_UNFINISHED_SETTLEMENT(11000041, "任务结算中，暂不支持调账，请一分钟后重试"),
    CHECK_IN_OLD_SCHEDULE_DATA_CAN_NOT_OPERATE(11000043, "旧档期数据不允许操作"),
    CHECK_IN_ALLOCATION_NOT_PERMISSION(11000044, "全麦奖励分配失败，无权限"),
    CHECK_IN_ALLOCATION_FAIL(11000045, "全麦奖励分配失败"),
    GET_CHECK_IN_ALL_MIC_LIST_FAIL(11000046, "获取全麦记录失败"),
    CHECK_IN_OPERATE_PERMISSION_FAIL(11000047, "不允许调账"),
    CHECK_IN_NOT_SCHEDULE_CANNOT_CHECK_IN(11000048, "操作失败，需将主播设置为定排主播方可勾选麦序"),


    //-------------------FriendshipNotesController 11001xxx-------------------
    SAVE_OR_UPDATE_FRIENDSHIP_NOTES_FAIL(11001001, "添加或修改笔记失败"),
    GET_FRIENDSHIP_NOTES_FAIL(11001002, "获取笔记失败"),
    GET_FRIENDSHIP_NOTES_TAB_FAIL(11001003, "获取笔记TAB失败"),
    FRIENDSHIP_NOTES_MAX_LENGTH(11001004, "超过最大字数限制，保存失败"),


    //-------------------AnnouncementController 11002xxx-------------------
    GET_ANNOUNCEMENT_FAIL(11002001, "获取公告失败"),

    //-------------------UpdateNotesController 11003xxx-------------------
    GET_UPDATE_NOTES_FAIL(11003001, "获取更新公告失败"),

    ;


    private final int code;
    private String msg;

    AssistantMsgCodes(int rCode, String msg) {
        this.code = rCode;
        this.msg = msg;
    }

    public AssistantMsgCodes setMsg(String msg) {
        this.msg = msg;
        return this;
    }
}
