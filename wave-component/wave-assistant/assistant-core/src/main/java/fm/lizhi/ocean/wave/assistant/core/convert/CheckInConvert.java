package fm.lizhi.ocean.wave.assistant.core.convert;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.util.StrUtil;
import fm.lizhi.ocean.wave.assistant.core.datastore.entity.WaveCheckInRecord;
import fm.lizhi.ocean.wave.assistant.core.datastore.entity.WaveCheckInSchedule;
import fm.lizhi.ocean.wave.assistant.core.model.bean.*;
import fm.lizhi.ocean.wave.assistant.core.model.param.CheckInOperateParam;
import fm.lizhi.ocean.wave.assistant.core.model.param.GetAllMicGiftNotAllocationParam;
import fm.lizhi.ocean.wave.assistant.core.model.param.GetCheckInHostConfigParam;
import fm.lizhi.ocean.wave.assistant.core.model.result.GetCheckInConfigResult;
import fm.lizhi.ocean.wave.assistant.core.model.result.GetCheckInDetailsResult;
import fm.lizhi.ocean.wave.assistant.core.model.vo.CheckInAllMicNotAllocationVo;
import fm.lizhi.ocean.wave.assistant.core.model.vo.CheckInScheduleVO;
import fm.lizhi.ocean.wave.assistant.core.model.vo.PageCheckInAllMicRecordVo;
import fm.lizhi.ocean.wave.assistant.core.model.vo.PageLightGiftRecordVo;
import fm.lizhi.ocean.wave.platform.api.platform.bean.WaveCheckInHostConfigBean;
import fm.lizhi.ocean.wave.platform.api.platform.bean.WaveEffectiveCheckInAllMicGiftConfigBean;
import fm.lizhi.ocean.wave.platform.api.platform.bean.WaveEffectiveCheckInBaseConfigBean;
import fm.lizhi.ocean.wave.platform.api.platform.constant.CheckInHistoryLimitDateEnum;
import fm.lizhi.ocean.wave.platform.api.platform.constant.CheckInTaskEnum;
import fm.lizhi.ocean.wave.platform.api.platform.request.RequestAllMicGiftNotAllocation;
import fm.lizhi.ocean.wave.platform.api.platform.request.RequestCheckInOperateCore;
import fm.lizhi.ocean.wave.platform.api.platform.request.RequestGetCheckInHostTimeSlotConfig;
import fm.lizhi.ocean.wave.platform.api.platform.response.ResponseGetCheckInDetails;
import fm.lizhi.ocean.wave.platform.api.platform.response.ResponseGetCheckInMyTask;
import fm.lizhi.ocean.wave.platform.api.platform.response.ResponseGetEffectiveCheckInConfig;
import fm.lizhi.ocean.wave.user.bean.SimpleUser;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Mapper(
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        /* 未映射目标属性的报告策略 */
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        /* 确定何时对bean映射的源属性值包含空检查 */
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface CheckInConvert {

    CheckInConvert I = Mappers.getMapper(CheckInConvert.class);


    /**
     * 默认 今日未完成分数
     */
    String DEFAULT_UNDONE_SCORE = "0";

    /**
     * 今日未完成分数 结算中标记
     */
    String UNDONE_SETTLEMENT_FLAT = "-1";


    default Long dateToLong(Date value){
        return value == null ? 0L : value.getTime();
    }

    @Mapping(target = "startTime", expression = "java(record.getStartTime().getTime())")
    @Mapping(target = "endTime", expression = "java(record.getEndTime().getTime())")
    CheckInScheduleVO schedulePo2VO(WaveCheckInSchedule record);

    List<CheckInScheduleVO> scheduleListPo2VOs(List<WaveCheckInSchedule> records);

    @Mapping(target = "avatar", expression = "java(fm.lizhi.ocean.wave.common.util.UrlUtils.getAvatarUrl(cdnHost, result.getAvatar()))")
    @Mapping(target = "name", source = "result.nickName")
    CheckInUserInfoBean simpleUser2UserInfoBean(SimpleUser result, String cdnHost);

    default List<CheckInUserInfoBean> simpleUserList2UserInfoBeans(List<SimpleUser> result, String cdnHost) {
        if (result == null) {
            return null;
        }
        return result.stream().map(e -> simpleUser2UserInfoBean(e, cdnHost)).collect(Collectors.toList());
    }

    @Mapping(target = "recordId", source = "record.id")
    @Mapping(target = "checkInCount", ignore = true)
    @Mapping(target = "originalCharmValue", source = "record.originalValue")
    @Mapping(target = "name", source = "userInfoBean.name")
    @Mapping(target = "avatar", source = "userInfoBean.avatar")
    @Mapping(target = "userId", source = "record.userId")
    CheckInRecordBean checkInRecordPo2Beans(WaveCheckInRecord record, CheckInUserInfoBean userInfoBean);

    @Mapping(target = "scheduleId", source = "record.id")
    CheckInScheduleBean checkInSchedulePo2Bean(WaveCheckInSchedule record);


    default GetCheckInConfigResult convertGetCheckInConfigResult(ResponseGetEffectiveCheckInConfig target, Long earlyStartSecond, Boolean enableOperateTaskProgressDiff){
        GetCheckInConfigResult result = new GetCheckInConfigResult();

        WaveEffectiveCheckInBaseConfigBean baseConfig = target.getBaseConfig();
        result.setShowAd(baseConfig.getShowAd());
        result.setAdUrl(baseConfig.getAdUrl());
        result.setShowReward(baseConfig.getShowReward());
        result.setHistoryLimitTimestamp(CheckInHistoryLimitDateEnum.getLimitTime(baseConfig.getHistoryLimitDate()));
        result.setTaskRule(baseConfig.getTaskType());
        result.setTaskDesc(CheckInTaskEnum.getDescByType(baseConfig.getTaskType()));
        result.setEarlyStartSecond(earlyStartSecond);
        result.setLightGiftEnable(target.getLightGiftConfig().getEnabled());
        result.setEnableOperateTaskProgressDiff(enableOperateTaskProgressDiff);
        result.setAllMicGiftEnable(Optional.ofNullable(target.getAllMicGiftConfig()).map(WaveEffectiveCheckInAllMicGiftConfigBean::getEnabled).orElse(false));
        result.setCheckInManagerConfig(target.getCheckInManagerConfig());
        result.setAdjustCharmAuthType(baseConfig.getAdjustCharmAuthType());

        return result;
    }


    RequestCheckInOperateCore convertRequestCheckInOperateCore(CheckInOperateParam checkInOperateBean, int appId, long operateUserId);

    List<GetCheckInDetailsResult> convertGetCheckInDetailsResult(List<ResponseGetCheckInDetails> target);

    @Mappings({
            @Mapping(target = "userId", source = "checkInRecordBean.userId"),
            @Mapping(target = "recordId", source = "checkInRecordBean.recordId"),
            @Mapping(target = "lightGiftDetail", source = "checkInLightGiftRecordList", qualifiedByName = "convertLightGiftRecord"),
            @Mapping(target = "allMicGiftDetail", source = "checkInAllMicGiftRecordList", qualifiedByName = "convertAllMicGiftRecord"),
            @Mapping(target = "taskDailyUnDoneDetail", source = "checkInUserTask", qualifiedByName = "convertTaskDailyUnDoneDetail"),
            @Mapping(target = "taskDailyScoreChange", source = "checkInUserTask", qualifiedByName = "convertTaskDailyScoreChange")
    })
    CheckInDetailBean buildCheckInDetailBean(CheckInRecordBean checkInRecordBean, CheckInUserTaskBean checkInUserTask, List<CheckInLightGiftRecordBean> checkInLightGiftRecordList, List<CheckInAllMicGiftRecordBean> checkInAllMicGiftRecordList);


    @Named("convertLightGiftRecord")
    default String convertLightGiftRecord(List<CheckInLightGiftRecordBean> checkInLightGiftRecordList) {
        if (checkInLightGiftRecordList == null || checkInLightGiftRecordList.isEmpty()) {
            return "0";
        }

        // 使用 HashMap 累加奖励值及其数量，避免展开成完整的奖励列表。
        Map<Long, Long> countMap = new HashMap<>();
        for (CheckInLightGiftRecordBean record : checkInLightGiftRecordList) {
            long reward = record.getRewardLadder();
            long amount = record.getGiftAmount();

            // 累加每个奖励值的数量
            countMap.merge(reward, amount, Long::sum);
        }

        // 获取所有的奖励值（键）并排序
        List<Long> sortedKeys = new ArrayList<>(countMap.keySet());
        Collections.sort(sortedKeys);

        // 拼接结果字符串, 尽量减少流操作，避免大数据量情况下带来的性能开销和对象回收压力
        StringBuilder result = new StringBuilder();
        for (Long key : sortedKeys) {
            if (result.length() > 0) {
                result.append(",");
            }
            result.append(key).append("*").append(countMap.get(key));
        }
        return result.toString();
    }

    @Named("convertAllMicGiftRecord")
    default String convertAllMicGiftRecord(List<CheckInAllMicGiftRecordBean> checkInAllMicGiftRecordList) {
        if (checkInAllMicGiftRecordList == null || checkInAllMicGiftRecordList.isEmpty()) {
            return "0";
        }

        // 使用 TreeMap 并指定降序比较器
        Map<Long, Long> countMap = new TreeMap<>(Comparator.reverseOrder());
        for (CheckInAllMicGiftRecordBean record : checkInAllMicGiftRecordList) {
            long reward = record.getRewardLadder();
            countMap.merge(reward, 1L, Long::sum);
        }

        StringJoiner resultJoiner = new StringJoiner(",");
        countMap.forEach((reward, count) -> resultJoiner.add(reward + "*" + count));

        return resultJoiner.toString();
    }

    @Named("convertTaskDailyScoreChange")
    default String convertTaskDailyScoreChange(CheckInUserTaskBean checkInUserTask){
        if (null == checkInUserTask || StringUtils.isBlank(checkInUserTask.getTaskDailyScoreChange()) || DEFAULT_UNDONE_SCORE.equals(checkInUserTask.getTaskDailyScoreChange())) {
            return DEFAULT_UNDONE_SCORE;
        }
        return convertTaskDetail(checkInUserTask.getTaskDailyScoreChange(), checkInUserTask.getTaskRule());
    }


    @Named("convertTaskDailyUnDoneDetail")
    default String convertTaskDailyUnDoneDetail(CheckInUserTaskBean checkInUserTask){
        if (null == checkInUserTask || StringUtils.isBlank(checkInUserTask.getTaskDailyUnDoneDetail()) || DEFAULT_UNDONE_SCORE.equals(checkInUserTask.getTaskDailyUnDoneDetail())) {
            return DEFAULT_UNDONE_SCORE;
        }
        return convertTaskDetail(checkInUserTask.getTaskDailyUnDoneDetail(), checkInUserTask.getTaskRule());
    }

    @Named("convertTaskDailyScoreChange")
    default String convertTaskDailyScoreChange(ResponseGetCheckInMyTask myTask){
        if (null == myTask || StringUtils.isBlank(myTask.getTaskDailyScoreChange()) || DEFAULT_UNDONE_SCORE.equals(myTask.getTaskDailyScoreChange())) {
            return DEFAULT_UNDONE_SCORE;
        }
        return convertTaskDetail(myTask.getTaskDailyScoreChange(), myTask.getTaskRule(), true);
    }

    @Named("convertTaskDailyDoneScore")
    default String convertTaskDailyDoneScore(ResponseGetCheckInMyTask myTask){
        if (null == myTask || StringUtils.isBlank(myTask.getTaskDailyDoneScore()) || DEFAULT_UNDONE_SCORE.equals(myTask.getTaskDailyDoneScore())) {
            return DEFAULT_UNDONE_SCORE;
        }
        return convertTaskDetail(myTask.getTaskDailyDoneScore(), myTask.getTaskRule());
    }

    @Named("convertTaskDailyUnDoneDetail")
    default String convertTaskDailyUnDoneDetail(ResponseGetCheckInMyTask myTask){
        if (null == myTask || StringUtils.isBlank(myTask.getTaskDailyUnDoneDetail()) || DEFAULT_UNDONE_SCORE.equals(myTask.getTaskDailyUnDoneDetail())) {
            return DEFAULT_UNDONE_SCORE;
        }
        return convertTaskDetail(myTask.getTaskDailyUnDoneDetail(), myTask.getTaskRule());
    }


    default String convertTaskDetail(String taskDetail, Integer taskRule, Boolean keepSymbols) {
        if (taskDetail.equals(UNDONE_SETTLEMENT_FLAT)){
            return "正在结算中...";
        }

        if (!taskRule.equals(CheckInTaskEnum.FULL_TASK_COMPLETION.getType())){
            return taskDetail;
        }

        return formatNumberListWithCounts(StrUtil.split(taskDetail, ",").stream().map(Long::parseLong).collect(Collectors.toList()), keepSymbols);
    }

    default String convertTaskDetail(String taskDetail, Integer taskRule) {
        return convertTaskDetail(taskDetail, taskRule, false);
    }

    /**
     * 将数字列表转换成字符串
     * @param numberList 100,200,100,300
     * @return "100*2,200*1,300*1"
     */
    @Named("formatNumberListWithCounts")
    default String formatNumberListWithCounts(List<Long> numberList){
        return formatNumberListWithCounts(numberList, false);
    }

    /**
     * 将数字列表转换成字符串
     * @param numberList 100,200,100,300
     * @param keepSymbols 保留符号，比如 "+100*2,+200*1,+300*1 -100*2"
     * @return "+100*2,+200*1,+300*1 -100*2"
     */
    @Named("formatNumberListWithCounts")
    default String formatNumberListWithCounts(List<Long> numberList, Boolean keepSymbols) {
        if (CollUtil.isEmpty(numberList)) {
            return "";
        }

        return numberList.stream()
                .collect(Collectors.groupingBy(n -> n, Collectors.counting()))
                .entrySet()
                .stream()
                .sorted(Map.Entry.comparingByKey())
                .map(entry -> {
                    Long number = entry.getKey();
                    if (keepSymbols){
                        return (number > 0 ? "+" + number : number) + "*" + entry.getValue();
                    }else {
                        return number + "*" + entry.getValue();
                    }
                })
                .collect(Collectors.joining(","));
    }

    default List<CheckInDetailBean> buildCheckInDetailBeanList(List<GetCheckInDetailsResult> results, Map<Long, CheckInRecordBean> checkInRecordBeanMap){
        if (MapUtils.isEmpty(checkInRecordBeanMap)){
            return new ArrayList<>();
        }

        if (CollUtil.isEmpty(results)){
            return new ArrayList<>();
        }

        List<CheckInDetailBean> result = new ArrayList<>();
        Map<Long, GetCheckInDetailsResult> resultMap = results.stream().collect(Collectors.toMap(e -> e.getCheckInRecord().getUserId(), Function.identity(), (e1, e2) -> e1));
        for (Long userId : checkInRecordBeanMap.keySet()) {
            if (!resultMap.containsKey(userId)){
                continue;
            }

            GetCheckInDetailsResult detailsResult = resultMap.get(userId);
            CheckInDetailBean bean = buildCheckInDetailBean(checkInRecordBeanMap.get(userId), detailsResult.getCheckInUserTask(),
                    detailsResult.getCheckInLightGiftRecordList(), detailsResult.getCheckInAllMicGiftRecordList()
            );
            result.add(bean);
        }
        return result;
    }


    List<CheckInLightGiftRecordBean> convertCheckInLightGiftRecordBeanList(List<fm.lizhi.ocean.wave.platform.api.platform.bean.CheckInLightGiftRecordBean> list);


    PageLightGiftRecordVo buildPageLightGiftRecordVo(CheckInLightGiftRecordBean record, SimpleUser sendUser, SimpleUser recUser);

    @Mappings({
            @Mapping(target = "taskDailyUnDoneDetail", source = "target", qualifiedByName = "convertTaskDailyUnDoneDetail"),
            @Mapping(target = "taskDailyScoreChange", source = "target", qualifiedByName = "convertTaskDailyScoreChange"),
            @Mapping(target = "taskDailyDoneScore", source = "target", qualifiedByName = "convertTaskDailyDoneScore"),
            @Mapping(target = "scheduleInfo.scheduleId", source = "scheduleInfo.id"),
    })
    CheckInMyTaskBean convertCheckInMyTaskBean(ResponseGetCheckInMyTask target);

    List<CheckInMyTaskBean> convertCheckInMyTaskBeanList(List<ResponseGetCheckInMyTask> target);


    CheckInHostConfigBean convertCheckInHostConfigBean(WaveCheckInHostConfigBean target);

    RequestGetCheckInHostTimeSlotConfig buildRequestGetCheckInHostTimeSlotConfig(GetCheckInHostConfigParam param, DateTime startTime, DateTime endTime, int timeSlot);

    GetCheckInHostConfigParam buildGetCheckInHostConfigParam(Integer appId, Long familyId, Long roomId, Long scheduleStartTime);

    RequestAllMicGiftNotAllocation buildRequestAllMicGiftNotAllocation(GetAllMicGiftNotAllocationParam param);

    List<CheckInAllMicGiftRecordBean> convertCheckInAllMicGiftRecordBeanList(List<fm.lizhi.ocean.wave.platform.api.platform.bean.CheckInAllMicGiftRecordBean> target);

    @Mappings({
            @Mapping(target = "sendTime", source = "record.createTime"),
    })
    CheckInAllMicNotAllocationVo buildCheckInAllMicNotAllocationVo(CheckInAllMicGiftRecordBean record, SimpleUser sendUser);

    PageCheckInAllMicRecordVo buildPageCheckInAllMicRecordVo(CheckInAllMicGiftRecordBean record, SimpleUser sendUser, SimpleUser allocationUser);
}
