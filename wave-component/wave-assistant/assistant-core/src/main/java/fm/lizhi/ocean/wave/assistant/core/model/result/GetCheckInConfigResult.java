package fm.lizhi.ocean.wave.assistant.core.model.result;

import fm.lizhi.ocean.wave.assistant.core.model.vo.WaveCheckInLightGiftConfigVo;
import fm.lizhi.ocean.wave.platform.api.platform.constant.CheckInHistoryLimitDateEnum;
import fm.lizhi.ocean.wave.platform.api.platform.constant.CheckInTaskEnum;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 获取麦序福利配置
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class GetCheckInConfigResult {


    // region ---- 基础配置 ----

    /**
     * 是否展示麦序福利图
     */
    private Boolean showAd;

    /**
     * 麦序福利图 URL
     */
    private String adUrl;

    /**
     * 是否展示奖励金额
     */
    private Boolean showReward;

    /**
     * 是否开启收光
     */
    private Boolean lightGiftEnable;

    /**
     * 查看最长历史打卡记录类型时间戳
     * @see CheckInHistoryLimitDateEnum
     */
    private Long historyLimitTimestamp;

    /**
     * 任务计算规则
     * @see CheckInTaskEnum
     */
    private Integer taskRule;

    /**
     * 任务计算规则描述
     * @see CheckInTaskEnum
     */
    private String taskDesc;

    /**
     * 提前 N 秒可编辑下一档期
     */
    private Long earlyStartSecond;

    /**
     * 是否开启任务进度调账
     */
    private Boolean enableOperateTaskProgressDiff;

    /**
     * 全麦奖励开关
     */
    private Boolean allMicGiftEnable;

    /**
     * 管理员列表配置
     */
    private List<Long> checkInManagerConfig;

    /**
     * 调账权限类型
     *
     * @see fm.lizhi.ocean.wave.platform.api.platform.constant.CheckInAdjustCharmAuthTypeEnum
     */
    private Integer adjustCharmAuthType;


}
