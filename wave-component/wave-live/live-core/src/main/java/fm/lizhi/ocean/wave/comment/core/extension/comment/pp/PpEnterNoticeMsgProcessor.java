package fm.lizhi.ocean.wave.comment.core.extension.comment.pp;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wave.comment.core.config.CommentCommonConfig;
import fm.lizhi.ocean.wave.comment.core.config.CommentConfig;
import fm.lizhi.ocean.wave.comment.core.extension.comment.IEnterNoticeMsgProcessor;
import fm.lizhi.ocean.wave.comment.core.extension.medal.v2.FreshUserMedalV2Node;
import fm.lizhi.ocean.wave.comment.core.extension.medal.v2.ICommentV2MedalNode;
import fm.lizhi.ocean.wave.comment.core.extension.medal.v2.VipLevelMedalV2Node;
import fm.lizhi.ocean.wave.comment.core.extension.medal.v2.WealthMedalV2Node;
import fm.lizhi.ocean.wave.comment.core.model.dto.EnterNoticeDTO;
import fm.lizhi.ocean.wave.comment.core.model.vo.EnterMsgVO;
import fm.lizhi.ocean.wave.comment.core.model.vo.MsgUserVO;
import fm.lizhi.ocean.wave.common.util.ContainsUtils;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wave.user.api.VipPrivilegeService;
import fm.lizhi.ocean.wave.user.constant.WaveNoblePrivilegeType;
import fm.lizhi.ocean.wave.user.param.VipPrivilegeHideParam;
import fm.lizhi.ocean.wave.user.result.VipPrivilegeHideResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * PP进房消息处理器
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class PpEnterNoticeMsgProcessor implements IEnterNoticeMsgProcessor {

    @Autowired
    private VipPrivilegeService vipPrivilegeService;

    @Autowired
    private VipLevelMedalV2Node vipLevelMedalNode;

    @Autowired
    private FreshUserMedalV2Node freshUserMedalNode;

    @Autowired
    private WealthMedalV2Node wealthMedalNode;

    @Autowired
    private CommentConfig commentConfig;

    @Override
    public BusinessEvnEnum getBusinessEnv() {
        return BusinessEvnEnum.PP;
    }

    @Override
    public MsgUserVO<?> buildUserVoAndAdapterExtra(EnterNoticeDTO enterNoticeEntry) {
        return new MsgUserVO<>();
    }

    @Override
    public CommentCommonConfig getCommentConfig() {
        return commentConfig.getPp();
    }

    /**
     * 过滤进房公告特效
     *
     * @param enterNoticeEntries 用户ID
     * @return 过滤后的进房公告
     */
    @Override
    public List<EnterNoticeDTO> filterHideEnterNotice(List<EnterNoticeDTO> enterNoticeEntries) {
        if (CollectionUtils.isNotEmpty(enterNoticeEntries)) {
            List<EnterNoticeDTO> filterRes = new ArrayList<>(enterNoticeEntries.size());
            for (EnterNoticeDTO enterNoticeEntry : enterNoticeEntries) {
                //隐藏的就不显示了
                if (!isHideEnterNoticePrivilege(enterNoticeEntry.getUserId())) {
                    filterRes.add(enterNoticeEntry);
                }
            }
            return filterRes;
        }

        return enterNoticeEntries;
    }

    @Override
    public List<ICommentV2MedalNode> getBuildEnterNoticeMedalNodes() {
        List<ICommentV2MedalNode> nodes = new ArrayList<>(8);
        //1. 设置PP贵族
        //2. 设置财富等级勋章
        //3. 设置新用户勋章
        nodes.add(vipLevelMedalNode);
        nodes.add(wealthMedalNode);
        nodes.add(freshUserMedalNode);
        return nodes;
    }

    /**
     * 是否展示欢迎按钮
     *
     * @param liveId           直播节目ID
     * @param enterNoticeEntry 进房公告消息
     */
    @Override
    public void setShowButton(long liveId, long userId, EnterNoticeDTO enterNoticeEntry, EnterMsgVO enterNoticeVO) {

    }

    @Override
    public boolean isCanGetEnterNotice(long liveId) {
        return commentConfig.getPp().isEnterNoticeOn() && !ContainsUtils.contains(commentConfig.getPp().getBigStarLiveLiveIds(), liveId);
    }

    @Override
    public boolean isBigStarNjHideMount(long njId) {
        return false;
    }


    /**
     * 是否隐藏进房公告
     *
     * @param userId 用户ID
     * @return true: 隐藏，false: 不隐藏
     */
    private boolean isHideEnterNoticePrivilege(long userId) {
        VipPrivilegeHideParam param = VipPrivilegeHideParam.builder().userId(userId).privilege(WaveNoblePrivilegeType.ENTER_HIDDEN).build();
        Result<VipPrivilegeHideResult> result = vipPrivilegeService.isPrivilegeHideByCache(param);
        if (result.rCode() != 0) {
            log.warn("isHideEnterNoticePrivilege error, userId: {}, rCode: {}", userId, result.rCode());
            return false;
        }
        return result.target().isStatus();
    }
}
