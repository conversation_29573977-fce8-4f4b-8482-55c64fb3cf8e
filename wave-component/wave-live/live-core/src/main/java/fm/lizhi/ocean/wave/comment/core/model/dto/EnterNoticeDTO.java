package fm.lizhi.ocean.wave.comment.core.model.dto;

import fm.lizhi.ocean.wave.comment.core.remote.bean.FreshUser;
import fm.lizhi.ocean.wave.comment.core.remote.bean.UserMountEntry;
import fm.lizhi.ocean.wave.comment.core.remote.bean.VipInfoEntry;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 *
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class EnterNoticeDTO {
    /**
     * 用户Id
     */
    private Long userId;
    /**
     * 进入时间
     */
    private Long timeStamp;
    /**
     * 是否匿名进房 1是 0否
     */
    private Integer anonymous;
    /**
     * 文本内容
     */
    private String notice;
    /**
     * 位数，匿名进房时的合并数量
     */
    private Integer count;
    /**
     * 财富等级信息 (匿名时，忽略该值)
     */
    private WealthLevelDTO wealthInfo;
    /**
     * 用户座驾信息
     */
    private UserMountEntry userMountEntry;
    /**
     * 头像
     */
    private String userCover;

    /**
     * 新用户信息
     */
    private FreshUser freshUserEntry;

    private HyEnterNoticeExtraDTO hyEnterNoticeExtraDTO;

    private PpEnterNoticeExtraDTO ppEnterNoticeExtraDTO;

    private XmEnterNoticeExtraDTO xmEnterNoticeExtraDTO;

}
