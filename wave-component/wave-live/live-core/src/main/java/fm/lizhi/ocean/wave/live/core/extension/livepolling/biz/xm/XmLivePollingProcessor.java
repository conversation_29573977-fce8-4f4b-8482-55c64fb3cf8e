package fm.lizhi.ocean.wave.live.core.extension.livepolling.biz.xm;

import com.google.common.base.Objects;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.commons.service.client.rcode.GeneralRCode;
import fm.lizhi.live.cmpt.behavior.msg.anchor.AnchorBehaviorType;
import fm.lizhi.live.cmpt.behavior.msg.device.DeviceBehaviorType;
import fm.lizhi.live.cmpt.behavior.msg.device.RoomType;
import fm.lizhi.live.data.bean.UserBehaviorType;
import fm.lizhi.ocean.wave.api.live.constants.LiveRoomStatusEnum;
import fm.lizhi.ocean.wave.common.extension.consts.BizRouterEnum;
import fm.lizhi.ocean.wave.common.util.ThreadUtils;
import fm.lizhi.ocean.wave.server.common.vo.ResultVO;
import fm.lizhi.ocean.wave.live.core.config.CommonLiveConfig;
import fm.lizhi.ocean.wave.live.core.config.LiveConfig;
import fm.lizhi.ocean.wave.live.core.extension.livepolling.LivePollingProcessor;
import fm.lizhi.ocean.wave.live.core.extension.livepolling.bean.LivePollingPostBean;
import fm.lizhi.ocean.wave.live.core.kafka.producer.biz.pp.bean.PpAnchorBehaviorBean;
import fm.lizhi.ocean.wave.live.core.kafka.producer.biz.pp.bean.PpDeviceBehaviorBean;
import fm.lizhi.ocean.wave.live.core.kafka.producer.biz.pp.bean.PpUserBehaviorBean;
import fm.lizhi.ocean.wave.live.core.kafka.producer.biz.xm.XmBehaviorKafkaProducer;
import fm.lizhi.ocean.wave.live.core.kafka.producer.biz.xm.bean.XmAnchorBehaviorBean;
import fm.lizhi.ocean.wave.live.core.kafka.producer.biz.xm.bean.XmDeviceBehaviorBean;
import fm.lizhi.ocean.wave.live.core.kafka.producer.biz.xm.bean.XmUserBehaviorBean;
import fm.lizhi.xm.decorate.call.api.CallService;
import fm.lizhi.xm.decorate.call.protocol.LiveCallProto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;

/**
 * <AUTHOR>
 * @date 2023/5/26
 */
@Slf4j
@Component
public class XmLivePollingProcessor implements LivePollingProcessor {

    @Autowired
    private LiveConfig liveConfig;

    @Autowired
    private XmBehaviorKafkaProducer xmBehaviorKafkaProducer;

    @Autowired
    private CallService callService;

    private static final ExecutorService THREAD_POOL_EXECUTOR = ThreadUtils.getTtlExecutors(
            "xmLivePollingBehaviorMsgSender", 4, 8, new LinkedBlockingQueue<Runnable>(1000));

    @Override
    public ResultVO<Void> postprocessor(LivePollingPostBean param) {
        if (Objects.equal(LiveRoomStatusEnum.NORMAL.getValue(), param.getLiveRoomBean().getStatus().getValue())) {
            THREAD_POOL_EXECUTOR.submit(() -> {
                //用户行为消息发送
                xmBehaviorKafkaProducer.sendUserBehavior(new XmUserBehaviorBean()
                        .setUserId(param.getCurrentUserId())
                        .setLiveId(param.getLiveBean().getId())
                        .setIp(param.getIp())
                        .setClientVersion(param.getClientVersion())
                        .setNjId(param.getLiveRoomBean().getUserId())
                        .setAppId(param.getAppId())
                        .setDeviceId(param.getDeviceId())
                        .setUserBehaviorType(UserBehaviorType.LIVE_LISTEN.getCode())
                );
                //主播行为消息发送
                xmBehaviorKafkaProducer.sendAnchorBehavior(new XmAnchorBehaviorBean()
                        .setLiveBean(param.getLiveBean())
                        .setAnchorBehaviorType(AnchorBehaviorType.LIVE.getValue())
                        .setAppId(param.getAppId())
                        .setUserId(param.getCurrentUserId())
                        .setNjId(param.getLiveRoomBean().getUserId())
                );
                //设备行为消息发送
                xmBehaviorKafkaProducer.sendDeviceBehavior(new XmDeviceBehaviorBean()
                        .setUserId(param.getCurrentUserId())
                        .setDeviceBehaviorType(DeviceBehaviorType.IN_ROOM.getValue())
                        .setDeviceId(param.getDeviceId())
                        .setLiveRoomId(param.getLiveRoomBean().getId())
                        .setRoomType(RoomType.LIVE_ROOM.getValue())
                        .setLiveId(param.getLiveBean().getId())
                        .setAppId(param.getAppId())
                        .setNjId(param.getLiveRoomBean().getUserId())
                );
            });
        }
        return ResultVO.success();
    }

    @Override
    public BizRouterEnum getBizRouter() {
        return BizRouterEnum.XM;
    }

    @Override
    public CommonLiveConfig getLiveCommonConfig() {
        return liveConfig.getXm();
    }

    @Override
    public String getActualChannelId(long liveId, String currentChannelId) {
        Result<LiveCallProto.ResponseGetLineCode> getLineCodeResult = callService.getLineCode(liveId);
        if(getLineCodeResult.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
            //获取失败直接置null，抛弃这一次的数据
            log.warn("xm getActualChannelId error, liveId={}`currentChannelId={}`rCode={}", liveId, currentChannelId, getLineCodeResult.rCode());
            return null;
        }
        return getLineCodeResult.target().getLineCode();
    }
}
