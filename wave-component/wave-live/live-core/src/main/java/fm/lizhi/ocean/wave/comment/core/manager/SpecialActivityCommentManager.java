package fm.lizhi.ocean.wave.comment.core.manager;


import com.google.common.collect.Lists;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.commons.service.client.rcode.GeneralRCode;
import fm.lizhi.ocean.wave.comment.core.constants.LiveRoomMsgConstants;
import fm.lizhi.ocean.wave.comment.core.convert.CommentMsgConvert;
import fm.lizhi.ocean.wave.comment.core.dao.redis.CommentRedis;
import fm.lizhi.ocean.wave.comment.core.extension.comment.IGetCommentMsgProcessor;
import fm.lizhi.ocean.wave.comment.core.manager.handler.SpecialActivityCommentHandlerFactory;
import fm.lizhi.ocean.wave.comment.core.model.vo.CommentVO;
import fm.lizhi.ocean.wave.comment.core.model.vo.SpecialActivityCommentVO;
import fm.lizhi.ocean.wave.common.auto.route.common.remote.MyAutowired;
import fm.lizhi.ocean.wave.common.extension.ProcessorV2Factory;
import fm.lizhi.ocean.wave.live.core.remote.bean.LiveBean;
import fm.lizhi.ocean.wave.live.core.remote.param.GetLiveRemoteParam;
import fm.lizhi.ocean.wave.live.core.remote.result.GetLiveRemoteResult;
import fm.lizhi.ocean.wave.live.core.remote.service.ILiveServiceRemote;
import fm.lizhi.ocean.wave.server.common.util.ContextUtils;
import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import fm.lizhi.ocean.wave.comment.core.manager.handler.SpecialActivityCommentExtraHandler;
import fm.lizhi.ocean.wave.comment.core.model.dto.TransientCommentDTO;

import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class SpecialActivityCommentManager {

    @Autowired
    private SpecialActivityCommentHandlerFactory factory;

    @Autowired
    private CommentQueryManager commentQueryManager;

    @Autowired
    private ProcessorV2Factory processorV2Factory;

    @MyAutowired
    private ILiveServiceRemote liveServiceRemote;

    @Autowired
    private CommentRedis commentRedis;
    @Autowired
    private CommentPushManager commentPushManager;


    /**
     * 处理互动玩法活动评论消息
     *
     * @param liveId   节目id
     * @param baseInfo 基础消息信息
     */
    public boolean handleSpecialActivityCommentMsg(long liveId, String json, TransientCommentDTO baseInfo) {
        //1. 获取互动玩法消息处理器，如果获取不到，就说明不支持该类型的互动玩法消息
        SpecialActivityCommentExtraHandler<Object> handler = factory.getHandler(baseInfo.getCommentType(), ContextUtils.getBusinessEvnEnum().getAppId());
        if (handler == null) {
            log.info("no handler for comment type:{}", baseInfo.getCommentType());
            return true;
        }

        IGetCommentMsgProcessor processor = processorV2Factory.getProcessor(IGetCommentMsgProcessor.class);
        long userId = ContextUtils.getContext().getUserId();
        int appId = ContextUtils.getBusinessEvnEnum().getAppId();
        //查询直播信息
        LiveBean live = getLive(liveId);
        if (live == null) {
            return true;
        }
        //2. 过滤并对评论排序，由于是事件驱动的，所以userId是0，无法按toUser过滤，返回toUser，交给前端过滤
        List<TransientCommentDTO> filterComments = commentQueryManager.filterAndSortComments(Lists.newArrayList(baseInfo), userId, false);
        //3. 转换评论列表
        List<CommentVO> commentVOList = commentQueryManager.transformCommentsToLiveComment(live, filterComments, processor);
        //CommentVO转SpecialActivityCommentVO
        List<SpecialActivityCommentVO> specialActivityCommentVOList = CommentMsgConvert.I.convertCommentList(commentVOList);
        //评论是一条条推送的，所以只有一条数据
        SpecialActivityCommentVO specialActivityCommentVO = specialActivityCommentVOList.get(0);
        //4. 处理互动玩法评论扩展信息
        handler.processActivityCommentExtra(handler.convertExtraDTO(json), specialActivityCommentVO);
        //5. 处理好的数据存储到redis
        commentRedis.saveSpecialActivityCommentMsg(appId, liveId, specialActivityCommentVO);
        //6. 处理好的数据推送给前端
        int code = commentPushManager.pushSpecialActivityComment(appId, liveId, LiveRoomMsgConstants.SPECIAL_ACTIVITY_COMMENT_MSG, specialActivityCommentVO);
        //系统问题导致的失败，直接不提交offset
        return code < GeneralRCode.GENERAL_RCODE_SERVER_BUSY;
    }

    /**
     * 获取直播信息
     *
     * @param liveId 直播id
     * @return 直播信息
     */
    private LiveBean getLive(Long liveId) {
        GetLiveRemoteParam getLiveRemoteParam = new GetLiveRemoteParam();
        getLiveRemoteParam.setLiveId(liveId);
        //getLiveWithCache底层也是加了本地缓存，如果能上游控制，就不在下游控制，同时减少网络IO
        Result<GetLiveRemoteResult> liveResult = liveServiceRemote.getLiveByCache(getLiveRemoteParam);
        if (liveResult.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
            log.error("getLive error, param:{}", getLiveRemoteParam);
            return null;
        }

        return liveResult.target().getLiveBean();
    }


}
