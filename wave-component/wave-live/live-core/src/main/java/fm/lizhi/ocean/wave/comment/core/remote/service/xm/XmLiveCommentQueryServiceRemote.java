package fm.lizhi.ocean.wave.comment.core.remote.service.xm;

import com.alibaba.dubbo.common.utils.StringUtils;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.commons.service.client.rcode.GeneralRCode;
import fm.lizhi.ocean.wave.comment.core.config.CommentConfig;
import fm.lizhi.ocean.wave.comment.core.constants.CommentTypeMapping;
import fm.lizhi.ocean.wave.comment.core.constants.WaveCommentType;
import fm.lizhi.ocean.wave.comment.core.model.dto.TransientCommentDTO;
import fm.lizhi.ocean.wave.comment.core.model.dto.XmCommentExtraDTO;
import fm.lizhi.ocean.wave.comment.core.model.vo.TailEffectVo;
import fm.lizhi.ocean.wave.comment.core.remote.adapter.xm.XmCommentQueryAdapter;
import fm.lizhi.ocean.wave.comment.core.remote.bean.AtUser;
import fm.lizhi.ocean.wave.comment.core.remote.param.GetCommentWithServerTimeParam;
import fm.lizhi.ocean.wave.comment.core.remote.result.GetLiveCommentResult;
import fm.lizhi.ocean.wave.comment.core.remote.service.ILiveCommentQueryServiceRemote;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wave.server.common.util.ContextUtils;
import fm.lizhi.ocean.wave.user.api.UserService;
import fm.lizhi.ocean.wave.user.result.GetTailEffectResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import xm.fm.lizhi.datacenter.comment.pp.api.TransientCommentService;
import xm.fm.lizhi.datacenter.comment.pp.constant.CommentVisibleClientType;
import xm.fm.lizhi.datacenter.comment.pp.protocol.CommentProto;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 直播评论接口适配
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class XmLiveCommentQueryServiceRemote implements ILiveCommentQueryServiceRemote {

    @Autowired
    private TransientCommentService transientCommentService;

    @Autowired
    private UserService userService;

    @Autowired
    private CommentConfig commentConfig;


    @Override
    public boolean support(BusinessEvnEnum evnEnum) {
        return BusinessEvnEnum.XIMI.equals(evnEnum);
    }

    /**
     * 获取评论列表
     *
     * @param param 获取评论列表参数
     * @return 评论列表
     */
    @Override
    public Result<GetLiveCommentResult> getCommentWithServerTime(GetCommentWithServerTimeParam param) {
        Result<CommentProto.ResponseGetTransientComment> result = transientCommentService.getCommentWithServerTime(
                param.getLiveId(),
                param.getStartTime(), param.getEndTime(),
                param.getCount(), param.isNjFirst(),
                param.isFirstEntry());

        if (result.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
            log.error("getCommentWithServerTime failed, rCode={}`param={}", result.rCode(), param);
            return new Result<>(result.rCode(), null);
        }

        CommentProto.ResponseGetTransientComment responseGetTransientComment = result.target();
        List<CommentProto.TransientComment> transientCommentList =
                responseGetTransientComment.getTransientCommentList();
        List<TransientCommentDTO> transientComments = new ArrayList<>();
        // 过滤掉PC端不可见评论
        transientCommentList = CollectionUtils.isEmpty(transientCommentList) ? transientCommentList : transientCommentList
                .stream().filter(entity -> !(entity.getVisibleClientType() == CommentVisibleClientType.APP_CLIENT.getCode())).collect(Collectors.toList());
        for (CommentProto.TransientComment transientComment : transientCommentList) {
            if (isSpecialActivityComment(transientComment.getCommentType())) {
                continue;
            }
            //数据转换
            TransientCommentDTO transientCommentDTO = XmCommentQueryAdapter.I.convertComment(transientComment);
            //被艾特的用户
            List<AtUser> atUsers = XmCommentQueryAdapter.I.convertAtUserIds(transientComment.getAtusersList());
            List<Long> toUserIds = generateToUserIdList(transientComment.getToUserList());
            //业务评论类型转平台评论类型
            int commentType = CommentTypeMapping.getWaveCommentType(transientComment.getCommentType(),
                    BusinessEvnEnum.from(ContextUtils.getContext().getHeader().getAppId()),
                    transientComment.getCommentTypeExtension());
            //如果评论类型是系统评论，且触发系统评论的用户ID存在，则使用触发者的头像
            long userId = commentType == WaveCommentType.SYSTEM_NOTICE_COMMENT && transientComment.getOperatorUserId() > 0 ?
                    transientComment.getOperatorUserId() : transientComment.getUserId();

            // 尾灯
            TailEffectVo tailEffectVo = generateTailEffect(userId);
            XmCommentExtraDTO xmCommentExtraDTO = new XmCommentExtraDTO()
                    .setTailEffect(tailEffectVo)
                    .setAnonymous(transientComment.getAnonymous())
                    .setAtUsers(atUsers);
            transientCommentDTO.setXmCommentExtra(xmCommentExtraDTO);
            transientCommentDTO.setCommentType(commentType);
            transientCommentDTO.setUserId(userId);
            transientCommentDTO.setToUser(toUserIds);
            transientComments.add(transientCommentDTO);
        }

        GetLiveCommentResult commentResult = GetLiveCommentResult.builder()
                .transientComments(transientComments)
                .commentServerTime(Math.min(responseGetTransientComment.getCommentServerTime(), param.getEndTime()))
                .lastPage(param.getCount() > transientComments.size())
                .build();
        return new Result<>(result.rCode(), commentResult);
    }

    private List<Long> generateToUserIdList(List<CommentProto.ToUser> toUserList) {
        List<Long> toUserIds = new ArrayList<>();
        for (CommentProto.ToUser toUser : toUserList) {
            toUserIds.add(toUser.getUserId());
        }
        return toUserIds;
    }

    /**
     * 生成尾灯
     *
     * @param userId 用户ID
     * @return 尾灯信息
     */
    private TailEffectVo generateTailEffect(long userId) {
        Result<GetTailEffectResult> result = userService.getTailEffect(userId);
        if (result.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS || result.target() == null) {
            return null;
        }
        GetTailEffectResult target = result.target();
        return TailEffectVo.builder()
                .effectUrl(target.getTailEffectUrl())
                .id(target.getId())
                .build();
    }

    /**
     * 是否是互动玩法评论
     *
     * @param commentType 评论类型
     * @return true：是，false：否
     */
    private boolean isSpecialActivityComment(int commentType) {
        List<Integer> specialActivityCommentTypes = commentConfig.getXm().getSpecialActivityCommentTypes();
        if (specialActivityCommentTypes.isEmpty()) {
            return false;
        }

        return specialActivityCommentTypes.contains(commentType);
    }

}
