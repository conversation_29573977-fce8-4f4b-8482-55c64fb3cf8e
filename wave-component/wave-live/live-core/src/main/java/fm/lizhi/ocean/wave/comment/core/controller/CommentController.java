package fm.lizhi.ocean.wave.comment.core.controller;

import fm.lizhi.ocean.wave.comment.core.constants.CommentMsgCodes;
import fm.lizhi.ocean.wave.comment.core.constants.LiveConstants;
import fm.lizhi.ocean.wave.comment.core.constants.WaveCommentType;
import fm.lizhi.ocean.wave.comment.core.manager.CommentFilterManager;
import fm.lizhi.ocean.wave.comment.core.manager.CommentManager;
import fm.lizhi.ocean.wave.comment.core.manager.CommentQueryManager;
import fm.lizhi.ocean.wave.comment.core.manager.EmotionManager;
import fm.lizhi.ocean.wave.comment.core.model.param.*;
import fm.lizhi.ocean.wave.comment.core.model.result.*;
import fm.lizhi.ocean.wave.comment.core.model.vo.EmotionVO;
import fm.lizhi.ocean.wave.comment.core.model.vo.FilterSettingVo;
import fm.lizhi.ocean.wave.comment.core.model.vo.GroupEmotionVO;
import fm.lizhi.ocean.wave.comment.core.remote.bean.CommentStyle;
import fm.lizhi.ocean.wave.common.util.UrlUtils;
import fm.lizhi.ocean.wave.server.common.auth.annotation.VerifyUserToken;
import fm.lizhi.ocean.wave.server.common.constant.MsgCodes;
import fm.lizhi.ocean.wave.server.common.util.ContextUtils;
import fm.lizhi.ocean.wave.server.common.vo.ResultVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 直播评论模块
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/comment")
public class CommentController {

    @Autowired
    private CommentManager commentManager;
    @Autowired
    private CommentFilterManager commentFilterManager;
    @Autowired
    private EmotionManager emotionManager;
    @Autowired
    private CommentQueryManager commentQueryManager;

    /**
     * 获取自定义表情包
     * @return
     */
    @VerifyUserToken
    @GetMapping("/emotion/diyList")
    public ResultVO<GroupEmotionVO> diyEmotionList(@RequestParam("type") Integer type
            , @RequestParam("pageNo") int pageNo
            , @RequestParam("pageSize") int pageSize){
        if (type == null) {
            type = LiveConstants.EmotionSource.LIVE;
        }
        return emotionManager.getDiyEmotionList(type, pageNo, pageSize);
    }

    /**
     * 删除自定义表情包
     * @param emotionId
     * @return
     */
    @VerifyUserToken
    @PostMapping("/emotion/delDiyEmotion/{emotionId}")
    public ResultVO<Void> delDiyEmotion(@PathVariable long emotionId){
        return emotionManager.delDiyEmotion(emotionId);
    }

    /**
     * 添加自定义表情包
     * @return
     */
    @VerifyUserToken
    @PostMapping("/emotion/addDiyEmotion")
    public ResultVO<Void> addDiyEmotion(@Validated @RequestBody AddDiyEmotionParam param){
        // 去掉图片的域名, 只有旧版本调用
        String remoteUrlHost = UrlUtils.remoteUrlHost(param.getEmotionUrl());
        return emotionManager.addDiyEmotion(param.setEmotionUrl(remoteUrlHost));
    }

    /**
     * 添加自定义表情包-批量
     * @return
     */
    @VerifyUserToken
    @PostMapping("/emotion/batchAddDiyEmotion")
    public ResultVO<Void> batchAddDiyEmotion(@Validated @RequestBody BatchAddDiyEmotionParam param){
        return emotionManager.batchAddDiyEmotion(param);
    }

    /**
     * 收藏自定义表情包
     * @param emotionId
     * @return
     */
    @VerifyUserToken
    @PostMapping("/emotion/collectDiyEmotion/{emotionId}")
    public ResultVO<Void> collectDiyEmotion(@PathVariable long emotionId){
        return emotionManager.collectDiyEmotion(emotionId);
    }

    /**
     * 自定义表情包排序
     * @param param
     * @return
     */
    @VerifyUserToken
    @PostMapping("/emotion/sortDiyEmotion")
    public ResultVO<Void> sortDiyEmotion(@Validated @RequestBody SortDiyEmotionParam param){
        return emotionManager.sortCollectDiyEmotion(param);
    }

    /**
     * 获取表情列表
     *
     * @param emotionVersion 表情包版本
     * @parma type 1=直播间，2=IM
     * @parma loadDiyEmotion 是否加载自定义表情包分组
     * @return 结果
     * @parma type 1=直播间，2=IM
     */
    @VerifyUserToken
    @GetMapping("/emotion/list")
    public ResultVO<EmotionResult> emotionList(@RequestParam int emotionVersion
            , @RequestParam(required = false) Integer type
            , @RequestParam(required = false) Integer loadDiyEmotion) {
        if (type == null) {
            type = LiveConstants.EmotionSource.LIVE;
        }
        if (loadDiyEmotion == null) {
            // 兼容旧版本，新版自定义表情包使用新接口查询
            loadDiyEmotion = LiveConstants.LoadDiyEmotion.YES;
        }

        EmotionResult emotionResult = commentManager.getEmotionResult(emotionVersion, type, loadDiyEmotion);
        if (emotionResult == null) {
            return ResultVO.failure("获取表情异常");
        }

        return ResultVO.success(emotionResult);
    }

    /**
     * 保存用户评论过滤设置
     *
     * @param param 过滤设置
     * @return 结果
     */
    @VerifyUserToken
    @PostMapping("/user/filter/setting")
    public ResultVO<Void> saveUserFilterSetting(@RequestBody SaveUserCommentFilterSettingParam param) {
        long userId = ContextUtils.getContext().getUserId();
        Integer appId = ContextUtils.getContext().getHeader().getAppId();
        List<CommentFilterSettingVo> settingList = param.getSettingList();

        Pair<Boolean, String> resultPair = commentFilterManager.saveUserFilterSetting(userId, appId, settingList);
        if (!resultPair.getLeft()) {
            return ResultVO.failure(resultPair.getRight());
        }

        return ResultVO.success();
    }

    /**
     * 获取用户评论过滤设置
     *
     * @return 结果
     */
    @VerifyUserToken
    @GetMapping("/user/filter/setting")
    public ResultVO<List<GetUserFilterSettingResult>> getUserFilterSetting() {
        long userId = ContextUtils.getContext().getUserId();
        Integer appId = ContextUtils.getContext().getHeader().getAppId();
        List<FilterSettingVo> userFilterSettingVo =
                commentFilterManager.getUserFilterSettingVo(userId, appId);

        Map<Integer, List<FilterSettingVo>> userFilterSettingMap = userFilterSettingVo.stream()
                .collect(Collectors.groupingBy(FilterSettingVo::getMessageType, Collectors.toList()));

        List<GetUserFilterSettingResult> result = new ArrayList<>();
        userFilterSettingMap.forEach((messageType, filterSettingVos) -> {
            GetUserFilterSettingResult enterResult = new GetUserFilterSettingResult();
            enterResult.setMessageType(messageType);
            enterResult.setList(filterSettingVos);
            result.add(enterResult);
        });

        return ResultVO.success(result);
    }

    /**
     * 获取评论id
     *
     * @return 结果
     */
    @VerifyUserToken
    @PostMapping("/getCommentId")
    public ResultVO<String> getCommentId() {
        return ResultVO.success(commentManager.generateCommentId() + "");
    }

    /**
     * 批量生成评论ID
     * @return
     */
    @VerifyUserToken
    @PostMapping("/batchGetCommentId")
    public ResultVO<List<String>> batchGetCommentId() {
        return ResultVO.success(commentManager.batchGenerateCommentId());
    }

    /**
     * 发送文字评论
     *
     * @param param 参数
     * @return 结果
     */
    @VerifyUserToken
    @PostMapping("/sendWordComment")
    public ResultVO<String> sendWordComment(@RequestBody SendWordCommentParam param) {
        //判断是否是有效的评论类型，目前只支持普通评论和欢迎按钮发起的评论
        boolean isIllegalType = param.getCommentType() != WaveCommentType.GENERAL_COMMENT
                && param.getCommentType() != WaveCommentType.WELCOME_BUTTON
                && param.getCommentType() != WaveCommentType.EMOTION_COMMENT
                && param.getCommentType() != WaveCommentType.INTERACTIVE_MOTION
                && param.getCommentType() != WaveCommentType.DIY_EMOTION
                && param.getCommentType() != WaveCommentType.SHARE_PARAM_COMMENT;
        if (isIllegalType) {
            return ResultVO.failure("参数错误");
        }

        //只有欢迎评论和表情允许空content，其他的暂时不行
        boolean isEmptyContext = param.getCommentType() != WaveCommentType.WELCOME_BUTTON
                && param.getCommentType() != WaveCommentType.EMOTION_COMMENT
                && param.getCommentType() != WaveCommentType.DIY_EMOTION
                && param.getCommentType() != WaveCommentType.INTERACTIVE_MOTION
                && (StringUtils.isBlank(param.getContent()));
        if (isEmptyContext) {
            return ResultVO.failure("参数错误");
        }

        //表情包评论表情包ID不能为空
        boolean isNullEmotionId = (param.getCommentType() == WaveCommentType.EMOTION_COMMENT
                || param.getCommentType() == WaveCommentType.DIY_EMOTION
                || param.getCommentType() == WaveCommentType.INTERACTIVE_MOTION)
                && (param.getEmotionId() == null);
        if (isNullEmotionId) {
            return ResultVO.failure("参数错误");
        }

        // 自定义表情包需要检查状态
        if (param.getCommentType() == WaveCommentType.DIY_EMOTION) {
            Optional<EmotionVO> op = emotionManager.getCollectDiyEmotion(param.getEmotionId());
            if (!op.isPresent()) {
                return ResultVO.failure(CommentMsgCodes.EMOTION_NOT_EXIST);
            }
            if (op.get().getReview() == null || !op.get().getReview()) {
                return ResultVO.failure(CommentMsgCodes.ADD_EMOTION_AUTH_FAIL);
            }
        }

        if (param.getLiveId() == null || param.getLiveId() <= 0) {
            return ResultVO.failure("参数错误");
        }
        if (param.getCommentId() == null
                || param.getCommentId() <= 0) {
            return ResultVO.failure("参数错误,评论id不能为空");
        }
        Integer appId = ContextUtils.getContext().getHeader().getAppId();
        // 发送评论
        Pair<Integer, String> result = commentManager.sendComment(appId, param);

        // 0表示成功
        if (result.getLeft() == 0) {
            return ResultVO.success(result.getRight());
        }

        return ResultVO.failure(result.getLeft(), result.getRight());
    }

    /**
     * 发送图片评论
     *
     * @param param 参数
     * @return 结果
     */
    @VerifyUserToken
    @PostMapping("/sendPicComment")
    public ResultVO<String> sendPicComment(@RequestBody SendPicCommentParam param) {
        if (param.getLiveId() == null
                || param.getLiveId() <= 0
                || StringUtils.isBlank(param.getImageUrl())
                || param.getCommentId() == null
                || param.getCommentId() <= 0) {
            return ResultVO.failure("参数错误");
        }
        Integer appId = ContextUtils.getContext().getHeader().getAppId();
        // 发送图片评论
        Pair<Integer, String> result =
                commentManager.sendImageComment(appId, param);
        if (result.getLeft() == 0) {
            return ResultVO.success(result.getRight());
        }

        return ResultVO.failure(result.getLeft(), result.getRight());
    }

    /**
     * 发送拍一拍评论
     *
     * @param param 参数
     * @return 结果
     */
    @VerifyUserToken
    @PostMapping("/sendPatComment")
    public ResultVO<String> sendPatWordComment(@RequestBody SendPatCommentParam param) {
        //判断是否是有效的评论类型，目前只支持拍一拍
        boolean isIllegalType = param.getCommentType() != WaveCommentType.RELATION_PAT_COMMENT;
        if (isIllegalType) {
            return ResultVO.failure("参数错误");
        }
        if (param.getLiveId() == null || param.getLiveId() <= 0) {
            return ResultVO.failure("参数错误");
        }
        if (param.getCommentId() == null
                || param.getCommentId() <= 0) {
            return ResultVO.failure("参数错误,评论id不能为空");
        }

        Integer appId = ContextUtils.getContext().getHeader().getAppId();
        Pair<Integer, String> result = commentManager.sendPatComment(appId, param);
        if (result.getLeft() == 0) {
            return ResultVO.success(result.getRight());
        }
        return ResultVO.failure(result.getLeft(), result.getRight());
    }


    /**
     * 获取最新评论
     *
     * @param param 参数
     * @return 结果
     */
    @VerifyUserToken
    @GetMapping("/getLatestComment")
    @Deprecated
    public ResultVO<LatestCommentsResult> getLatestComments(GetLatestCommentsParam param) {
        if (param.getLiveId() == null || param.getLiveId() <= 0) {
            return ResultVO.failure("参数错误");
        }

        if (StringUtils.isNotBlank(param.getPerformanceId())
                && !NumberUtils.isNumber(param.getPerformanceId())){
            return ResultVO.failure(MsgCodes.PARAM_ERROR);
        }

        ResultVO<LatestCommentsResult> latestCommentsResult = commentManager.latestComments(param.getLiveId(), param.getPerformanceId());
        if (!latestCommentsResult.isOK()) {
            return latestCommentsResult;
        }
        return ResultVO.success(latestCommentsResult.getData());
    }

    /**
     * 获取过滤后的最新评论
     *
     * @param param 参数
     * @return 结果
     */
    @VerifyUserToken
    @GetMapping("/getFilterLatestComment")
    @Deprecated
    public ResultVO<LatestCommentsResult> getFilterLatestComment(GetLatestCommentsParam param) {
        if (param.getLiveId() == null || param.getLiveId() <= 0) {
            return ResultVO.failure("参数错误");
        }

        ResultVO<LatestCommentsResult> latestCommentsResult = commentManager.latestFilterComments(param.getLiveId(), param.getPerformanceId());
        if (!latestCommentsResult.isOK()) {
            return latestCommentsResult;
        }

        return ResultVO.success(latestCommentsResult.getData());
    }

    /**
     * 同步评论状态
     *
     * @param param 参数
     * @return 结果
     */
    @VerifyUserToken
    @PostMapping("/syncComments")
    public ResultVO<List<SyncCommentResult>> syncCommentList(@RequestBody SyncCommentParam param) {
        if (param.getLiveId() == null || param.getLiveId() <= 0
                || param.getCommentIds() == null || param.getCommentIds().size() <= 0) {
            return ResultVO.failure("参数错误");
        }

        //修复[null]的情况
        List<Long> filterList = param.getCommentIds().stream().filter(Objects::nonNull).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(filterList)) {
            return ResultVO.failure("参数错误");
        }
        param.setCommentIds(filterList);

        Integer appId = ContextUtils.getContext().getHeader().getAppId();
        return commentManager.syncComments(appId, param.getLiveId(), param.getCommentIds());
    }


    /**
     * 获取气泡列表
     * @return
     */
    @GetMapping("/styleList")
    @VerifyUserToken
    public ResultVO<List<CommentStyle>> getCommentStyleList(){
        return commentManager.getCommentStyleList();
    }


    /**
     * 获取最新评论
     *
     * @param param 参数
     * @return 结果
     */
    @VerifyUserToken
    @GetMapping("/getLatestCommentV2")
    public ResultVO<LatestCommentsV2Result> getLatestCommentsV2(GetLatestCommentsParam param) {
        if (param.getLiveId() == null || param.getLiveId() <= 0) {
            return ResultVO.failure("参数错误");
        }

        if (StringUtils.isNotBlank(param.getPerformanceId())
                && !NumberUtils.isNumber(param.getPerformanceId())){
            return ResultVO.failure(MsgCodes.PARAM_ERROR);
        }

        ResultVO<LatestCommentsV2Result> latestCommentsResult = commentQueryManager.latestComments(param.getLiveId(), param.getPerformanceId());
        if (!latestCommentsResult.isOK()) {
            return latestCommentsResult;
        }
        return ResultVO.success(latestCommentsResult.getData());
    }

    /**
     * 获取过滤后的最新评论
     *
     * @param param 参数
     * @return 结果
     */
    @VerifyUserToken
    @GetMapping("/getFilterLatestCommentV2")
    public ResultVO<LatestCommentsV2Result> getFilterLatestCommentV2(GetLatestCommentsParam param) {
        if (param.getLiveId() == null || param.getLiveId() <= 0) {
            return ResultVO.failure("参数错误");
        }

        ResultVO<LatestCommentsV2Result> latestCommentsResult = commentQueryManager.latestFilterComments(param.getLiveId(), param.getPerformanceId());
        if (!latestCommentsResult.isOK()) {
            return latestCommentsResult;
        }

        return ResultVO.success(latestCommentsResult.getData());
    }

    /**
     * 获取过滤后的最新评论
     *
     * @param param 参数
     * @return 结果
     */
    @VerifyUserToken
    @GetMapping("/getLatestSpecialComment")
    public ResultVO<LatestCommentsV2Result> getLatestSpecialComment(GetLatestCommentsParam param) {
        if (param.getLiveId() == null || param.getLiveId() <= 0) {
            return ResultVO.failure("参数错误");
        }

        ResultVO<LatestCommentsV2Result> latestCommentsResult = commentQueryManager.getSpecialActivityComments(param.getLiveId(), param.getPerformanceId());
        if (!latestCommentsResult.isOK()) {
            return latestCommentsResult;
        }

        return ResultVO.success(latestCommentsResult.getData());
    }

}
