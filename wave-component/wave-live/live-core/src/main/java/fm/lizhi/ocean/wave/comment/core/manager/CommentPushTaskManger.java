package fm.lizhi.ocean.wave.comment.core.manager;

import fm.lizhi.commons.service.client.rcode.GeneralRCode;
import fm.lizhi.ocean.wave.comment.core.config.CommentConfig;
import fm.lizhi.ocean.wave.comment.core.constants.LiveRoomMsgConstants;
import fm.lizhi.ocean.wave.comment.core.dao.redis.CommentRateLimitRedisDao;
import fm.lizhi.ocean.wave.common.util.RedisLock;
import fm.lizhi.ocean.wave.common.util.ThreadUtils;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.skywalking.apm.toolkit.trace.RunnableWrapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.List;
import java.util.concurrent.*;

/**
 * 评论限流管理类
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class CommentPushTaskManger {

    @Autowired
    private CommentRateLimitRedisDao commentRateLimitRedisDao;

    @Autowired
    private CommentConfig commentConfig;

    @Autowired
    private CommentPushManager commentPushManager;

    /**
     * 线程池
     */
    private static final ExecutorService THREAD_POOL_EXECUTOR = ThreadUtils.getTtlExecutors("ROOM_MSG_TIMEOUT_PUSH", 3, 6, new LinkedBlockingQueue<Runnable>(10000));

    @PostConstruct
    public void init() {
        ScheduledExecutorService executorService = Executors.newSingleThreadScheduledExecutor();
        executorService.scheduleAtFixedRate(this::executeScanAndPushTimeOutLiveId, 0, commentConfig.getRoomMsgTimeoutScanMillSec(), TimeUnit.MILLISECONDS);
    }


    /**
     * 扫描超时评论直播间
     */
    public void executeScanAndPushTimeOutLiveId() {
        for (BusinessEvnEnum businessEvnEnum : BusinessEvnEnum.values()) {
            if (businessEvnEnum.getOnline() != 1) {
                continue;
            }
            pushTimeOutLiveIds(businessEvnEnum.getAppId(), LiveRoomMsgConstants.COMMENT_MSG);
        }
    }

    /**
     * 记录直播房间评论时间
     *
     * @param appId   应用ID
     * @param liveId  直播节目ID
     * @param msgType 消息类型
     * @param time    时间
     * @return 结果
     */
    public boolean saveLiveCommentInfo(int appId, long liveId, String msgType, long time) {
        try (RedisLock lock = commentRateLimitRedisDao.getRoomMsgPushLock(appId, liveId, msgType)) {
            if (!lock.lock()) {
                //获取不到锁，结束
                return false;
            }
            //更新最新的直播间最新评论时间
            boolean res = commentRateLimitRedisDao.saveLiveRoomLatestCommentTime(appId, liveId, msgType, time);
            if (!res) {
                log.error("saveLiveRoomLatestCommentTime error: appId={}, liveId={}, msgType={}", appId, liveId, msgType);
                return false;
            }
            //先查询是否存在，不存在则添加
            boolean exist = commentRateLimitRedisDao.existLiveCommentInfo(appId, liveId, msgType);
            if (!exist) {
                boolean saveRes = commentRateLimitRedisDao.saveLiveCommentInfo(appId, liveId, msgType, time);
                if (!saveRes) {
                    log.error("saveLiveCommentInfo error: appId={}, liveId={}, msgType={}", appId, liveId, msgType);
                }
                return saveRes;
            }
        } catch (Exception e) {
            log.error("saveLiveCommentInfo error: appId={}, liveId={}, msgType={},e:", appId, liveId, msgType, e);
            return false;
        }
        return true;
    }

    /**
     * 推送超时评论直播间
     * 会有多个节点抢这个定时任务，我们按appId和msgType进行加锁
     *
     * @param appId   应用ID
     * @param msgType 消息类型
     */
    public void pushTimeOutLiveIds(int appId, String msgType) {
        try (RedisLock lock = commentRateLimitRedisDao.getRoomMsgOutTimeTaskLock(appId, msgType)) {
            if (!lock.tryLock()) {
                //获取不到锁，不等待了，结束
                return;
            }
            List<String> timeOutLiveIds = commentRateLimitRedisDao.getTimeOutLiveIds(appId, msgType, commentConfig.getRoomMsgTimeoutPushMillSec());
            if (CollectionUtils.isEmpty(timeOutLiveIds)) {
                return;
            }

            for (String liveId : timeOutLiveIds) {
                THREAD_POOL_EXECUTOR.execute(RunnableWrapper.of(() -> executePushCore(appId, msgType, liveId)));
            }
        } catch (Exception e) {
            log.error("pushTimeOutLiveIds happen error: appId={}, msgType={}", appId, msgType, e);
        }
    }

    /**
     * 执行推送的和兴方法
     *
     * @param appId   应用旧的
     * @param msgType 消息类型
     * @param liveId  直播节目ID
     */
    private void executePushCore(int appId, String msgType, String liveId) {
        //加个liveId级别的锁，避免在推送时删除时，不断累加又重复推
        try (RedisLock lock = commentRateLimitRedisDao.getRoomMsgPushLock(appId, Long.parseLong(liveId), msgType)) {
            if (!lock.lock()) {
                //获取不到锁，结束
                return;
            }
            Long latestCommentTime = commentRateLimitRedisDao.getLiveRoomLatestCommentTime(appId, msgType, Long.parseLong(liveId));
            log.debug("executePushCore appId:{},msgType:{},liveId:{},latestCommentTime:{}", appId, msgType, liveId, latestCommentTime);
            if (latestCommentTime <= 0) {
                return;
            }
            int code = commentPushManager.pushCommentMsgChange(Long.parseLong(liveId), appId, msgType, latestCommentTime);
            //删除直播间liveId
            if (code < GeneralRCode.GENERAL_RCODE_SERVER_BUSY) {
                commentRateLimitRedisDao.removeLiveRoomLatestCommentTime(appId, msgType, liveId);
                commentRateLimitRedisDao.removeLiveId(appId, msgType, liveId);
            }
        } catch (Exception e) {
            log.error("executePushCore.executePushCore error: appId={}, msgType={}, liveId={}, e:", appId, msgType, liveId, e);
        }
    }

}
