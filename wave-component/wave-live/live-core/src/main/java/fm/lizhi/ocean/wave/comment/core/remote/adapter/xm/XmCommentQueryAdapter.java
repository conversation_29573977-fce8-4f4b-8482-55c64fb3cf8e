package fm.lizhi.ocean.wave.comment.core.remote.adapter.xm;

import fm.lizhi.ocean.wave.comment.core.model.dto.TransientCommentDTO;
import fm.lizhi.ocean.wave.comment.core.model.dto.WealthLevelDTO;
import fm.lizhi.ocean.wave.comment.core.remote.bean.AtUser;
import fm.lizhi.ocean.wave.comment.core.remote.bean.FreshUser;
import fm.lizhi.ocean.wave.comment.core.remote.bean.Medal;
import fm.lizhi.ocean.wave.comment.core.remote.bean.SimpleUser;
import org.apache.commons.collections4.CollectionUtils;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;
import xm.fm.lizhi.datacenter.comment.pp.protocol.CommentProto;

import java.util.ArrayList;
import java.util.List;

/**
 * PP评论查询适配器
 *
 * <AUTHOR>
 */
@Mapper(collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        /* 未映射目标属性的报告策略 */
        unmappedTargetPolicy = ReportingPolicy.ERROR,
        /* 确定何时对bean映射的源属性值包含空检查 */
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface XmCommentQueryAdapter {

    XmCommentQueryAdapter I = Mappers.getMapper(XmCommentQueryAdapter.class);

    /**
     * 转换评论
     *
     * @param transientComment rpc相应体
     * @return 评论中间数据
     */
    @Mapping(target = "ppCommentExtra", ignore = true)
    @Mapping(target = "xmCommentExtra", ignore = true)
    @Mapping(target = "hyCommentExtra", ignore = true)
    @Mapping(target = "toUser", ignore = true)
    @Mapping(target = "aspectRatio", ignore = true)
    @Mapping(target = "simpleUser", expression = "java(convertSimpleUser(transientComment.getSimpleUser()))")
    @Mapping(target = "wealthLevel", expression = "java(convertWealthLevel(transientComment.getPpWealthLevel()))")
    @Mapping(target = "freshUser", expression = "java(convertFreshUser(transientComment.getFreshUser()))")
    @Mapping(target = "medal", expression = "java(convertMedals(transientComment.getPpMedalList(), transientComment.getMedalWallMedalList()))")
    TransientCommentDTO convertComment(CommentProto.TransientComment transientComment);


    /**
     * 转换为财富等级
     *
     * @param wealthLevel 财富等级
     * @return 结果
     */
    WealthLevelDTO convertWealthLevel(CommentProto.PpWealthLevel wealthLevel);

    /**
     * 转换为简单用户
     *
     * @param simpleUser 简单用户
     * @return 结果
     */
    @Mapping(target = "nameColorsList", source = "userNameColorsList")
    @Mapping(target = "roomVipUrls", source = "roomVipUrlsList")
    SimpleUser convertSimpleUser(CommentProto.SimpleUser simpleUser);

    /**
     * 转换为新用户
     *
     * @param freshUser 新用户
     * @return 结果
     */
    @Mapping(target = "aspect", ignore = true)
    @Mapping(target = "url", ignore = true)
    FreshUser convertFreshUser(CommentProto.FreshUser freshUser);

    /**
     * 转换勋章
     *
     * @param ppMedalList        勋章列表
     * @param medalWallMedalList 佩戴的勋章列表
     * @return 结果
     */
    default List<Medal> convertMedals(List<CommentProto.PpMedal> ppMedalList, List<CommentProto.SimpleMedal> medalWallMedalList) {
        List<Medal> medalBeans = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(ppMedalList)) {
            for (CommentProto.PpMedal ppMedal : ppMedalList) {
                Medal medalBean = new Medal();
                medalBean.setCover(ppMedal.getCover());
                medalBean.setAspect(ppMedal.getAspect());
                medalBeans.add(medalBean);
            }
        }

        if (CollectionUtils.isNotEmpty(medalWallMedalList)) {
            for (CommentProto.SimpleMedal simpleMedal : medalWallMedalList) {
                Medal medalBean = new Medal();
                medalBean.setCover(simpleMedal.getImage());
                medalBean.setAspect(simpleMedal.getAspect());
                medalBeans.add(medalBean);
            }
        }

        return medalBeans;
    }

    /**
     * 转换为at用户列表
     *
     * @param atusersList at用户列表
     * @return 转换结果
     */
    List<AtUser> convertAtUserIds(List<CommentProto.AtUser> atusersList);

}
