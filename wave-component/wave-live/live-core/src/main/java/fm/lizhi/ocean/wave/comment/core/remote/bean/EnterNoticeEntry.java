package fm.lizhi.ocean.wave.comment.core.remote.bean;

import fm.lizhi.ocean.wave.comment.core.model.dto.WealthLevelDTO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 *
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Deprecated
public class EnterNoticeEntry {
    /**
     * 用户Id
     */
    private Long userId;
    /**
     * 进入时间
     */
    private Long timeStamp;
    /**
     * 是否匿名进房 1是 0否
     */
    private Integer anonymous;
    /**
     * 文本内容
     */
    private String notice;
    /**
     * 位数，匿名进房时的合并数量
     */
    private Integer count;
    /**
     * 财富等级信息 (匿名时，忽略该值)
     */
    private WealthLevelDTO wealthInfo;
    /**
     * 用户座驾信息
     */
    private UserMountEntry userMountEntry;
    /**
     * 头像
     */
    private String userCover;
    /**
     * 贵族信息
     */
    private VipInfoEntry vipInfoEntry;
    /**
     * 关系进房特效JSON，VO参考SvgaEffectVo
     */
    private String relationEffectJson;
    /**
     * 新用户信息
     */
    private FreshUser freshUserEntry;
    /**
     * 是否显示欢迎按钮
     */
    private Boolean isShowButton;
    /**
     * 密友关系进房特效JSON，VO参考SvgaEffectVo
     */
    private String closeFriendEnterRoomEffectJson;

    /**
     * 进房标记,0:正常数据，1：标记数据
     */
    private int enterMark;

    /**    ximi独有
     *     MONTH_ROOM_VIP(1, "月贵宾"),
     *     YEAR_ROOM_VIP(2, "年贵宾"),
     */
    private Integer userRoomVipStatus;


    /**
     * 进房来源
     */
    private Integer comeSource;

    /**
     * 进房来源目标用户ID
     */
    private Long comeSourceTgtUid;

    /**
     * 是否 显示新人欢迎横幅
     */
    private boolean showFreshUserWelcome;

}
