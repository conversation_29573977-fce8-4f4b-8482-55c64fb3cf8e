package fm.lizhi.ocean.wave.comment.core.manager;

import cn.hutool.core.util.NumberUtil;
import com.alibaba.fastjson.JSONObject;
import fm.lizhi.commons.util.GuidGenerator;
import fm.lizhi.ocean.wave.comment.core.constants.CommentFilterType;
import fm.lizhi.ocean.wave.comment.core.filtersetting.WaveCommentFilterConfigDao;
import fm.lizhi.ocean.wave.comment.core.filtersetting.WaveUserCommentFilterSettingDao;
import fm.lizhi.ocean.wave.comment.core.filtersetting.entity.WaveCommentFilterConfig;
import fm.lizhi.ocean.wave.comment.core.filtersetting.entity.WaveUserCommentFilterSetting;
import fm.lizhi.ocean.wave.comment.core.manager.filter.EnterNoticeCompositeV2Filter;
import fm.lizhi.ocean.wave.comment.core.manager.filter.EnterNoticeFansV2Filter;
import fm.lizhi.ocean.wave.comment.core.manager.filter.EnterNoticeWealthLevelV2Filter;
import fm.lizhi.ocean.wave.comment.core.model.dto.EnterNoticeDTO;
import fm.lizhi.ocean.wave.comment.core.model.dto.TransientCommentDTO;
import fm.lizhi.ocean.wave.comment.core.model.param.CommentFilterSettingVo;
import fm.lizhi.ocean.wave.comment.core.model.vo.FilterSettingVo;
import fm.lizhi.ocean.wave.comment.core.remote.bean.EnterNoticeEntry;
import fm.lizhi.ocean.wave.comment.core.remote.service.IRelationShipServiceRemote;
import fm.lizhi.ocean.wave.common.auto.route.common.remote.MyAutowired;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;

import static fm.lizhi.ocean.wave.comment.core.constants.CommentFilterType.ENTER_NOTICE_FANS;
import static fm.lizhi.ocean.wave.comment.core.constants.CommentFilterType.ENTER_NOTICE_WEALTH_LEVEL;

/**
 * Description:
 *
 * <AUTHOR>
 * Created in  2023/5/18
 */
@Slf4j
@Component
public class CommentFilterManager {

    @Autowired
    private GuidGenerator guidGenerator;
    @Autowired
    private WaveUserCommentFilterSettingDao waveUserCommentFilterSettingDao;
    @Autowired
    private WaveCommentFilterConfigDao waveCommentFilterConfigDao;

    @MyAutowired
    private IRelationShipServiceRemote relationShipServiceRemote;

    /**
     * 保存用户最新的评论过滤配置
     *
     * @param userId      用户Id
     * @param appId       应用Id
     * @param settingList 过滤配置
     */
    public Pair<Boolean, String> saveUserFilterSetting(long userId, int appId, List<CommentFilterSettingVo> settingList) {
        Date now = new Date();
        List<WaveUserCommentFilterSetting> filterSettings = new ArrayList<>(settingList.size());
        for (CommentFilterSettingVo settingVo : settingList) {
            WaveCommentFilterConfig setting = waveCommentFilterConfigDao.getSetting(settingVo.getSettingId());
            if (setting == null) {
                return Pair.of(false, "过滤配置不存在");
            }

            if (settingVo.getValue() != null) {
                boolean isInteger = NumberUtils.isCreatable(settingVo.getValue()) && settingVo.getValue().trim().matches("-?\\d+");
                if (!isInteger) {
                    log.info("数值只能是整数, settingVo={}", JSONObject.toJSONString(settingVo));
                    return Pair.of(false, "数值只能是整数");
                }
            }

            if (setting.getType() == ENTER_NOTICE_WEALTH_LEVEL.getValue()
                    && settingVo.getStatus()
                    && settingVo.getValue() == null) {
                return Pair.of(false, "财富等级一定要输入指定值");
            }

            WaveUserCommentFilterSetting waveUserCommentFilterSetting = new WaveUserCommentFilterSetting();
            waveUserCommentFilterSetting.setId(guidGenerator.genId());
            waveUserCommentFilterSetting.setSettingId(settingVo.getSettingId());
            waveUserCommentFilterSetting.setUserId(userId);
            waveUserCommentFilterSetting.setAppId(appId);
            waveUserCommentFilterSetting.setValue(settingVo.getValue() == null ? "" : settingVo.getValue());
            waveUserCommentFilterSetting.setMsgType(setting.getMsgType());
            waveUserCommentFilterSetting.setType(setting.getType());
            waveUserCommentFilterSetting.setStatus(settingVo.getStatus());
            waveUserCommentFilterSetting.setCreateTime(now);
            waveUserCommentFilterSetting.setModifyTime(now);
            filterSettings.add(waveUserCommentFilterSetting);
        }
        waveUserCommentFilterSettingDao.saveUserFilterSetting(userId, appId, filterSettings);
        return Pair.of(true, "保存成功");
    }

    /**
     * 获取用户最新的评论过滤配置
     *
     * @param userId 用户Id
     * @param appId  应用Id
     * @return 过滤配置
     */
    public List<FilterSettingVo> getUserFilterSettingVo(long userId, int appId) {
        List<WaveCommentFilterConfig> settingList = waveCommentFilterConfigDao.getSettingList(appId);
        List<WaveUserCommentFilterSetting> userFilterSetting = waveUserCommentFilterSettingDao.getUserFilterSetting(userId, appId);
        Map<Long, WaveUserCommentFilterSetting> settingId2FilterSettingMap = new HashMap<>(userFilterSetting.size());
        for (WaveUserCommentFilterSetting filterSetting : userFilterSetting) {
            settingId2FilterSettingMap.put(filterSetting.getSettingId(), filterSetting);
        }
        List<FilterSettingVo> filterSettingVos = new ArrayList<>(settingList.size());
        for (WaveCommentFilterConfig filterConfig : settingList) {
            Long id = filterConfig.getId();
            FilterSettingVo filterSettingVo = new FilterSettingVo();
            filterSettingVo.setSettingId(String.valueOf(filterConfig.getId()));
            filterSettingVo.setName(filterConfig.getName());
            filterSettingVo.setMessageType(filterConfig.getMsgType());
            WaveUserCommentFilterSetting filterSetting = settingId2FilterSettingMap.get(id);
            if (filterSetting != null) {
                filterSettingVo.setValue(filterSetting.getValue());
                filterSettingVo.setStatus(filterSetting.getStatus());
            } else {
                filterSettingVo.setStatus(false);
            }

            CommentFilterType commentFilterType = CommentFilterType.from(filterConfig.getType());
            if (commentFilterType == null) {
                continue;
            }

            if (commentFilterType.equals(ENTER_NOTICE_FANS)) {
                filterSettingVo.setInputType(0);
            }

            if (commentFilterType.equals(ENTER_NOTICE_WEALTH_LEVEL)) {
                filterSettingVo.setInputType(1);
            }

            filterSettingVos.add(filterSettingVo);
        }
        return filterSettingVos;
    }

    /**
     * 根据用户设置过滤评论
     *
     * @param userId             用户Id
     * @param appId              应用Id
     * @param enterNoticeEntries 原始进场消息列表
     * @return 过滤后的进场消息列表
     */
    public List<EnterNoticeEntry> filterEnterNoticeByUserSetting(long userId, int appId,
                                                                 List<EnterNoticeEntry> enterNoticeEntries) {
        //空列表就不要浪费资源再查一遍了
        if (CollectionUtils.isEmpty(enterNoticeEntries)) {
            return enterNoticeEntries;
        }

        // 获取用户的评论过滤配置
        List<WaveUserCommentFilterSetting> userFilterSetting =
                waveUserCommentFilterSettingDao.getUserFilterSetting(userId, appId);

        EnterNoticeCompositeFilter enterNoticeCompositeFilter = new EnterNoticeCompositeFilter();

        for (WaveUserCommentFilterSetting filterSetting : userFilterSetting) {

            if (filterSetting.getStatus()
                    && ENTER_NOTICE_WEALTH_LEVEL.getValue() == filterSetting.getType()) {
                int wealthLevel = NumberUtil.isNumber(filterSetting.getValue())
                        ? Integer.parseInt(filterSetting.getValue()) : 0;
                enterNoticeCompositeFilter.addFilter(
                        new EnterNoticeWealthLevelFilter(wealthLevel));
            }

            if (filterSetting.getStatus()
                    && ENTER_NOTICE_FANS.getValue() == filterSetting.getType()) {
                enterNoticeCompositeFilter.addFilter(
                        new EnterNoticeFansFilter(relationShipServiceRemote, userId, appId));
            }
        }

        return enterNoticeCompositeFilter.filter(enterNoticeEntries);
    }

    /**
     * 根据用户设置过滤评论
     *
     * @param userId             用户Id
     * @param appId              应用Id
     * @param enterNoticeEntries 原始进场消息列表
     * @return 过滤后的进场消息列表
     */
    public List<EnterNoticeDTO> filterEnterMsgByUserSetting(long userId, int appId,
                                                            List<EnterNoticeDTO> enterNoticeEntries) {
        //空列表就不要浪费资源再查一遍了
        if (CollectionUtils.isEmpty(enterNoticeEntries)) {
            return enterNoticeEntries;
        }

        // 获取用户的评论过滤配置
        List<WaveUserCommentFilterSetting> userFilterSetting =
                waveUserCommentFilterSettingDao.getUserFilterSetting(userId, appId);

        EnterNoticeCompositeV2Filter enterNoticeCompositeFilter = new EnterNoticeCompositeV2Filter();

        for (WaveUserCommentFilterSetting filterSetting : userFilterSetting) {

            if (filterSetting.getStatus()
                    && ENTER_NOTICE_WEALTH_LEVEL.getValue() == filterSetting.getType()) {
                int wealthLevel = NumberUtil.isNumber(filterSetting.getValue())
                        ? Integer.parseInt(filterSetting.getValue()) : 0;
                enterNoticeCompositeFilter.addFilter(
                        new EnterNoticeWealthLevelV2Filter(wealthLevel));
            }

            if (filterSetting.getStatus()
                    && ENTER_NOTICE_FANS.getValue() == filterSetting.getType()) {
                enterNoticeCompositeFilter.addFilter(
                        new EnterNoticeFansV2Filter(relationShipServiceRemote, userId, appId));
            }
        }

        return enterNoticeCompositeFilter.filter(enterNoticeEntries);
    }

    /**
     * 兼容基于用户配置进行渠道进房评论过滤逻辑
     * 由于需要过滤的评论不多，简单实现，不按照进房公告过滤一样创建过滤类
     *
     * @param userId   用户ID
     * @param appId    应用ID
     * @param comments 评论列表
     * @return 结果
     */
    public List<TransientCommentDTO> filterEnterCommentByUserSetting(long userId, int appId, List<TransientCommentDTO> comments) {
        //空列表就不要浪费资源再查一遍了
        if (CollectionUtils.isEmpty(comments)) {
            return comments;
        }

        // 获取用户的评论过滤配置
        List<WaveUserCommentFilterSetting> userFilterSetting =
                waveUserCommentFilterSettingDao.getUserFilterSetting(userId, appId);

        CommentCompositeFilter commentCompositeFilter = new CommentCompositeFilter();
        for (WaveUserCommentFilterSetting filterSetting : userFilterSetting) {

            if (filterSetting.getStatus()
                    && ENTER_NOTICE_WEALTH_LEVEL.getValue() == filterSetting.getType()) {
                int wealthLevel = NumberUtil.isNumber(filterSetting.getValue())
                        ? Integer.parseInt(filterSetting.getValue()) : 0;
                commentCompositeFilter.addFilter(new CommentWealthLevelFilter(wealthLevel));
            }

            if (filterSetting.getStatus()
                    && ENTER_NOTICE_FANS.getValue() == filterSetting.getType()) {
                commentCompositeFilter.addFilter(new CommentFansFilter(relationShipServiceRemote, userId, appId));
            }
        }

        return commentCompositeFilter.filter(comments);
    }
}
