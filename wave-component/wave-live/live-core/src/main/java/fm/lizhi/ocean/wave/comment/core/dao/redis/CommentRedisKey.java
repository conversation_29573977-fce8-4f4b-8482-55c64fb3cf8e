package fm.lizhi.ocean.wave.comment.core.dao.redis;

import fm.lizhi.ocean.wave.common.util.KeyUtils;

/**
 * 评论redis key
 *
 * <AUTHOR>
 */
public enum CommentRedisKey implements CacheKeyGenerator.CacheKeyType {
    /**
     * 发送评论id
     * 数据结构：set集合
     * key：SEND_COMMENTID_SET_${userId}
     * value: 1
     */
    SEND_COMMENTID_SET,

    /**
     * 是否通知进房公告，key_liveId_uid
     * 数据结构：String
     * key: +={liveId}+={uid}
     * value: 1
     */
    SEND_COME_ROOM_COMMENT,

    /**
     * 最新评论消息列表
     * key: +={liveId}
     * score: 评论时间
     * member: 评论数据
     */
    LATEST_COMMENT_MSG_SORT_SET(true),

    /**
     * 进房公告消息列表,包含座驾
     * key: +={liveId}
     * score: 进房时间
     * member: 进房公告数据
     */
    ENTER_NOTICE_MSG_SORT_SET(true),

    /**
     * 评论清除锁
     */
    COMMENT_CLEAN_LOCK(true),

    /**
     * 有评论消息的直播间集合
     * key: LIVE_ROOM_MSG_CHANGE_#{appId}_#{msgType}
     * 数据结构：zset集合
     * param：
     * - appId：应用ID
     * - msgType：消息类型，如评论消息，进房消息
     * Member：liveId
     * Score: 评论时间
     */
    LIVE_ROOM_MSG_CHANGE(true),

    /**
     * 直播间最新的评论时间
     * key: LIVE_ROOM_LATEST_COMMENT_TIME_#{appId}_#{msgType}
     * 数据结构：hash集合
     * Param:
     * - appId：应用ID
     * - msgType：消息类型，如评论消息，进房消息
     */
    LIVE_ROOM_LATEST_COMMENT_TIME(true),

    /**
     * 直播间消息推送锁
     * key: LIVE_ROOM_MSG_PUSH_LOCK_#{appId}_#{msgType}_#{liveId}
     * 数据结构：string
     * Param:
     * - appId：应用ID
     * - msgType：消息类型，如评论消息，进房消息
     * - liveId: 直播节目ID
     * Value: 数量
     * Expire：1min
     */
    LIVE_ROOM_MSG_PUSH_LOCK(true),

    /**
     * 直播间消息超时处理任务锁
     * key: ROOM_MSG_OUT_TIME_TASK_LOCK_#{appId}_#{msgType}
     * 数据结构：string
     * Param:
     * - appId：应用ID
     * - msgType：消息类型，如评论消息，进房消息
     * Value: 数量
     * Expire：1min
     */
    ROOM_MSG_OUT_TIME_TASK_LOCK(true),

    /**
     * 特殊活动评论消息列表
     */
    SPECIAL_ACTIVITY_COMMENT_MSG_SORT_SET(true),
    ;

    /**
     * 是否区分预发线上
     */
    private boolean afx;

    CommentRedisKey() {
    }

    CommentRedisKey(boolean afx) {
        this.afx = afx;
    }

    @Override
    public String getPrefix() {
        return KeyUtils.getPrefix("COMMENT", afx);
    }

    @Override
    public String getKey(Object... args) {
        StringBuilder sb = new StringBuilder(this.getPrefix());

        switch (this) {
            default:
                sb.append("_");
                sb.append(this.name());
                break;
        }

        for (Object o : args) {
            sb.append("_" + o);
        }
        return sb.toString();
    }
}
