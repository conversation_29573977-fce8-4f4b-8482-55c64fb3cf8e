package fm.lizhi.ocean.wave.comment.core.model.bean.xm;

import fm.lizhi.ocean.wave.comment.core.model.bean.SongSpecialActivityCommentExtra;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 西米歌手互动玩法评论额外信息
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class XmSongSpecialActivityCommentExtra implements SongSpecialActivityCommentExtra {

    /**
     * 歌手名称
     */
    private String singerName;

    /**
     * 歌曲封面
     */
    private String songCover;

    /**
     * 歌曲名称
     */
    private String songName;

    /**
     * 演唱主播用户id
     */
    private Long targetUserId;

    /**
     * 演唱主播用户头像
     */
    private String avatar;

    /**
     * 演唱主播所在麦序
     */
    private Integer seatIndex;

    /**
     * 歌曲id
     */
    private Long songId;

    /**
     * 点歌标题
     */
    private String title;
}
