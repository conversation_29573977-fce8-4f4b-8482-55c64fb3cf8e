package fm.lizhi.ocean.wave.comment.core.extension.comment;

import fm.lizhi.ocean.wave.comment.core.model.bean.FreshUserInterestCommentExtra;
import fm.lizhi.ocean.wave.comment.core.model.bean.RoomPlayListCommentCardCommentExtra;
import fm.lizhi.ocean.wave.comment.core.model.bean.RoomRouletteWheelCommentExtra;
import fm.lizhi.ocean.wave.comment.core.model.bean.SongSpecialActivityCommentExtra;
import fm.lizhi.ocean.wave.comment.core.remote.bean.FreshUserInterestBean;
import fm.lizhi.ocean.wave.comment.core.remote.bean.RoomPlaylistCommentCardBean;
import fm.lizhi.ocean.wave.comment.core.remote.bean.RoomRouletteWheelCommentCardBean;
import fm.lizhi.ocean.wave.comment.core.remote.bean.SongInfo;
import fm.lizhi.ocean.wave.common.extension.BusinessEnvAwareProcessor;

/**
 * 评论业务扩展数据处理器
 *
 * <AUTHOR>
 */
public interface ISpecialActivityCommentExtraProcessor extends BusinessEnvAwareProcessor {

    /**
     * 转换点唱信息
     *
     * @param json 评论kafka JSON
     * @return 点唱信息
     */
    SongInfo convertSongInfo(String json);

    /**
     * 转换直播间精选评论信息
     *
     * @param json 评论kafka JSON
     * @return 结果bean
     */
    RoomPlaylistCommentCardBean convertRoomPlaylistCommentCardBean(String json);

    /**
     * 构建点唱评论业务扩展数据
     *
     * @param param 歌曲信息
     * @return 业务扩展数据
     */
    SongSpecialActivityCommentExtra buildSongCommentBizExtra(SongInfo param);

    /**
     * 构建点唱评论业务扩展数据
     *
     * @param cardBean 歌曲信息
     * @return 业务扩展数据
     */
    RoomPlayListCommentCardCommentExtra buildRoomPlayListCommentBizExtra(RoomPlaylistCommentCardBean cardBean);

    /**
     * 转换房间轮盘信息
     *
     * @param json 评论kafka JSON
     * @return 结果bean
     */
    RoomRouletteWheelCommentCardBean convertRoomRouletteWheelCommentBean(String json);

    /**
     * 构建点唱评论业务扩展数据
     *
     * @param cardBean 歌曲信息
     * @return 业务扩展数据
     */
    RoomRouletteWheelCommentExtra buildRoomRouletteWheelCommentExtra(RoomRouletteWheelCommentCardBean cardBean);

    /**
     * 新用户偏好信息
     *
     * @param json 评论kafka JSON
     * @return 结果bean
     */
    FreshUserInterestBean convertFreshUserInterestCommentBean(String json);

    /**
     * 构建新用户偏好信息
     *
     * @param cardBean 歌曲信息
     * @return 业务扩展数据
     */
    FreshUserInterestCommentExtra buildFreshUserInterestCommentExtra(FreshUserInterestBean cardBean);

    @Override
    default Class<? extends BusinessEnvAwareProcessor> getBaseBusinessProcessor() {
        return ISpecialActivityCommentExtraProcessor.class;
    }

}
