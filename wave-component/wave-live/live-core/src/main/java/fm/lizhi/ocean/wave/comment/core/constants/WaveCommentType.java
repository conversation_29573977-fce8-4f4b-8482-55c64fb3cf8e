package fm.lizhi.ocean.wave.comment.core.constants;


/**
 * 评论类型
 *
 * <AUTHOR>
 */
public interface WaveCommentType {

    /**
     * 普通评论
     */
    int GENERAL_COMMENT = 0;
    /**
     * 系统公告类型评论
     */
    int SYSTEM_NOTICE_COMMENT = 1;
    /**
     * 表情评论，普通表情，例如：骰子和猜拳
     */
    int EMOTION_COMMENT = 2;
    /**
     * 系统用户评论
     */
    int SYSTEM_PUBLIC_COMMENT = 3;
    /**
     * 送礼评论
     */
    int SEND_GIFT_COMMENT = 4;

    /**
     * 欢迎按钮评论
     */
    int WELCOME_BUTTON = 5;

    /**
     * 关系拍评论
     */
    int RELATION_PAT_COMMENT = 6;

    /**
     * 送礼后关注引导评论
     */
    int GIVE_GIFT_ATTENTION_COMMENT = 7;

    /**
     * 麦上气泡
     */
    int MIC_BUBBLE = 8;

    /**
     * 上麦欢迎
     */
    int ON_MIC_WELCOME = 9;

    /**
     * 用于ugc/pgc房间进房公告
     */
    int LIVE_INTRO = 10;

    /**
     * 分享节目成功评论
     */
    int SHARE_PARAM_COMMENT = 11;

    /**
     * 活动系统评论
     */
    int ACTIVITY_COMMENT = 12;

    /**
     * 互动表情，app表情
     */
    int INTERACTIVE_MOTION = 13;

    /**
     * 形象礼物评论
     */
    int AVATAR_GIFT_COMMENT = 14;

    /**
     * 双人形象礼物评论
     */
    int AVATAR_GIFT_CP_COMMENT = 15;

    /**
     * 自动打招呼
     */
    int AUTO_SAY_HI_COMMENT = 16;

    /**
     * 资产赠送评论
     */
    int SEND_ASSETS_COMMENT = 17;

    /**
     * 点赞后的评论
     */
    int GIVE_LIKE_COMMENT = 18;

    /**
     * X-发射站玩法评论
     */
    int ROCKET_LAUNCH = 19;

    /**
     * 贵族表情包
     */
    int VIP_EMOTION = 20;

    /**
     * 默契礼物的评论
     */
    int TACIT_GIFT = 21;

    /**
     * 点歌评论
     */
    int ORDER_SONG = 22;

    /**
     * 直播间精选歌单评论
     */
    int LIVE_ROOM_PLAYLIST = 23;

    /**
     * 随机轮盘
     */
    int LIVE_ROOM_ROULETTE_WHEEL = 24;

    /**
     * 新用户偏好信息评论
     */
    int FRESH_USER_INTEREST = 25;

    /**
     * 自定义表情包评论
     */
    int DIY_EMOTION = 26;

    /**
     * 通用表情包评论
     */
    int COMMON_EMOTION = 27;


    /**
     * 作品内容信息评论
     */
    int FEED_CONTENT = 28;

    /**
     * 渠道进房评论，例如：xxx从xxx来了
     */
    int CHANNEL_ENTER_COMMENT = 29;

    /**
     * 未知评论
     */
    int UNKNOWN_COMMENT = -1;

}
