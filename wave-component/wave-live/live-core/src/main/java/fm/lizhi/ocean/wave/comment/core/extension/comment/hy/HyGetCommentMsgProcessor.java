package fm.lizhi.ocean.wave.comment.core.extension.comment.hy;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wave.comment.core.config.CommentCommonConfig;
import fm.lizhi.ocean.wave.comment.core.config.CommentConfig;
import fm.lizhi.ocean.wave.comment.core.constants.WaveCommentType;
import fm.lizhi.ocean.wave.comment.core.convert.CommentMsgConvert;
import fm.lizhi.ocean.wave.comment.core.extension.comment.IGetCommentMsgProcessor;
import fm.lizhi.ocean.wave.comment.core.extension.medal.v2.*;
import fm.lizhi.ocean.wave.comment.core.manager.PlayQuestManager;
import fm.lizhi.ocean.wave.comment.core.model.bean.hy.HyFreshUserComeSourceEnterNoticeExtra;
import fm.lizhi.ocean.wave.comment.core.model.bean.ICommentBean;
import fm.lizhi.ocean.wave.comment.core.model.dto.HyCommentExtraDTO;
import fm.lizhi.ocean.wave.comment.core.model.dto.TransientCommentDTO;
import fm.lizhi.ocean.wave.comment.core.model.dto.UserLevelMarkDTO;
import fm.lizhi.ocean.wave.comment.core.model.dto.WealthLevelDTO;
import fm.lizhi.ocean.wave.comment.core.model.vo.*;
import fm.lizhi.ocean.wave.comment.core.remote.bean.AnthorLevel;
import fm.lizhi.ocean.wave.comment.core.remote.bean.FreshUser;
import fm.lizhi.ocean.wave.comment.core.remote.bean.Medal;
import fm.lizhi.ocean.wave.comment.core.remote.bean.SimpleMedal;
import fm.lizhi.ocean.wave.common.util.ModelMapperUtils;
import fm.lizhi.ocean.wave.common.util.UrlUtils;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wave.server.common.util.ContextUtils;
import fm.lizhi.ocean.wave.server.common.vo.ResultVO;
import fm.lizhi.ocean.wave.user.api.MedalService;
import fm.lizhi.ocean.wave.user.api.UserService;
import fm.lizhi.ocean.wave.user.api.UserWealthLevelService;
import fm.lizhi.ocean.wave.user.result.FreshUserResult;
import fm.lizhi.ocean.wave.user.result.MedalGroupResult;
import fm.lizhi.ocean.wave.user.result.MedalResult;
import fm.lizhi.ocean.wave.user.result.WealthLevelResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * Description: 黑叶获取直播间消息处理类（评论和进房公告）
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class HyGetCommentMsgProcessor implements IGetCommentMsgProcessor {

    @Autowired
    private CommentConfig commentConfig;

    @Autowired
    private WealthMedalV2Node wealthMedalNode;

    @Autowired
    private HyAnchorLevelMedalV2Node anchorLevelMedalNode;

    @Autowired
    private HyFreshUserMedalV2Node freshUserMedalNode;

    @Autowired
    private HyUserMedalListV2Node userMedalListNode;

    @Autowired
    private UserRoleMedalV2Node userRoleMedalNode;

    @Autowired
    private UserWearMedalListV2Node userWearMedalListNode;

    @Autowired
    private MedalService medalService;

    @Autowired
    private UserWealthLevelService userWealthLevelService;

    @Autowired
    private UserService userService;

    @Autowired
    private HyVipLevelMedalV2Node hyVipLevelMedalNode;

    @Autowired
    private PlayQuestManager playQuestManager;

    @Override
    public BusinessEvnEnum getBusinessEnv() {
        return BusinessEvnEnum.HEI_YE;
    }

    @Override
    public CommentCommonConfig getCommentConfig() {
        return commentConfig.getHy();
    }


    @Override
    public MsgUserVO<HyMsgUserExtraInfo> createUserAndFillBizExtra(TransientCommentDTO transientComment) {
        return new MsgUserVO<>();
    }

    @Override
    public void adapterUserExtra(TransientCommentDTO transientComment, MsgUserVO<?> userVO) {
    }

    @Override
    public List<ICommentV2MedalNode> getBuildCommentMedalNodes() {
        List<ICommentV2MedalNode> nodes = new ArrayList<>(16);
        //不同的业务需要的勋章节点不同，且顺序不同
        nodes.add(hyVipLevelMedalNode);
        nodes.add(freshUserMedalNode);
        nodes.add(userRoleMedalNode);
        nodes.add(wealthMedalNode);
        nodes.add(anchorLevelMedalNode);
        nodes.add(userWearMedalListNode);
        nodes.add(userMedalListNode);
        return nodes;
    }

    @Override
    public ResultVO<TransientCommentDTO> resetCommentUserProperty(TransientCommentDTO transientComment) {
        boolean needReset = transientComment.getCommentType() == WaveCommentType.SYSTEM_NOTICE_COMMENT && transientComment.getOperatorUserId() > 0;
        if (!needReset) {
            return ResultVO.success(transientComment);
        }

        TransientCommentDTO targetComment = ModelMapperUtils.MODEL_MAPPER.map(transientComment, TransientCommentDTO.class);
        HyCommentExtraDTO hyCommentExtra = targetComment.getHyCommentExtra();

        //查询勋章列表信息
        Result<List<MedalGroupResult>> medalUserRes = medalService.medalUserUseListFromCache(targetComment.getUserId());
        //查询财富等级信息
        Result<WealthLevelResult> wealthRes = userWealthLevelService.getWealthLevelFromCache(targetComment.getUserId());
        //是否是新用户查询
        Result<FreshUserResult> freshUserResult = userService.isFreshUserByCache(targetComment.getUserId());
        //查询勋章列表信息
        Result<List<MedalResult>> medalListRes = medalService.getMedalListFromCache(targetComment.getUserId());
        //查询用户等级
        LevelMarkVO userLevel = getUserLevel(targetComment.getUserId());

        List<SimpleMedal> simpleMedals = CommentMsgConvert.I.convertSimpleMedalList(medalUserRes.target());
        WealthLevelDTO wealthLevelDTO = CommentMsgConvert.I.convertWealthLevel(wealthRes.target());
        FreshUser freshUser = CommentMsgConvert.I.convertFreshUser(freshUserResult.target());
        List<Medal> medals = CommentMsgConvert.I.convertMedalList(medalListRes.target());
        AnthorLevel anthorLevel = CommentMsgConvert.I.convertAnthorLevel(userLevel);

        if (wealthLevelDTO != null) {
            wealthLevelDTO.setCover(buildWealthUrl(wealthLevelDTO));
        }
        if (Objects.nonNull(anthorLevel)) {
            if (commentConfig.getHy().isDefaultLevelMedalAspectFlag()) {
                anthorLevel.setAspect(commentConfig.getHy().getDefaultLevelMedalAspect());
            } else {
                anthorLevel.setAspect(userLevel.getAspect().floatValue());
            }
        }

        targetComment.setWealthLevel(wealthLevelDTO);
        targetComment.setFreshUser(freshUser);
        targetComment.setMedal(medals);
        hyCommentExtra.setAnthorLevel(anthorLevel);
        hyCommentExtra.setSimpleMedals(simpleMedals);
        return ResultVO.success(targetComment);
    }

    @Override
    public void fillEnterCommentMedal(TransientCommentDTO transientComment) {
    }

    @Override
    public void fillCommentExtraInfo(CommentVO commentVO, TransientCommentDTO transientComment) {
        // 小陪伴独有  多元素评论
        HyCommentExtraDTO hyCommentExtra = transientComment.getHyCommentExtra();
        @SuppressWarnings("unchecked")
        CommentExtraInfoVO<HyCommentExtraInfoVO> extra = (CommentExtraInfoVO<HyCommentExtraInfoVO>) commentVO.getExtra();
        if (extra == null) {
            extra = new CommentExtraInfoVO<HyCommentExtraInfoVO>();
        }
        HyCommentExtraInfoVO bizExtra = extra.getBizOtherExtra();
        if (bizExtra == null) {
            bizExtra = new HyCommentExtraInfoVO();
        }

        //构建设置黑叶特有的评论扩展属性
        bizExtra.setTailEffect(hyCommentExtra.getTailEffect());
        bizExtra.setContentItems(hyCommentExtra.getContentItems());
        extra.setBizOtherExtra(bizExtra);
        commentVO.setExtra(extra);
    }

    @Override
    public List<ICommentBean> processSpecialEnterMsg(List<TransientCommentDTO> transientCommentDTOList, List<CommentVO> commentVOList) {
        //过滤出新人进房消息
        Optional<TransientCommentDTO> commentOptional = transientCommentDTOList.stream().filter(comment -> comment.getCommentType() == WaveCommentType.CHANNEL_ENTER_COMMENT).filter(comment -> comment.getHyCommentExtra().getComeSource() != null).findFirst();

        if (!commentOptional.isPresent()) {
            return new ArrayList<>(commentVOList);
        }

        TransientCommentDTO transientCommentDTO = commentOptional.get();
        //过滤出新人进房消息
        Optional<CommentVO> enterCommentOptional = commentVOList.stream().filter(comment -> comment.getCommentId().equals(String.valueOf(transientCommentDTO.getCommentId()))).findFirst();
        if (!enterCommentOptional.isPresent()) {
            return new ArrayList<>(commentVOList);
        }

        HyFreshUserComeSourceEnterNoticeExtra extra = new HyFreshUserComeSourceEnterNoticeExtra();
        boolean isNew = transientCommentDTO.getFreshUser().isIs();

        //获取通过谁进房的用户ID
        long comeSourceTgtUid = transientCommentDTO.getHyCommentExtra().getComeSource().getComeSourceTgtUid();
        //如果用户A 通过 用户B 进房 （如通过用户B的个人资料页/分享/动态）进入，comeSourceTgtUid不为空，当前用户-用户B 看到用户A进房后，进房样式会看到特殊样式，其他用户看到的还是普通样式
        if (comeSourceTgtUid > 0 && comeSourceTgtUid == ContextUtils.getContext().getUserId()) {
            extra.setShowSpecial(true);
        }

        //默认设置false
        extra.setShowSpecial(false);
        extra.setShowWelcomeNotice(false);
        extra.setFreshUser(isNew);
        //是新人才显示欢迎通知
        extra.setShowWelcomeNotice(isNew);
        extra.setEnterRoomText(commentConfig.getHy().getEnterRoomText());
        extra.setFreshUserIcon(commentConfig.getHy().getFreshUserIcon());
        extra.setFreshUserRoomIcon(commentConfig.getHy().getFreshUserRoomIcon());


        //commentVOList过滤掉记录
        List<ICommentBean> res = new ArrayList<>(commentVOList);

        //遍历消息
        for (int i = 0; i < commentVOList.size(); i++) {
            CommentVO comment = (CommentVO) res.get(i);
            if (comment.getCommentId().equals(String.valueOf(transientCommentDTO.getCommentId()))) {
                //替换掉评论记录
                SpecialActivityCommentVO specialActivityCommentVO = CommentMsgConvert.I.convert(enterCommentOptional.get());
                specialActivityCommentVO.setActivityExtra(extra);
                res.set(i, specialActivityCommentVO);
            }
        }
        return res;
    }

    /**
     * 获取用户等级
     *
     * @param uid 用户ID
     * @return
     */
    public LevelMarkVO getUserLevel(long uid) {
        Result<UserLevelMarkDTO> result = playQuestManager.getUserLevelMarkFromCache(uid);
        if (null != result && result.rCode() == 0) {
            List<LevelMarkVO> levelMarks = result.target().getLevelMarks();
            if (CollectionUtils.isEmpty(levelMarks)) {
                return null;
            }
            Optional<LevelMarkVO> first = levelMarks.stream().filter(s -> s.getType() == 2).findFirst();
            if (first.isPresent()) {
                return first.get();
            }

        }
        return null;
    }

    /**
     * 构建财富等级图片地址
     *
     * @param wealthInfo 财富等级信息
     * @return 图片地址
     */
    private String buildWealthUrl(WealthLevelDTO wealthInfo) {
        if (wealthInfo.getCover().startsWith("http")) {
            return wealthInfo.getCover();
        }
        //财富等级CDN、公屏处用特殊地址
        String cdn = commentConfig.getHy().getWealthLevelCdn();
        return UrlUtils.addCdnHost(cdn, wealthInfo.getCover());
    }
}
