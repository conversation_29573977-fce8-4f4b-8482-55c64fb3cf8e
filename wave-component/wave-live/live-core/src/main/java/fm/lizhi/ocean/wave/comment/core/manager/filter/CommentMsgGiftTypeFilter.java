package fm.lizhi.ocean.wave.comment.core.manager.filter;

import fm.lizhi.ocean.wave.comment.core.model.dto.TransientCommentDTO;
import hy.fm.lizhi.datacenter.comment.pp.constant.CommentTypeExtension;

import java.util.ArrayList;
import java.util.List;


/**
 * 送礼消息过滤
 * <AUTHOR>
 */
public class CommentMsgGiftTypeFilter implements CommentV2Filter {

    @Override
    public List<TransientCommentDTO> filter(List<TransientCommentDTO> comments) {
        List<TransientCommentDTO> result = new ArrayList<>();
        for (TransientCommentDTO comment : comments) {
            if (CommentTypeExtension.GIFT == comment.getCommentTypeExtension()) {
                result.add(comment);
            }
        }

        return result;
    }
}
