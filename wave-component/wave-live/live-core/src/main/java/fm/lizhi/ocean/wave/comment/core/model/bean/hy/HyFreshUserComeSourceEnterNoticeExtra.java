package fm.lizhi.ocean.wave.comment.core.model.bean.hy;

import fm.lizhi.ocean.wave.comment.core.model.bean.FreshUserComeSourceEnterNoticeExtra;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class HyFreshUserComeSourceEnterNoticeExtra implements FreshUserComeSourceEnterNoticeExtra {

    /**
     * 是否显示欢迎通知
     */
    private Boolean showWelcomeNotice;

    /**
     * 是否新用户
     */
    private Boolean freshUser;

    /**
     * 是否显示特别样式
     */
    private Boolean showSpecial;

    /**
     * 新用户图标
     */
    private String freshUserIcon;

    /**
     * 新人进房标签
     */
    private String freshUserRoomIcon;

    /**
     * 进房文案
     */
    private String enterRoomText;

}
