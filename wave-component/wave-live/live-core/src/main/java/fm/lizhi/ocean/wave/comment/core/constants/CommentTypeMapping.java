package fm.lizhi.ocean.wave.comment.core.constants;

import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import hy.fm.lizhi.datacenter.comment.pp.constant.CommentType;
import hy.fm.lizhi.datacenter.comment.pp.constant.CommentTypeExtension;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.Map;

/**
 * 评论类型映射
 *
 * <AUTHOR>
 * @date 2023/8/2 7:43 下午
 */
@Slf4j
@Getter
public enum CommentTypeMapping {

    //================ HY评论类型枚举 ==================
    /**
     * 普通评论
     */
    HY_GENERAL_COMMENT(WaveCommentType.GENERAL_COMMENT, BusinessEvnEnum.HEI_YE, CommentType.GENERAL_COMMENT),

    /**
     * 系统公告类型评论
     */
    HY_SYSTEM_NOTICE_COMMENT(WaveCommentType.SYSTEM_NOTICE_COMMENT, BusinessEvnEnum.HEI_YE, CommentType.SYS_MSG_COMMENT),

    /**
     * 表情评论，普通表情，例如：猜拳，投骰子
     */
    HY_EMOTION_COMMENT(WaveCommentType.EMOTION_COMMENT, BusinessEvnEnum.HEI_YE, CommentType.EMOTION_COMMENT),

    /**
     * 系统公共评论
     */
    HY_SYSTEM_PUBLIC_COMMENT(WaveCommentType.SYSTEM_PUBLIC_COMMENT, BusinessEvnEnum.HEI_YE, CommentType.SYS_PUBLIC_COMMENT),

    /**
     * 送礼评论
     */
    HY_SEND_GIFT_COMMENT(WaveCommentType.SEND_GIFT_COMMENT, BusinessEvnEnum.HEI_YE, CommentType.SYS_PUBLIC_COMMENT, CommentTypeExtension.GIFT),

    /**
     * 欢迎按钮评论
     */
    HY_WELCOME_BUTTON(WaveCommentType.WELCOME_BUTTON, BusinessEvnEnum.HEI_YE, CommentType.HY_WELCOME_BUTTON),

    /**
     * 关系拍评论
     */
    HY_RELATION_PAT_COMMENT(WaveCommentType.RELATION_PAT_COMMENT, BusinessEvnEnum.HEI_YE, CommentType.RELATION_PAT_COMMENT),

    /**
     * 送礼后引导关注评论
     */
    HY_GIVE_GIFT_ATTENTION_COMMENT(WaveCommentType.GIVE_GIFT_ATTENTION_COMMENT, BusinessEvnEnum.HEI_YE, CommentType.PP_GIVE_GIFT_ATTENTION_COMMENT),

    /**
     * 麦上气泡评论
     */
    HY_MIC_BUBBLE(WaveCommentType.MIC_BUBBLE, BusinessEvnEnum.HEI_YE, CommentType.HY_MIC_BUBBLE),

    /**
     * 上麦欢迎评论
     */
    HY_ON_MIC_WELCOME(WaveCommentType.ON_MIC_WELCOME, BusinessEvnEnum.HEI_YE, CommentType.HY_ON_MIC_WELCOME),

    /**
     * 进房公告
     */
    HY_LIVE_INTRO(WaveCommentType.LIVE_INTRO, BusinessEvnEnum.HEI_YE, CommentType.HY_LIVE_INTRO),

    /**
     * 分享节目成功评论
     */
    HY_SHARE_PARAM_COMMENT(WaveCommentType.SHARE_PARAM_COMMENT, BusinessEvnEnum.HEI_YE, CommentType.SHARE_PARAM_COMMENT),

    /**
     * 活动评论
     */
    HY_ACTIVITY_COMMENT(WaveCommentType.ACTIVITY_COMMENT, BusinessEvnEnum.HEI_YE, CommentType.SYS_MSG_COMMENT, CommentTypeExtension.ACTIVITY_COMMENT),

    /**
     * 互动表情
     */
    HY_INTERACTIVE_MOTION(WaveCommentType.INTERACTIVE_MOTION, BusinessEvnEnum.HEI_YE, CommentType.INTERACTIVE_MOTION),

    /**
     * X-发射站评论
     */
    HY_ROCKET_LAUNCH(WaveCommentType.ROCKET_LAUNCH, BusinessEvnEnum.HEI_YE, CommentType.ROCKET_LAUNCH),

    /**
     *
     */
    HY_TACIT_GIFT(WaveCommentType.TACIT_GIFT, BusinessEvnEnum.HEI_YE, CommentType.TACIT_GIFT),

    /**
     * 渠道进房评论，例如：xxx从xxx来了
     */
    HY_CHANNEL_ENTER_COMMENT(WaveCommentType.CHANNEL_ENTER_COMMENT, BusinessEvnEnum.HEI_YE, CommentType.SYS_MSG_COMMENT, CommentTypeExtension.ENTER_ROOM),

    //================ PP评论类型枚举 ==================
    /**
     * 普通评论
     */
    PP_GENERAL_COMMENT(WaveCommentType.GENERAL_COMMENT, BusinessEvnEnum.PP, pp.fm.lizhi.datacenter.comment.pp.constant.CommentType.GENERAL_COMMENT),

    /**
     * 系统公告类型评论
     */
    PP_SYSTEM_NOTICE_COMMENT(WaveCommentType.SYSTEM_NOTICE_COMMENT, BusinessEvnEnum.PP, pp.fm.lizhi.datacenter.comment.pp.constant.CommentType.SYS_MSG_COMMENT),

    /**
     * 表情评论
     */
    PP_EMOTION_COMMENT(WaveCommentType.EMOTION_COMMENT, BusinessEvnEnum.PP, pp.fm.lizhi.datacenter.comment.pp.constant.CommentType.EMOTION_COMMENT),

    /**
     * 系统公共评论
     */
    PP_SYSTEM_PUBLIC_COMMENT(WaveCommentType.SYSTEM_PUBLIC_COMMENT, BusinessEvnEnum.PP, pp.fm.lizhi.datacenter.comment.pp.constant.CommentType.SYS_PUBLIC_COMMENT),

    /**
     * 送礼评论
     */
    PP_SEND_GIFT_COMMENT(WaveCommentType.SEND_GIFT_COMMENT, BusinessEvnEnum.PP, pp.fm.lizhi.datacenter.comment.pp.constant.CommentType.SYS_PUBLIC_COMMENT, pp.fm.lizhi.datacenter.comment.pp.constant.CommentTypeExtension.GIFT),

    /**
     * 关系拍评论
     */
    PP_RELATION_PAT_COMMENT(WaveCommentType.RELATION_PAT_COMMENT, BusinessEvnEnum.PP, pp.fm.lizhi.datacenter.comment.pp.constant.CommentType.RELATION_PAT_COMMENT),

    /**
     * 送礼后引导关注评论
     */
    PP_GIVE_GIFT_ATTENTION_COMMENT(WaveCommentType.GIVE_GIFT_ATTENTION_COMMENT, BusinessEvnEnum.PP, pp.fm.lizhi.datacenter.comment.pp.constant.CommentType.PP_GIVE_GIFT_ATTENTION_COMMENT),

    /**
     * 活动评论
     */
    PP_ACTIVITY_COMMENT(WaveCommentType.ACTIVITY_COMMENT, BusinessEvnEnum.PP, pp.fm.lizhi.datacenter.comment.pp.constant.CommentType.SYS_MSG_COMMENT, pp.fm.lizhi.datacenter.comment.pp.constant.CommentTypeExtension.ACTIVITY_COMMENT),

    /**
     * 互动表情
     */
    PP_INTERACTIVE_MOTION(WaveCommentType.INTERACTIVE_MOTION, BusinessEvnEnum.PP, pp.fm.lizhi.datacenter.comment.pp.constant.CommentType.PP_EMOTION_COMMENT),

    /**
     * 分享节目成功评论
     */
    PP_SHARE_PARAM_COMMENT(WaveCommentType.SHARE_PARAM_COMMENT, BusinessEvnEnum.PP, pp.fm.lizhi.datacenter.comment.pp.constant.CommentType.SHARE_PARAM_COMMENT),

    /**
     * 自定义表情包评论
     */
    PP_DIY_EMOTION(WaveCommentType.DIY_EMOTION, BusinessEvnEnum.PP, pp.fm.lizhi.datacenter.comment.pp.constant.CommentType.DIY_EMOTION_COMMENT),

    /**
     * 渠道进房评论
     */
    PP_CHANNEL_ENTER_COMMENT(WaveCommentType.CHANNEL_ENTER_COMMENT, BusinessEvnEnum.PP, pp.fm.lizhi.datacenter.comment.pp.constant.CommentType.SYS_MSG_COMMENT, pp.fm.lizhi.datacenter.comment.pp.constant.CommentTypeExtension.ENTER_ROOM),


    //================ XM评论类型枚举 ==================
    /**
     * 普通评论
     */
    XM_GENERAL_COMMENT(WaveCommentType.GENERAL_COMMENT, BusinessEvnEnum.XIMI, xm.fm.lizhi.datacenter.comment.pp.constant.CommentType.GENERAL_COMMENT),

    /**
     * 系统公告类型评论
     */
    XM_SYSTEM_NOTICE_COMMENT(WaveCommentType.SYSTEM_NOTICE_COMMENT, BusinessEvnEnum.XIMI, xm.fm.lizhi.datacenter.comment.pp.constant.CommentType.SYS_MSG_COMMENT),

    /**
     * 表情评论
     */
    XM_EMOTION_COMMENT(WaveCommentType.EMOTION_COMMENT, BusinessEvnEnum.XIMI, xm.fm.lizhi.datacenter.comment.pp.constant.CommentType.EMOTION_COMMENT),

    /**
     * 系统公共评论
     */
    XM_SYSTEM_PUBLIC_COMMENT(WaveCommentType.SYSTEM_PUBLIC_COMMENT, BusinessEvnEnum.XIMI, xm.fm.lizhi.datacenter.comment.pp.constant.CommentType.SYS_PUBLIC_COMMENT),

    /**
     * 送礼评论
     */
    XM_SEND_GIFT_COMMENT(WaveCommentType.SEND_GIFT_COMMENT, BusinessEvnEnum.XIMI, xm.fm.lizhi.datacenter.comment.pp.constant.CommentType.SYS_PUBLIC_COMMENT, xm.fm.lizhi.datacenter.comment.pp.constant.CommentTypeExtension.GIFT),

    /**
     * 送礼后引导关注评论
     */
    XM_GIVE_GIFT_ATTENTION_COMMENT(WaveCommentType.GIVE_GIFT_ATTENTION_COMMENT, BusinessEvnEnum.XIMI, xm.fm.lizhi.datacenter.comment.pp.constant.CommentType.PP_GIVE_GIFT_ATTENTION_COMMENT),

    /**
     * 活动评论
     */
    XM_ACTIVITY_COMMENT(WaveCommentType.ACTIVITY_COMMENT, BusinessEvnEnum.XIMI, xm.fm.lizhi.datacenter.comment.pp.constant.CommentType.SYS_MSG_COMMENT, xm.fm.lizhi.datacenter.comment.pp.constant.CommentTypeExtension.ACTIVITY_COMMENT),

    /**
     * 互动表情
     */
    XM_INTERACTIVE_MOTION(WaveCommentType.INTERACTIVE_MOTION, BusinessEvnEnum.XIMI, xm.fm.lizhi.datacenter.comment.pp.constant.CommentType.PP_EMOTION_COMMENT),

    /**
     * 形象礼物评论
     */
    XM_AVATAR_GIFT_COMMENT(WaveCommentType.AVATAR_GIFT_COMMENT, BusinessEvnEnum.XIMI, xm.fm.lizhi.datacenter.comment.pp.constant.CommentType.AVATAR_GIFT_COMMENT),

    /**
     * 双人形象礼物评论
     */
    XM_AVATAR_GIFT_CP_COMMENT(WaveCommentType.AVATAR_GIFT_CP_COMMENT, BusinessEvnEnum.XIMI, xm.fm.lizhi.datacenter.comment.pp.constant.CommentType.AVATAR_GIFT_CP_COMMENT),

    /**
     * 自动打招呼评论
     */
    XM_AUTO_SAY_HI_COMMENT(WaveCommentType.AUTO_SAY_HI_COMMENT, BusinessEvnEnum.XIMI, xm.fm.lizhi.datacenter.comment.pp.constant.CommentType.AUTO_SAY_HI_COMMENT),

    /**
     * 资产赠送评论
     */
    XM_SEND_ASSETS_COMMENT(WaveCommentType.SEND_ASSETS_COMMENT, BusinessEvnEnum.XIMI, xm.fm.lizhi.datacenter.comment.pp.constant.CommentType.SEND_DRESSUP_COMMENT),

    /**
     * 点赞后的评论
     */
    XM_GIVE_LIKE_COMMENT(WaveCommentType.GIVE_LIKE_COMMENT, BusinessEvnEnum.XIMI, xm.fm.lizhi.datacenter.comment.pp.constant.CommentType.GIVEALIKE_COMMENT),

    /**
     * 分享节目成功评论
     */
    XM_SHARE_PARAM_COMMENT(WaveCommentType.SHARE_PARAM_COMMENT, BusinessEvnEnum.XIMI, xm.fm.lizhi.datacenter.comment.pp.constant.CommentType.SHARE_PARAM_COMMENT),

    /**
     * 西米贵族表情包
     */
    XM_VIP_EMOTION(WaveCommentType.VIP_EMOTION, BusinessEvnEnum.XIMI, xm.fm.lizhi.datacenter.comment.pp.constant.CommentType.XM_EMOTION_COMMENT),

    /**
     * 西米点歌评论
     */
    XM_SONG_COMMENT(WaveCommentType.ORDER_SONG, BusinessEvnEnum.XIMI, xm.fm.lizhi.datacenter.comment.pp.constant.CommentType.ORDER_SONG_COMMENT),

    /**
     * 西米精选歌单评论
     */
    XM_LIVE_RECOMMEND_PLAYLIST_COMMENT(WaveCommentType.LIVE_ROOM_PLAYLIST, BusinessEvnEnum.XIMI, xm.fm.lizhi.datacenter.comment.pp.constant.CommentType.VOCAL_ROOM_PLAYLIST),
    /**
     * 西米随机轮盘评论
     */
    XM_LIVE_ROULETTE_WHEEL_COMMENT(WaveCommentType.LIVE_ROOM_ROULETTE_WHEEL, BusinessEvnEnum.XIMI, xm.fm.lizhi.datacenter.comment.pp.constant.CommentType.ROULETTE_WHEEL_COMMENT),
    /**
     * 西米新用户偏好信息评论
     */
    XM_FRESH_USER_INTEREST_COMMENT(WaveCommentType.FRESH_USER_INTEREST, BusinessEvnEnum.XIMI, xm.fm.lizhi.datacenter.comment.pp.constant.CommentType.FRESH_USER_INTEREST_COMMENT),
    /**
     * 西米通用表情
     */
    XM_COMMON_EMOTION_COMMENT(WaveCommentType.COMMON_EMOTION, BusinessEvnEnum.XIMI, xm.fm.lizhi.datacenter.comment.pp.constant.CommentType.XM_COMMON_EMOTION_COMMENT),
    /**
     * 西米作品评论
     */
    XM_FEED_CONTENT_COMMENT(WaveCommentType.FEED_CONTENT, BusinessEvnEnum.XIMI, xm.fm.lizhi.datacenter.comment.pp.constant.CommentType.FEED_CONTENT_COMMENT),

    /**
     * 渠道进房评论，例如：xxx从xxx来了
     */
    XM_CHANNEL_ENTER_COMMENT(WaveCommentType.CHANNEL_ENTER_COMMENT, BusinessEvnEnum.XIMI, xm.fm.lizhi.datacenter.comment.pp.constant.CommentType.SYS_MSG_COMMENT, xm.fm.lizhi.datacenter.comment.pp.constant.CommentTypeExtension.ENTER_ROOM),

    ;
    private final int waveCommentType;
    private final BusinessEvnEnum businessEvn;
    private final int bizCommentType;
    private final Integer commentTypeExtend;


    CommentTypeMapping(int waveCommentType, BusinessEvnEnum businessEvn, int bizCommentType, Integer commentTypeExtend) {
        this.waveCommentType = waveCommentType;
        this.businessEvn = businessEvn;
        this.bizCommentType = bizCommentType;
        this.commentTypeExtend = commentTypeExtend;
    }

    CommentTypeMapping(int waveCommentType, BusinessEvnEnum businessEvn, int bizCommentType) {
        this(waveCommentType, businessEvn, bizCommentType, null);
    }

    private static final Map<String, CommentTypeMapping> extendTypeMap = new HashMap<>(8);

    static {
        for (CommentTypeMapping typeMapping : CommentTypeMapping.values()) {
            if (typeMapping.getCommentTypeExtend() != null) {
                String key = getExtendTypeMapKey(typeMapping.getCommentTypeExtend(), typeMapping.getBusinessEvn().getAppId());
                extendTypeMap.put(key, typeMapping);
            }

        }
    }

    /**
     * 业务方评论类型转平台评论类型
     *
     * @param bizCommentType    业务评论类型
     * @param businessEvn       环境
     * @param commentTypeExtend 拓展评论类型
     * @return 平台评论类型
     */
    public static int getWaveCommentType(int bizCommentType, BusinessEvnEnum businessEvn, int commentTypeExtend) {
        //先判断是否有拓展字段,否则会返回错误的类型
        String key = getExtendTypeMapKey(commentTypeExtend, businessEvn.getAppId());
        CommentTypeMapping mapping = extendTypeMap.get(key);
        if (mapping != null) {
            return mapping.getWaveCommentType();
        }

        for (CommentTypeMapping typeMapping : values()) {
            if (typeMapping.businessEvn != businessEvn) {
                //不是同业务的，过滤掉
                continue;
            }

            //评论类型匹配即是目标类型
            if (typeMapping.getBizCommentType() == bizCommentType) {
                return typeMapping.getWaveCommentType();
            }
        }
        return WaveCommentType.UNKNOWN_COMMENT;
    }

    /**
     * 业务方评论类型转平台评论类型
     *
     * @param waveCommentType 平台业务评论类型
     * @param businessEvn     环境
     * @return 业务评论类型
     */
    public static int getBizCommentType(int waveCommentType, BusinessEvnEnum businessEvn) {
        for (CommentTypeMapping typeMapping : values()) {
            if (typeMapping.businessEvn != businessEvn) {
                //不是同业务的，过滤掉
                continue;
            }

            //评论类型匹配即是目标类型, 平台评论类型是唯一的，对应业务的评论类型也是唯一的，不需要commentTypeExtend字段
            if (typeMapping.getWaveCommentType() == waveCommentType) {
                return typeMapping.getBizCommentType();
            }
        }

        log.warn("waveCommentType not found,waveCommentType={},businessEvn={}", waveCommentType, businessEvn.getBusinessEnv());
        //找不到对应的评论类型，抛出异常
        throw new RuntimeException("找不到合适的业务评论类型");
    }

    private static String getExtendTypeMapKey(int commentTypeExtend, int appId) {
        return commentTypeExtend + "_" + appId;
    }
}
