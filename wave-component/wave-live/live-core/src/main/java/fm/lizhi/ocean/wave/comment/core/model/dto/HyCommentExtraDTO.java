package fm.lizhi.ocean.wave.comment.core.model.dto;

import fm.lizhi.ocean.wave.comment.core.model.vo.ComeSourceVO;
import fm.lizhi.ocean.wave.comment.core.model.vo.TailEffectVo;
import fm.lizhi.ocean.wave.comment.core.remote.bean.*;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class HyCommentExtraDTO {

    /**
     * 尾灯 黑叶独有的
     */
    private TailEffectVo tailEffect;

    /**
     * 多元素的评论内容
     *  黑叶独有的
     */
    private List<MultiContentItem> contentItems;

    /**
     * 进房来源 黑叶独有
     */
    private ComeSourceVO comeSource;

    /**
     * 主播等级勋章
     */
    private AnthorLevel anthorLevel;

    /**
     * 用户佩戴的勋章集合
     */
    private List<SimpleMedal> simpleMedals;

}
