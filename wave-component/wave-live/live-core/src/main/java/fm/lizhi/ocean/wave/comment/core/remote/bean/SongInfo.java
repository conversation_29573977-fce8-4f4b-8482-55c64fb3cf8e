package fm.lizhi.ocean.wave.comment.core.remote.bean;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class SongInfo {

    /**
     * 标题
     */
    private String title;

    /**
     * 歌手名称
     */
    private String singerName;

    /**
     * 歌曲封面
     */
    private String songCover;

    /**
     * 歌曲名称
     */
    private String songName;

    /**
     * 演唱主播用户id
     */
    private Long targetUserId;

    /**
     * 演唱主播用户头像
     */
    private String avatar;

    /**
     * 演唱主播所在麦序
     */
    private Integer seatIndex;

    /**
     * 歌曲id
     */
    private Long songId;

}
