package fm.lizhi.ocean.wave.comment.core.extension.comment.hy;

import fm.lizhi.ocean.wave.comment.core.extension.comment.ICommentMedalNodeProcessor;
import fm.lizhi.ocean.wave.comment.core.model.dto.EnterNoticeDTO;
import fm.lizhi.ocean.wave.comment.core.model.dto.TransientCommentDTO;
import fm.lizhi.ocean.wave.comment.core.remote.bean.SimpleMedal;
import fm.lizhi.ocean.wave.comment.core.remote.bean.VipInfoEntry;
import fm.lizhi.ocean.wave.comment.core.remote.bean.VipLevel;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 */
@Component
public class HyCommentMedalNodeProcessor implements ICommentMedalNodeProcessor {
    @Override
    public List<SimpleMedal> getWearMedalList(TransientCommentDTO transientCommentDTO) {
        return transientCommentDTO.getHyCommentExtra().getSimpleMedals();
    }

    @Override
    public VipLevel getVipLevel(TransientCommentDTO transientCommentDTO) {
        return null;
    }

    @Override
    public VipInfoEntry getVipInfoEntry(EnterNoticeDTO enterNoticeEntry) {
        return enterNoticeEntry.getHyEnterNoticeExtraDTO().getVipInfoEntry();
    }

    @Override
    public BusinessEvnEnum getBusinessEnv() {
        return BusinessEvnEnum.HEI_YE;
    }
}
