package fm.lizhi.ocean.wave.comment.core.manager.filter;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.commons.service.client.rcode.GeneralRCode;
import fm.lizhi.ocean.wave.comment.core.model.dto.EnterNoticeDTO;
import fm.lizhi.ocean.wave.comment.core.remote.param.GetFansParam;
import fm.lizhi.ocean.wave.comment.core.remote.result.GetFansResult;
import fm.lizhi.ocean.wave.comment.core.remote.service.IRelationShipServiceRemote;

import java.util.ArrayList;
import java.util.List;

/**
 * 进场消息粉丝等级过滤器
 * <AUTHOR>
 */
public class EnterNoticeFansV2Filter implements EnterNoticeV2Filter {

    private final IRelationShipServiceRemote relationShipServiceRemote;
    private final Long userId;
    private final Integer appId;

    public EnterNoticeFansV2Filter(IRelationShipServiceRemote relationShipServiceRemote,
                                   Long userId,
                                   Integer appId) {
        this.relationShipServiceRemote = relationShipServiceRemote;
        this.userId = userId;
        this.appId = appId;
    }

    @Override
    public List<EnterNoticeDTO> filter(List<EnterNoticeDTO> msg) {
        List<EnterNoticeDTO> result = new ArrayList<>();
        GetFansParam param = new GetFansParam();
        //查询粉丝信息 <p>例如: 查询用户b是否是用户a的粉丝，
        // 则参数userId=a, toUserId=b，在rCode=0的情况下，判断下响应字段status=1
        param.setUserId(userId);
        param.setAppId(Long.valueOf(appId));
        for (EnterNoticeDTO enterNoticeEntry : msg) {
            param.setToUserId(enterNoticeEntry.getUserId());
            Result<GetFansResult> getFansResultResult = relationShipServiceRemote.getFans(param);
            if (getFansResultResult.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
                continue;
            }

            GetFansResult getFansResult = getFansResultResult.target();
            if (getFansResult.getStatus() == 1) {
                result.add(enterNoticeEntry);
            }
        }

        return result;
    }
}
