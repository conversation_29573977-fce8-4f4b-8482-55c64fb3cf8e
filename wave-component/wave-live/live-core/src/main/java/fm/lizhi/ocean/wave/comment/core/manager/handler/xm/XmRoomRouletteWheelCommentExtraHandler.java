package fm.lizhi.ocean.wave.comment.core.manager.handler.xm;

import fm.lizhi.ocean.wave.comment.core.config.CommentConfig;
import fm.lizhi.ocean.wave.comment.core.constants.WaveCommentType;
import fm.lizhi.ocean.wave.comment.core.convert.XmSpecialActivityCommentConvert;
import fm.lizhi.ocean.wave.comment.core.manager.handler.SpecialActivityCommentExtraHandler;
import fm.lizhi.ocean.wave.comment.core.model.bean.RoomRouletteWheelCommentExtra;
import fm.lizhi.ocean.wave.comment.core.model.vo.SpecialActivityCommentVO;
import fm.lizhi.ocean.wave.comment.core.remote.bean.RoomRouletteWheelCommentCardBean;
import fm.lizhi.ocean.wave.common.util.JsonUtils;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import xm.fm.lizhi.datacenter.comment.pp.bean.RouletteWheelExtraBean;
import xm.fm.lizhi.datacenter.comment.pp.bean.SpecialActivityCommentKafkaBean;

/**
 * 直播间精选评论卡片消息处理器
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class XmRoomRouletteWheelCommentExtraHandler implements SpecialActivityCommentExtraHandler<RoomRouletteWheelCommentCardBean> {

    @Autowired
    private CommentConfig commentConfig;

    @Override
    public RoomRouletteWheelCommentCardBean convertExtraDTO(String json) {
        SpecialActivityCommentKafkaBean specialActivityCommentBean = JsonUtils.fromJsonString(json, SpecialActivityCommentKafkaBean.class);
        RouletteWheelExtraBean rouletteWheelExtraBean = specialActivityCommentBean.getCommentInfo().getRouletteWheelExtraBean();
        if (rouletteWheelExtraBean == null) {
            return null;
        }
        return XmSpecialActivityCommentConvert.I.convertRoomRouletteWheelCommentBean(rouletteWheelExtraBean);
    }

    @Override
    public void processActivityCommentExtra(RoomRouletteWheelCommentCardBean cardBean, SpecialActivityCommentVO commentVO) {
        if (cardBean == null || commentVO.getCommentType() != this.getCommentType()) {
            return;
        }
        //构建处理点唱玩法的评论信息
        RoomRouletteWheelCommentExtra commentExtra = XmSpecialActivityCommentConvert.I.convertRoomRouletteWheelCommentBizExtra(cardBean);
        commentVO.setActivityExtra(commentExtra);
        commentVO.setContent(cardBean.getTitle() == null ? commentConfig.getXm().getRoomRouletteWheelTitle() : cardBean.getTitle());
    }

    @Override
    public int getCommentType() {
        return WaveCommentType.LIVE_ROOM_ROULETTE_WHEEL;
    }

    @Override
    public int getAppId() {
        return BusinessEvnEnum.XIMI.getAppId();
    }

}
