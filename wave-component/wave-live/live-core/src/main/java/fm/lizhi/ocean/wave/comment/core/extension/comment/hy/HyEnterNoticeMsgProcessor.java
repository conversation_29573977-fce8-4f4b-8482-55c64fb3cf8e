package fm.lizhi.ocean.wave.comment.core.extension.comment.hy;

import com.alibaba.fastjson.JSON;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wave.comment.core.config.CommentCommonConfig;
import fm.lizhi.ocean.wave.comment.core.config.CommentConfig;
import fm.lizhi.ocean.wave.comment.core.extension.comment.IEnterNoticeMsgProcessor;
import fm.lizhi.ocean.wave.comment.core.extension.medal.*;
import fm.lizhi.ocean.wave.comment.core.extension.medal.v2.*;
import fm.lizhi.ocean.wave.comment.core.model.dto.EnterNoticeDTO;
import fm.lizhi.ocean.wave.comment.core.model.dto.HyEnterNoticeExtraDTO;
import fm.lizhi.ocean.wave.comment.core.model.vo.EnterMsgVO;
import fm.lizhi.ocean.wave.comment.core.model.vo.MsgUserVO;
import fm.lizhi.ocean.wave.comment.core.remote.bean.EnterNoticeEntry;
import fm.lizhi.ocean.wave.comment.core.remote.result.UserEnterRoomResult;
import fm.lizhi.ocean.wave.comment.core.remote.service.IPayRateServiceRemote;
import fm.lizhi.ocean.wave.common.auto.route.common.remote.MyAutowired;
import fm.lizhi.ocean.wave.common.util.ContainsUtils;
import fm.lizhi.ocean.wave.common.util.RpcResult;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wave.user.api.VipPrivilegeService;
import fm.lizhi.ocean.wave.user.constant.WaveNoblePrivilegeType;
import fm.lizhi.ocean.wave.user.param.VipPrivilegeHideParam;
import fm.lizhi.ocean.wave.user.result.VipPrivilegeHideResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * 黑叶进房消息处理器
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class HyEnterNoticeMsgProcessor implements IEnterNoticeMsgProcessor {

    @Autowired
    private VipPrivilegeService vipPrivilegeService;

    @Autowired
    private CommentConfig commentConfig;

    @Autowired
    private HyVipLevelMedalV2Node hyVipLevelMedalNode;

    @Autowired
    private HyFreshUserMedalV2Node freshUserMedalNode;

    @Autowired
    private WealthMedalV2Node wealthMedalNode;

    @Autowired
    private HyAnchorLevelMedalV2Node anchorLevelMedalNode;

    @Autowired
    private HyUserMedalListV2Node userMedalListNode;

    @MyAutowired
    private IPayRateServiceRemote payRateServiceRemote;

    @Override
    public BusinessEvnEnum getBusinessEnv() {
        return BusinessEvnEnum.HEI_YE;
    }

    @Override
    public MsgUserVO<?> buildUserVoAndAdapterExtra(EnterNoticeDTO enterNoticeEntry) {
        return new MsgUserVO<>();
    }

    @Override
    public CommentCommonConfig getCommentConfig() {
        return commentConfig.getHy();
    }

    /**
     * 过滤进房公告特效
     *
     * @param enterNoticeEntries 用户ID
     * @return 过滤后的进房公告
     */
    @Override
    public List<EnterNoticeDTO> filterHideEnterNotice(List<EnterNoticeDTO> enterNoticeEntries) {
        try {
            if (CollectionUtils.isNotEmpty(enterNoticeEntries)) {
                List<EnterNoticeDTO> filterRes = new ArrayList<>(enterNoticeEntries.size());
                for (EnterNoticeDTO enterNoticeEntry : enterNoticeEntries) {
                    //隐藏的就不显示了
                    if (!isHideEnterNoticePrivilege(enterNoticeEntry.getUserId())) {
                        filterRes.add(enterNoticeEntry);
                    }
                }
                return filterRes;
            }
            return enterNoticeEntries;
        } catch (Exception e) {
            log.error("filterHideEnterNotice error", e);
            return enterNoticeEntries;
        }
    }

    @Override
    public List<ICommentV2MedalNode> getBuildEnterNoticeMedalNodes() {
        List<ICommentV2MedalNode> nodes = new ArrayList<>(16);
        //不同的业务需要的勋章节点不同，且顺序不同
        List<Integer> medalShowSort = JSON.parseArray(commentConfig.getHy().getPayRateMedalShowSortJSONArr(), Integer.class);
        if (CollectionUtils.isEmpty(medalShowSort)) {
            log.warn("getBuildEnterNoticeMedalNodes medalShowSort is empty");
            return nodes;
        }
        // 贵族勋章
        nodes.add(hyVipLevelMedalNode);

        boolean isExistMedalListNode = false;
        for (Integer medal : medalShowSort) {
            switch (medal) {
                case 1:
                    nodes.add(freshUserMedalNode);
                    break;
                case 2:
                    nodes.add(wealthMedalNode);
                    break;
                default:
                    nodes.add(userMedalListNode);
                    isExistMedalListNode = true;
                    break;
            }
            //用户勋章列表会很多个， 但是放一个节点就好了，剩下的直接在节点处理里操作
            if (isExistMedalListNode) {
                break;
            }
        }
        nodes.add(anchorLevelMedalNode);
        return nodes;
    }

    @Override
    public void setShowButton(long liveId, long userId, EnterNoticeDTO enterNoticeEntry, EnterMsgVO enterNoticeVO) {
        HyEnterNoticeExtraDTO hyEnterNoticeExtraDTO = enterNoticeEntry.getHyEnterNoticeExtraDTO();
        if (hyEnterNoticeExtraDTO.getShowButton() == null || !hyEnterNoticeExtraDTO.getShowButton()) {
            return;
        }

        if (!commentConfig.getHy().isWelcomeCountLimitSwitch()) {
            //关闭限制，直接显示
            enterNoticeVO.setIsShowButton(true);
            return;
        }
        Result<UserEnterRoomResult> result = payRateServiceRemote.getUserRoom(enterNoticeEntry.getUserId(), liveId);
        int rCode = result.rCode();
        log.info("EnterNoticeCacheManager buildEnterLiveRoomNotices rCode={},userId={},liveId={},isShowButton={}", rCode, enterNoticeEntry.getUserId(), liveId, hyEnterNoticeExtraDTO.getShowButton());
        //查询成功时，且返回的展示按钮值为true，则展示按钮
        enterNoticeVO.setIsShowButton(enterNoticeEntry.getHyEnterNoticeExtraDTO().getShowButton());
    }

    @Override
    public boolean isCanGetEnterNotice(long liveId) {
        return commentConfig.getHy().isEnterNoticeOn() && !ContainsUtils.contains(commentConfig.getHy().getBigStarLiveLiveIds(), liveId);
    }

    @Override
    public boolean isBigStarNjHideMount(long njId) {
        return false;
    }


    /**
     * 是否隐藏进房公告
     *
     * @param userId 用户ID
     * @return true: 隐藏，false: 不隐藏
     */
    private boolean isHideEnterNoticePrivilege(long userId) {
        VipPrivilegeHideParam param = VipPrivilegeHideParam.builder().userId(userId).privilege(WaveNoblePrivilegeType.ENTER_HIDDEN).build();
        Result<VipPrivilegeHideResult> result = vipPrivilegeService.isPrivilegeHideByCache(param);
        if (result.rCode() != 0) {
            log.warn("isHideEnterNoticePrivilege error, userId: {}, rCode: {}", userId, result.rCode());
            return false;
        }
        return result.target().isStatus();
    }
}
