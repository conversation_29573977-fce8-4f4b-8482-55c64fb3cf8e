package fm.lizhi.ocean.wave.comment.core.extension.medal.v2;

import com.google.common.collect.Lists;
import fm.lizhi.ocean.wave.comment.core.config.CommentCommonConfig;
import fm.lizhi.ocean.wave.comment.core.extension.medal.bean.GenMedalInfoV2Context;
import fm.lizhi.ocean.wave.comment.core.model.vo.BadgeImageVO;
import fm.lizhi.ocean.wave.comment.core.remote.bean.FreshUser;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;

import static fm.lizhi.ocean.wave.comment.core.extension.medal.bean.GenMedalInfoV2Context.MedalShowArea.COMMENT_AREA;
import static fm.lizhi.ocean.wave.comment.core.extension.medal.bean.GenMedalInfoV2Context.MedalShowArea.ENTER_NOTICE_AREA;

/**
 * 新用户勋章节点(pp、xm)
 * <AUTHOR>
 */
@Slf4j
@Component
public class FreshUserMedalV2Node implements ICommentV2MedalNode {

    @Override
    public Optional<List<BadgeImageVO>> buildMedalImageInfo(GenMedalInfoV2Context context) {
        int medalShowArea = context.getMedalShowArea();
        CommentCommonConfig commentConfig = context.getCommentConfig();
        FreshUser freshUser = null;
        if (medalShowArea == COMMENT_AREA.getArea()) {
            //评论的新用户信息是在发送评论时就有的
            freshUser = context.getComment().getFreshUser();
        } else if (medalShowArea == ENTER_NOTICE_AREA.getArea()) {
            //进房公告的新用户信息，需要在构建时查询
            freshUser = context.getEnterNoticeEntry().getFreshUserEntry();
        }

        //不符合要求，结束
        if (freshUser == null || !freshUser.isIs()) {
            return Optional.empty();
        }

        BadgeImageVO badgeImageVO = new BadgeImageVO();

        //加入新用户图标
        badgeImageVO.setBadgeUrl(commentConfig.getFreshUserIconUrl());
        badgeImageVO.setBadgeAspect(commentConfig.getFreshUserIconAspect());
        return Optional.of(Lists.newArrayList(badgeImageVO));
    }
}
