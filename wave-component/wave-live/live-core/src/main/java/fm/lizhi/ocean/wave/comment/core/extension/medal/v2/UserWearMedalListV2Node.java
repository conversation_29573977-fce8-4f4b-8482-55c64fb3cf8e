package fm.lizhi.ocean.wave.comment.core.extension.medal.v2;

import fm.lizhi.ocean.wave.comment.core.extension.comment.ICommentMedalNodeProcessor;
import fm.lizhi.ocean.wave.comment.core.extension.medal.bean.GenMedalInfoV2Context;
import fm.lizhi.ocean.wave.comment.core.model.dto.TransientCommentDTO;
import fm.lizhi.ocean.wave.comment.core.model.vo.BadgeImageVO;
import fm.lizhi.ocean.wave.comment.core.remote.bean.SimpleMedal;
import fm.lizhi.ocean.wave.common.extension.ProcessorV2Factory;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * 用户佩戴的勋章列表节点(目前HY才有)
 * <AUTHOR>
 */
@Component
public class UserWearMedalListV2Node implements ICommentV2MedalNode {

    @Autowired
    private ProcessorV2Factory factory;

    @Override
    public Optional<List<BadgeImageVO>> buildMedalImageInfo(GenMedalInfoV2Context context) {
        TransientCommentDTO comment = context.getComment();
        ICommentMedalNodeProcessor processor = factory.getProcessor(ICommentMedalNodeProcessor.class);
        List<SimpleMedal> wearMedalList = processor.getWearMedalList(comment);
        if (comment == null || CollectionUtils.isEmpty(wearMedalList)) {
            return Optional.empty();
        }

        // 主播勋章列表
        List<BadgeImageVO> medalList = new ArrayList<>();
        for (SimpleMedal simpleMedal : wearMedalList) {
            BadgeImageVO badgeImageVO = new BadgeImageVO();
            badgeImageVO.setBadgeUrl(
                    StringUtils.isNotBlank(simpleMedal.getLongImage())
                            ? simpleMedal.getLongImage() : simpleMedal.getImage());
            badgeImageVO.setBadgeAspect(simpleMedal.getAspect());
            medalList.add(badgeImageVO);
        }
        return Optional.of(medalList);
    }
}
