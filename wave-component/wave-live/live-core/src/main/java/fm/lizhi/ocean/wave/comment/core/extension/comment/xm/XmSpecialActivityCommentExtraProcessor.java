package fm.lizhi.ocean.wave.comment.core.extension.comment.xm;

import fm.lizhi.ocean.wave.comment.core.convert.XmSpecialActivityCommentConvert;
import fm.lizhi.ocean.wave.comment.core.extension.comment.ISpecialActivityCommentExtraProcessor;
import fm.lizhi.ocean.wave.comment.core.model.bean.FreshUserInterestCommentExtra;
import fm.lizhi.ocean.wave.comment.core.model.bean.RoomPlayListCommentCardCommentExtra;
import fm.lizhi.ocean.wave.comment.core.model.bean.RoomRouletteWheelCommentExtra;
import fm.lizhi.ocean.wave.comment.core.model.bean.xm.XmSongSpecialActivityCommentExtra;
import fm.lizhi.ocean.wave.comment.core.remote.bean.FreshUserInterestBean;
import fm.lizhi.ocean.wave.comment.core.remote.bean.RoomPlaylistCommentCardBean;
import fm.lizhi.ocean.wave.comment.core.remote.bean.RoomRouletteWheelCommentCardBean;
import fm.lizhi.ocean.wave.comment.core.remote.bean.SongInfo;
import fm.lizhi.ocean.wave.common.config.CommonProviderConfig;
import fm.lizhi.ocean.wave.common.util.JsonUtils;
import fm.lizhi.ocean.wave.common.util.UrlUtils;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import xm.fm.lizhi.datacenter.comment.pp.bean.*;

/**
 * 互动活动评论差异化信息处理器
 *
 * <AUTHOR>
 */
@Component
public class XmSpecialActivityCommentExtraProcessor implements ISpecialActivityCommentExtraProcessor {

    @Autowired
    private CommonProviderConfig commonProviderConfig;

    @Override
    public SongInfo convertSongInfo(String json) {
        SpecialActivityCommentKafkaBean specialActivityCommentBean = JsonUtils.fromJsonString(json, SpecialActivityCommentKafkaBean.class);
        OrderSongInfoBean orderSongInfoBean = specialActivityCommentBean.getCommentInfo().getOrderSongInfoBean();
        if (orderSongInfoBean == null) {
            return null;
        }

        return XmSpecialActivityCommentConvert.I.convertSongInfo(orderSongInfoBean);
    }

    @Override
    public RoomPlaylistCommentCardBean convertRoomPlaylistCommentCardBean(String json) {
        SpecialActivityCommentKafkaBean specialActivityCommentBean = JsonUtils.fromJsonString(json, SpecialActivityCommentKafkaBean.class);
        xm.fm.lizhi.datacenter.comment.pp.bean.RoomPlaylistCommentCardBean roomPlaylistBean = specialActivityCommentBean.getCommentInfo().getRoomPlaylistBean();
        if (roomPlaylistBean == null) {
            return null;
        }
        return XmSpecialActivityCommentConvert.I.convertRoomPlayListCommentBean(roomPlaylistBean);
    }

    @Override
    public XmSongSpecialActivityCommentExtra buildSongCommentBizExtra(SongInfo songInfo) {
        String cdnHost = commonProviderConfig.getBusinessConfig(BusinessEvnEnum.XIMI.getAppId()).getCdnHost();
        XmSongSpecialActivityCommentExtra commentExtra = XmSpecialActivityCommentConvert.I.convertSongCommentBizExtra(songInfo);
        commentExtra.setSongCover(UrlUtils.addCdnHost(cdnHost, songInfo.getSongCover()));
        commentExtra.setAvatar(UrlUtils.addCdnHost(cdnHost, songInfo.getAvatar()));
        return commentExtra;
    }

    @Override
    public RoomPlayListCommentCardCommentExtra buildRoomPlayListCommentBizExtra(RoomPlaylistCommentCardBean cardBean) {
        return XmSpecialActivityCommentConvert.I.convertRoomPlayListCommentBizExtra(cardBean);
    }

    @Override
    public RoomRouletteWheelCommentCardBean convertRoomRouletteWheelCommentBean(String json) {
        SpecialActivityCommentKafkaBean specialActivityCommentBean = JsonUtils.fromJsonString(json, SpecialActivityCommentKafkaBean.class);
        RouletteWheelExtraBean rouletteWheelExtraBean = specialActivityCommentBean.getCommentInfo().getRouletteWheelExtraBean();
        if (rouletteWheelExtraBean == null) {
            return null;
        }
        return XmSpecialActivityCommentConvert.I.convertRoomRouletteWheelCommentBean(rouletteWheelExtraBean);
    }

    @Override
    public RoomRouletteWheelCommentExtra buildRoomRouletteWheelCommentExtra(RoomRouletteWheelCommentCardBean cardBean) {
        return XmSpecialActivityCommentConvert.I.convertRoomRouletteWheelCommentBizExtra(cardBean);
    }

    @Override
    public FreshUserInterestBean convertFreshUserInterestCommentBean(String json) {
        SpecialActivityCommentKafkaBean specialActivityCommentBean = JsonUtils.fromJsonString(json, SpecialActivityCommentKafkaBean.class);
        FreshUserInterestInfoBean interestInfoBean = specialActivityCommentBean.getCommentInfo().getFreshUserInterestInfoBean();
        if (interestInfoBean == null) {
            return null;
        }
        return XmSpecialActivityCommentConvert.I.convertFreshUserInterestCommentBean(interestInfoBean);
    }

    @Override
    public FreshUserInterestCommentExtra buildFreshUserInterestCommentExtra(FreshUserInterestBean freshUserInterestBean) {
        return XmSpecialActivityCommentConvert.I.convertFreshUserInterestCommentBizExtra(freshUserInterestBean);
    }

    @Override
    public BusinessEvnEnum getBusinessEnv() {
        return BusinessEvnEnum.XIMI;
    }
}
