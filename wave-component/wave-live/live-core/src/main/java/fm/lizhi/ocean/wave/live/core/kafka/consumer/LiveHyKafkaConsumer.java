package fm.lizhi.ocean.wave.live.core.kafka.consumer;

import fm.lizhi.common.kafka.common.strategy.AutoOffsetReset;
import fm.lizhi.common.kafka.ioc.api.annotation.KafkaHandler;
import fm.lizhi.common.kafka.ioc.api.annotation.KafkaListener;
import fm.lizhi.common.kafka.ioc.api.annotation.SubscriptionOn;
import fm.lizhi.common.kafka.ioc.api.strategy.annotation.AutoOffsetResetStrategy;
import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.live.room.hy.constant.AnchorBehaviorMsg;
import fm.lizhi.live.room.hy.constant.AnchorBehaviorType;
import fm.lizhi.live.room.hy.model.BanCommentMsg;
import fm.lizhi.live.room.hy.model.KickOutMsg;
import fm.lizhi.live.room.hy.model.RoleChangeMsg;
import fm.lizhi.ocean.wave.comment.core.manager.CommentCacheCleanManager;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wave.common.util.KafkaMsgUtils;
import fm.lizhi.ocean.wave.live.core.manager.LivePushManager;
import fm.lizhi.ocean.wave.live.core.model.bean.LivePushBean;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * Description:
 * <p>
 * 黑叶kafka消息消费者
 * - 默认无异常时表示消费成功，抛出异常表示消费失败，消费失败的消息会重新消费，直到消费成功。
 * - 默认使用的json消息反序列化器，未声明的字段会抛异常，如希望能够忽略未声明的字段，请选择使用自定义的反序列化器，或者通过String接收再反序列化。
 *
 * <AUTHOR>
 * Created in  2023/5/19
 */
@Slf4j
@Component
@KafkaListener(clusterNamespace = "hy-kafka250-bootstrap-server")
@SubscriptionOn(enable = "${hy.kafka.consumer.enable}")
public class LiveHyKafkaConsumer {


    @Autowired
    private LivePushManager livePushManager;

    @Autowired
    private CommentCacheCleanManager commentCacheCleanManager;

    /**
     * 直播信息变更推送
     * @param body
     */
    @KafkaHandler(topic = "lz_topic_hy_live_push_msg",
            group = "lz_topic_hy_live_push_msg_wave_group")
    @AutoOffsetResetStrategy(AutoOffsetReset.Latest)
    public void handleLivePush(String body){
        String msg = null;
        try {
            msg = KafkaMsgUtils.removeKafkaMsgCICDHead(body);
            LivePushBean livePushBean = JsonUtil.loads(msg, LivePushBean.class);
            if (livePushBean.getLiveId() == null) {
                log.warn("hy.handleLivePush liveId is null, msg:{}", msg);
                return;
            }

            livePushManager.pushLive(livePushBean.getLiveId()
                    , livePushBean.getBizType()
                    , BusinessEvnEnum.HEI_YE.getAppId());
        } catch (Exception e) {
            log.error("hy.handleLivePush json parse error,  msg:{}", msg, e);
        }
    }

    /**
     * hy主播行为消息
     *
     * @param body 消息体
     */
    @KafkaHandler(topic = "lz_hy_topic_live_room_anchor_behavior_message",
            group = "lz_ocean_wave_live_anchor_behavior_group")
    @AutoOffsetResetStrategy(AutoOffsetReset.Latest)
    public void handleAnchorBehaviorMsg(String body) {
        String msg = null;
        try {
            msg = KafkaMsgUtils.removeKafkaMsgCICDHead(body);
            AnchorBehaviorMsg anchorBehaviorMsg;
            anchorBehaviorMsg = JsonUtil.loads(msg, AnchorBehaviorMsg.class);
            AnchorBehaviorType behaviorType = AnchorBehaviorType.from(anchorBehaviorMsg.getType());

            if (behaviorType == null) {
                log.warn("hy.anchorBehaviorMsg type, type:{}", anchorBehaviorMsg.getType());
                return;
            }
            log.info("hy.anchorBehaviorMsg type:{}, userId:{}, liveId:{}", behaviorType, anchorBehaviorMsg.getUserId(), anchorBehaviorMsg.getLive().getLiveId());

            int appId = BusinessEvnEnum.HEI_YE.getAppId();
            long liveId = anchorBehaviorMsg.getLive().getLiveId();
            long liveRoomId = anchorBehaviorMsg.getLive().getLiveRoomId();
            livePushManager.pushLiveSync(liveRoomId, liveId, appId);
            //清理评论缓存
            if (behaviorType == AnchorBehaviorType.CLOSE_LIVE) {
                commentCacheCleanManager.setCommentCacheExpireTime(appId, liveId);
            }
        } catch (Exception e) {
            log.error("hy.anchorBehaviorMsg json parse error,  msg:{}, orgMsg:{}", msg, body, e);
        }
    }

    /**
     * 评论禁言消息
     *
     * @param body
     */
    @KafkaHandler(topic = "lz_topic_hy_ban_comment_msg",
            group = "lz_ocean_wave_live_ban_comment_group")
    @AutoOffsetResetStrategy(AutoOffsetReset.Latest)
    public void handleBanCommentMsg(String body) {
        String msg = null;
        try {
            msg = KafkaMsgUtils.removeKafkaMsgCICDHead(body);
            BanCommentMsg banCommentMsg = JsonUtil.loads(msg, BanCommentMsg.class);
            long liveRoomId = banCommentMsg.getLiveRoomId();
            int operateType = banCommentMsg.getOperateType();
            long userId = banCommentMsg.getUserId();
            log.info("banCommentMsg liveRoomId:{}, userId:{}, operateType:{}", liveRoomId, userId, operateType);
            livePushManager.pushBanComment(liveRoomId, userId, operateType, BusinessEvnEnum.HEI_YE.getAppId());
        } catch (Exception e) {
            log.error("banCommentMsg json parse error,  msg:{}, orgMsg:{}", msg, body, e);
        }
    }

    /**
     * 处理踢出/取消踢出用户消息
     *
     * @param body
     */
    @KafkaHandler(topic = "lz_topic_hy_kickout_user_msg",
            group = "lz_ocean_wave_live_kickout_user_group")
    @AutoOffsetResetStrategy(AutoOffsetReset.Latest)
    public void handleKickOutMsg(String body) {
        String msg = null;
        try {
            msg = KafkaMsgUtils.removeKafkaMsgCICDHead(body);
            KickOutMsg kickOutMsg = JsonUtil.loads(msg, KickOutMsg.class);
            long liveRoomId = kickOutMsg.getLiveRoomId();
            long tgtUserIg = kickOutMsg.getTgtUserIg();
            int status = kickOutMsg.getStatus();
            log.info("kickOutMsg liveRoomId:{}, tgtUserIg:{}, status:{}", liveRoomId, tgtUserIg, status);
            livePushManager.pushKickOut(liveRoomId, tgtUserIg, status, BusinessEvnEnum.HEI_YE.getAppId());
        } catch (Exception e) {
            log.error("kickOutMsg json parse error,  msg:{}, orgMsg:{}", msg, body, e);
        }

    }

    @KafkaHandler(topic = "lz_topic_hy_role_change_msg",
            group = "lz_ocean_wave_live_role_change_group")
    @AutoOffsetResetStrategy(AutoOffsetReset.Latest)
    public void roleChange(String body) {
        String msg = null;
        try {
            msg = KafkaMsgUtils.removeKafkaMsgCICDHead(body);
            RoleChangeMsg roleChangeMsg = JsonUtil.loads(msg, RoleChangeMsg.class);
            long liveRoomId = roleChangeMsg.getLiveRoomId();
            long userId = roleChangeMsg.getUserId();
            int role = roleChangeMsg.getRole();
            int operateType = roleChangeMsg.getOperateType();
            log.info("roleChangeMsg liveRoomId:{}, userId:{}, role:{}, operateType:{}", liveRoomId, userId, role, operateType);
            livePushManager.pushRoleChange(liveRoomId, userId, role, operateType, BusinessEvnEnum.HEI_YE.getAppId());
        } catch (Exception e) {
            log.error("roleChangeMsg json parse error, msg:{}, orgMsg:{}", msg, body, e);
        }
    }


//    /**
//     * hy 更改直播背景消息
//     *
//     * @param body 消息体
//     */
//    @KafkaHandler(topic = "lz_hy_topic_live_background_change",
//            group = "lz_ocean_wave_live_background_change_group")
//    @AutoOffsetResetStrategy(AutoOffsetReset.Latest)
//    public void handleLiveBackGround(String body) {
//        String msg = null;
//        try {
//            msg = KafkaMsgUtils.removeKafkaMsgCICDHead(body);
//            int appId = BusinessEvnEnum.HEI_YE.getAppId();
//            livePushManager.pushLiveSync(liveRoomId, liveId, appId);
//        } catch (Exception e) {
//            log.error("hy.anchorBehaviorMsg json parse error,  msg:{}, orgMsg:{}", msg, body, e);
//        }
//    }



}
