package fm.lizhi.ocean.wave.comment.core.extension.medal.v2;

import fm.lizhi.ocean.wave.comment.core.extension.medal.bean.GenMedalInfoV2Context;
import fm.lizhi.ocean.wave.comment.core.model.vo.BadgeImageVO;
import fm.lizhi.ocean.wave.comment.core.remote.bean.Medal;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * 用户拥有的勋章列表节点
 * <AUTHOR>
 */
@Component
public class UserMedalListV2Node implements ICommentV2MedalNode {

    @Override
    public Optional<List<BadgeImageVO>> buildMedalImageInfo(GenMedalInfoV2Context context) {
        List<Medal> medals = context.getComment().getMedal();
        if (CollectionUtils.isEmpty(medals)) {
            return Optional.empty();
        }

        // 勋章列表
        List<BadgeImageVO> medalList = new ArrayList<>();
        for (Medal medal : medals) {
            if (StringUtils.isBlank(medal.getCover())) {
                continue;
            }
            BadgeImageVO badgeImageVO = new BadgeImageVO();
            badgeImageVO.setBadgeUrl(medal.getCover());
            badgeImageVO.setBadgeAspect(medal.getAspect());
            medalList.add(badgeImageVO);
        }
        return Optional.of(medalList);
    }
}
