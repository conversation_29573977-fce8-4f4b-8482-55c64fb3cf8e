package fm.lizhi.ocean.wave.comment.core.remote.service.hy;

import com.alibaba.dubbo.common.utils.StringUtils;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.commons.service.client.rcode.GeneralRCode;
import fm.lizhi.ocean.wave.comment.core.config.CommentConfig;
import fm.lizhi.ocean.wave.comment.core.constants.CommentTypeMapping;
import fm.lizhi.ocean.wave.comment.core.constants.WaveCommentType;
import fm.lizhi.ocean.wave.comment.core.model.dto.HyCommentExtraDTO;
import fm.lizhi.ocean.wave.comment.core.model.dto.TransientCommentDTO;
import fm.lizhi.ocean.wave.comment.core.model.vo.ComeSourceVO;
import fm.lizhi.ocean.wave.comment.core.model.vo.TailEffectVo;
import fm.lizhi.ocean.wave.comment.core.remote.adapter.hy.HyCommentQueryAdapter;
import fm.lizhi.ocean.wave.comment.core.remote.bean.AnthorLevel;
import fm.lizhi.ocean.wave.comment.core.remote.bean.MultiContentItem;
import fm.lizhi.ocean.wave.comment.core.remote.bean.SimpleMedal;
import fm.lizhi.ocean.wave.comment.core.remote.param.GetCommentWithServerTimeParam;
import fm.lizhi.ocean.wave.comment.core.remote.result.GetLiveCommentResult;
import fm.lizhi.ocean.wave.comment.core.remote.service.ILiveCommentQueryServiceRemote;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wave.server.common.util.ContextUtils;
import fm.lizhi.ocean.wave.user.api.UserService;
import fm.lizhi.ocean.wave.user.result.GetTailEffectResult;
import hy.fm.lizhi.datacenter.comment.pp.api.TransientCommentService;
import hy.fm.lizhi.datacenter.comment.pp.protocol.CommentProto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 直播评论接口适配
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class HyLiveCommentQueryServiceRemote implements ILiveCommentQueryServiceRemote {

    @Autowired
    private TransientCommentService transientCommentService;

    @Autowired
    private UserService userService;

    @Autowired
    private CommentConfig commentConfig;

    @Override
    public boolean support(BusinessEvnEnum evnEnum) {
        return BusinessEvnEnum.HEI_YE.equals(evnEnum);
    }

    /**
     * 获取评论列表
     *
     * @param param 获取评论列表参数
     * @return 评论列表
     */
    @Override
    public Result<GetLiveCommentResult> getCommentWithServerTime(GetCommentWithServerTimeParam param) {
        Result<CommentProto.ResponseGetTransientComment> result = transientCommentService.getCommentWithServerTime(
                param.getLiveId(),
                param.getStartTime(), param.getEndTime(),
                param.getCount(), param.isNjFirst(),
                param.isFirstEntry());

        if (result.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
            log.error("getCommentWithServerTime failed, rCode={}`param={}", result.rCode(), param);
            return new Result<>(result.rCode(), null);
        }

        CommentProto.ResponseGetTransientComment responseGetTransientComment = result.target();
        List<CommentProto.TransientComment> transientCommentList =
                responseGetTransientComment.getTransientCommentList();
        List<TransientCommentDTO> transientComments = new ArrayList<>();
        for (CommentProto.TransientComment transientComment : transientCommentList) {
            if (isSpecialActivityComment(transientComment.getCommentType())) {
                continue;
            }
            //主播等级勋章
            CommentProto.AnthorLevel anthorLevel = transientComment.hasAnthorLevel() ? transientComment.getAnthorLevel() : null;
            //数据转换
            TransientCommentDTO transientCommentDTO = HyCommentQueryAdapter.I.convertComment(transientComment);
            // 主播等级
            AnthorLevel anchorLevelBean = HyCommentQueryAdapter.I.convertAnthorLevel(anthorLevel);
            // 多元素评论
            List<MultiContentItem> multiContentItems = HyCommentQueryAdapter.I.generateMultiContentItem(transientComment.getMultiContentItemsList());
            // 来源
            ComeSourceVO comeSourceVO = HyCommentQueryAdapter.I.convertComeSource(transientComment);
            // 可见用户
            List<Long> toUserIds = generateToUserIdList(transientComment.getToUserList());
            List<SimpleMedal> simpleMedals = HyCommentQueryAdapter.I.convertSimpleMedals(transientComment.getSimpleMedalList());

            // 评论类型转换
            int commentType = CommentTypeMapping.getWaveCommentType(transientComment.getCommentType(),
                    BusinessEvnEnum.from(ContextUtils.getContext().getHeader().getAppId()),
                    transientComment.getCommentTypeExtension());
            //如果评论类型是系统评论，且触发系统评论的用户ID存在，则使用触发者的头像
            long userId = commentType == WaveCommentType.SYSTEM_NOTICE_COMMENT && transientComment.getOperatorUserId() > 0 ?
                    transientComment.getOperatorUserId() : transientComment.getUserId();
            // 尾部特效
            TailEffectVo tailEffectVo = generateTailEffect(userId);

            HyCommentExtraDTO hyCommentExtraDTO = new HyCommentExtraDTO()
                    .setAnthorLevel(anchorLevelBean)
                    .setContentItems(multiContentItems)
                    .setComeSource(comeSourceVO)
                    .setSimpleMedals(simpleMedals)
                    .setTailEffect(tailEffectVo);
            transientCommentDTO.setHyCommentExtra(hyCommentExtraDTO);
            transientCommentDTO.setCommentType(commentType);
            transientCommentDTO.setUserId(userId);
            transientCommentDTO.setToUser(toUserIds);
            transientComments.add(transientCommentDTO);
        }
        GetLiveCommentResult commentResult = GetLiveCommentResult.builder()
                .transientComments(transientComments)
                .commentServerTime(Math.min(responseGetTransientComment.getCommentServerTime(), param.getEndTime()))
                .lastPage(param.getCount() > transientComments.size())
                .build();
        return new Result<>(result.rCode(), commentResult);
    }


    private List<Long> generateToUserIdList(List<CommentProto.ToUser> toUserList) {
        List<Long> toUserIds = new ArrayList<>();
        for (CommentProto.ToUser toUser : toUserList) {
            toUserIds.add(toUser.getUserId());
        }
        return toUserIds;
    }


    private TailEffectVo generateTailEffect(long userId) {
        Result<GetTailEffectResult> result = userService.getTailEffect(userId);
        if (result.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS || result.target() == null) {
            return null;
        }
        GetTailEffectResult target = result.target();
        return TailEffectVo.builder()
                .effectUrl(target.getTailEffectUrl())
                .id(target.getId())
                .build();
    }

    /**
     * 是否是互动玩法评论
     *
     * @param commentType 评论类型
     * @return true：是，false：否
     */
    private boolean isSpecialActivityComment(int commentType) {
        List<Integer> specialActivityCommentTypes = commentConfig.getHy().getSpecialActivityCommentTypes();
        if (specialActivityCommentTypes.isEmpty()) {
            return false;
        }

        return specialActivityCommentTypes.contains(commentType);
    }


}
