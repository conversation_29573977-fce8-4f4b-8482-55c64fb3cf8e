package fm.lizhi.ocean.wave.comment.core.remote.result;

import fm.lizhi.ocean.wave.comment.core.model.dto.EnterNoticeDTO;
import fm.lizhi.ocean.wave.comment.core.remote.bean.EnterNoticeEntry;
import fm.lizhi.ocean.wave.comment.core.remote.bean.TransientComment;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 获取进房消息结果
 *
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class GetEnterNoticeMsgResult {
    /**
     * 进房消息
     */
    private List<EnterNoticeDTO> entries;
    /**
     * 查询的结束时间
     */
    private long endTime;
}
