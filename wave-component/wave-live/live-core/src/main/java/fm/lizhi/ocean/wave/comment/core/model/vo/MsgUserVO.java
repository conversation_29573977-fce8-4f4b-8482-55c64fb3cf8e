package fm.lizhi.ocean.wave.comment.core.model.vo;

import fm.lizhi.ocean.wave.comment.core.model.bean.MsgUserExtraInfo;
import fm.lizhi.ocean.wave.user.bean.UserAvatarWidget;
import lombok.Data;

import java.util.List;

/**
 * 用户信息VO
 *
 * <AUTHOR>
 */
@Data
public class MsgUserVO<T extends MsgUserExtraInfo> {
    /**
     * 用户id
     */
    private String userId;
    /**
     * 用户名
     */
    private String userName;
    /**
     * 用户头像
     */
    private String userAvatar;
    /**
     * 用户icon列表
     */
    private List<BadgeImageVO> userIcons;

    /**
     * 用户的头像框
     */
    private UserAvatarWidget userAvatarWidget;

    /**
     * 业务扩展字段，某个业务独有的字段
     */
    private T bizOtherExtra;
}
