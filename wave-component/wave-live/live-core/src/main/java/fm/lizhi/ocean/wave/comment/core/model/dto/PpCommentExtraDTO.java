package fm.lizhi.ocean.wave.comment.core.model.dto;

import fm.lizhi.ocean.wave.comment.core.remote.bean.*;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class PpCommentExtraDTO {

    /**
     * 用户佩戴的勋章集合
     */
    private List<SimpleMedal> simpleMedals;
    /**
     * 贵族等级
     */
    private VipLevel vipLevel;

    /**
     * 大客户勋章，从接口查询出来
     */
    private VipMedal vipMedal;

    /**
     * 成长关系列表，发评论的用户和多人的成长关系，先带着走，后面再选出和查询的用户的成长关系，设置到growRelationLevel字段
     */
    private List<GrowRelationLevel> growRelationLevelList;

    /**
     * 守护勋章，PP独有
     */
    private GuardMedal guardMedal;

    /**
     * 蒙面状态
     */
    private int maskStatus;

    /**
     * 成长关系
     */
    private GrowRelationLevel growRelationLevel;

}
