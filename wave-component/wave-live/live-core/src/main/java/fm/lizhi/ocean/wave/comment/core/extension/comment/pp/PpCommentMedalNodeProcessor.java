package fm.lizhi.ocean.wave.comment.core.extension.comment.pp;

import fm.lizhi.ocean.wave.comment.core.extension.comment.ICommentMedalNodeProcessor;
import fm.lizhi.ocean.wave.comment.core.model.dto.EnterNoticeDTO;
import fm.lizhi.ocean.wave.comment.core.model.dto.TransientCommentDTO;
import fm.lizhi.ocean.wave.comment.core.remote.bean.SimpleMedal;
import fm.lizhi.ocean.wave.comment.core.remote.bean.VipInfoEntry;
import fm.lizhi.ocean.wave.comment.core.remote.bean.VipLevel;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 */
@Component
public class PpCommentMedalNodeProcessor implements ICommentMedalNodeProcessor {
    @Override
    public List<SimpleMedal> getWearMedalList(TransientCommentDTO transientCommentDTO) {
        return transientCommentDTO.getPpCommentExtra().getSimpleMedals();
    }

    @Override
    public VipLevel getVipLevel(TransientCommentDTO transientCommentDTO) {
        return transientCommentDTO.getPpCommentExtra().getVipLevel();
    }

    @Override
    public VipInfoEntry getVipInfoEntry(EnterNoticeDTO enterNoticeEntry) {
        return enterNoticeEntry.getPpEnterNoticeExtraDTO().getVipInfoEntry();
    }

    @Override
    public BusinessEvnEnum getBusinessEnv() {
        return BusinessEvnEnum.PP;
    }
}
