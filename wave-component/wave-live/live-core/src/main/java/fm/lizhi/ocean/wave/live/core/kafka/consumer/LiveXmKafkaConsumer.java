package fm.lizhi.ocean.wave.live.core.kafka.consumer;

import fm.lizhi.common.kafka.common.strategy.AutoOffsetReset;
import fm.lizhi.common.kafka.ioc.api.annotation.KafkaHandler;
import fm.lizhi.common.kafka.ioc.api.annotation.KafkaListener;
import fm.lizhi.common.kafka.ioc.api.annotation.SubscriptionOn;
import fm.lizhi.common.kafka.ioc.api.strategy.annotation.AutoOffsetResetStrategy;
import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.live.cmpt.behavior.msg.anchor.AnchorBehaviorMsg;
import fm.lizhi.live.cmpt.behavior.msg.anchor.AnchorBehaviorType;
import fm.lizhi.live.room.pp.bean.BanCommentMsg;
import fm.lizhi.live.room.pp.bean.RoleChangeMsg;
import fm.lizhi.ocean.wave.comment.core.manager.CommentCacheCleanManager;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wave.common.util.KafkaMsgUtils;
import fm.lizhi.ocean.wave.live.core.manager.LivePushManager;
import fm.lizhi.ocean.wave.platform.api.live.constants.LiveKickOutFlag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import xm.fm.lizhi.pp.bean.kickout.KickOutMsg;

/**
 * <AUTHOR>
 * <p>
 * Description: xm kafka消息消费者
 */
@Slf4j
@Component
@KafkaListener(clusterNamespace = "xm-kafka250-bootstrap-server")
@SubscriptionOn(enable = "${xm.kafka.consumer.enable}")
public class LiveXmKafkaConsumer {
    @Autowired
    private LivePushManager livePushManager;
    @Autowired
    private CommentCacheCleanManager commentCacheCleanManager;

    /**
     * 评论禁言消息
     *
     * @param body
     */
    @KafkaHandler(topic = "lz_topic_xm_ban_comment_msg",
            group = "lz_ocean_wave_live_ban_comment_group")
    @AutoOffsetResetStrategy(AutoOffsetReset.Latest)
    public void handleBanCommentMsg(String body) {
        String msg = null;
        try {
            msg = KafkaMsgUtils.removeKafkaMsgCICDHead(body);
            BanCommentMsg banCommentMsg = JsonUtil.loads(msg, BanCommentMsg.class);
            long liveRoomId = banCommentMsg.getLiveRoomId();
            int operateType = banCommentMsg.getOperateType();
            long userId = banCommentMsg.getUserId();
            log.info("LiveXmKafkaConsumer banCommentMsg liveRoomId={}`userId={}`operateType={}", liveRoomId, userId, operateType);
            livePushManager.pushBanComment(liveRoomId, userId, operateType, BusinessEvnEnum.XIMI.getAppId());
        } catch (Exception e) {
            log.error("LiveXmKafkaConsumer banCommentMsg json parse error,  msg={}`orgMsg={}", msg, body, e);
        }
    }


    /**
     * 角色变化监听
     *
     * @param body 用户角色变化消息
     */
    @KafkaHandler(topic = "lz_topic_xm_role_change_msg",
            group = "lz_ocean_wave_live_role_change_group")
    @AutoOffsetResetStrategy(AutoOffsetReset.Latest)
    public void handleRoleChangeMsg(String body) {
        String msg = null;
        try {
            msg = KafkaMsgUtils.removeKafkaMsgCICDHead(body);
            RoleChangeMsg roleChangeMsg = JsonUtil.loads(msg, RoleChangeMsg.class);
            long liveRoomId = roleChangeMsg.getLiveRoomId();
            long userId = roleChangeMsg.getUserId();
            int role = roleChangeMsg.getRole();
            int operateType = roleChangeMsg.getOperateType();
            log.info("LiveXmKafkaConsumer roleChangeMsg liveRoomId:{}, userId:{}, role:{}, operateType:{}", liveRoomId, userId, role, operateType);
            livePushManager.pushRoleChange(liveRoomId, userId, role, operateType, BusinessEvnEnum.XIMI.getAppId());
        } catch (Exception e) {
            log.error("LiveXmKafkaConsumer roleChangeMsg json parse error, msg:{}, orgMsg:{}", msg, body, e);
        }

    }

    /**
     * 西米主播行为消息
     *
     * @param body 消息体
     */
    @KafkaHandler(topic = "xm_topic_live_room_anchor_behavior_message",
            group = "lz_ocean_wave_live_anchor_behavior_group")
    @AutoOffsetResetStrategy(AutoOffsetReset.Latest)
    public void handleAnchorBehaviorMsg(String body) {
        String msg = null;
        try {
            msg = KafkaMsgUtils.removeKafkaMsgCICDHead(body);
            AnchorBehaviorMsg anchorBehaviorMsg;
            anchorBehaviorMsg = JsonUtil.loads(msg, AnchorBehaviorMsg.class);
            AnchorBehaviorType behaviorType = AnchorBehaviorType.from(anchorBehaviorMsg.getType());

            if (behaviorType == null) {
                log.warn("xm.anchorBehaviorMsg type, type:{}", anchorBehaviorMsg.getType());
                return;
            }
            log.info("xm.anchorBehaviorMsg type:{}, userId:{}, liveId:{}", behaviorType, anchorBehaviorMsg.getUserId(), anchorBehaviorMsg.getLive().getLiveId());

            //直播中不做任何操作，量级太大了
            if (behaviorType == AnchorBehaviorType.LIVE) {
                return;
            }


            int appId = BusinessEvnEnum.XIMI.getAppId();
            long liveId = anchorBehaviorMsg.getLive().getLiveId();
            long liveRoomId = anchorBehaviorMsg.getLive().getLiveRoomId();
            livePushManager.pushLiveSync(liveRoomId, liveId, appId);
            //清理评论缓存
            if (behaviorType == AnchorBehaviorType.CLOSE_LIVE) {
                commentCacheCleanManager.setCommentCacheExpireTime(appId, liveId);
            }
        } catch (Exception e) {
            log.error("xm.anchorBehaviorMsg json parse error,  msg:{}, orgMsg:{}", msg, body, e);
        }
    }


    /**
     * 处理踢出/取消踢出用户消息
     *
     * @param body
     */
    @KafkaHandler(topic = "xm_topic_kick_out_user_msg",
            group = "lz_ocean_wave_live_kickout_user_group")
    @AutoOffsetResetStrategy(AutoOffsetReset.Latest)
    public void handleKickOutMsg(String body) {
        String msg = null;
        try {
            msg = KafkaMsgUtils.removeKafkaMsgCICDHead(body);
            KickOutMsg kickOutMsg = JsonUtil.loads(msg, KickOutMsg.class);
            long liveRoomId = kickOutMsg.getLiveRoomId();
            long targetUserId = kickOutMsg.getTargetUserId();
            log.info("xm kickOutMsg liveRoomId:{}, targetUserId:{}", liveRoomId, targetUserId);
            livePushManager.pushKickOut(liveRoomId, targetUserId, LiveKickOutFlag.KICK_OUT.getFlag(), BusinessEvnEnum.XIMI.getAppId());
        } catch (Exception e) {
            log.error("xm kickOutMsg json parse error,  msg:{}, orgMsg:{}", msg, body, e);
        }
    }


//    /**
//     * xm 更改直播背景消息
//     *
//     * @param body 消息体
//     */
//    @KafkaHandler(topic = "lz_xm_topic_live_background_change",
//            group = "lz_ocean_wave_live_background_change_group")
//    @AutoOffsetResetStrategy(AutoOffsetReset.Latest)
//    public void handleLiveBackGround(String body) {
//        String msg = null;
//        try {
//
//            int appId = BusinessEvnEnum.HEI_YE.getAppId();
//            livePushManager.pushLiveSync(liveRoomId, liveId, appId);
//        } catch (Exception e) {
//            log.error("hy.anchorBehaviorMsg json parse error,  msg:{}, orgMsg:{}", msg, body, e);
//        }
//    }

}
