package fm.lizhi.ocean.wave.comment.core.remote.result;

import fm.lizhi.ocean.wave.comment.core.remote.bean.TransientComment;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 获取评论列表结果
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Deprecated
public class GetCommentWithServerTimeResult {
    /**
     * 评论列表
     */
    private List<TransientComment> transientComments;
    /**
     * 当前comment服务系统时间
     */
    private long commentServerTime;

    /**
     * 是否最后一页
     */
    private boolean lastPage;
}
