package fm.lizhi.ocean.wave.comment.core.extension.medal.bean;

import fm.lizhi.ocean.wave.comment.core.config.CommentCommonConfig;
import fm.lizhi.ocean.wave.comment.core.model.dto.EnterNoticeDTO;
import fm.lizhi.ocean.wave.comment.core.model.dto.TransientCommentDTO;
import fm.lizhi.ocean.wave.comment.core.remote.bean.EnterNoticeEntry;
import fm.lizhi.ocean.wave.live.core.remote.bean.LiveBean;
import lombok.*;

/**
 * 版本覆盖后需要删除
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class GenMedalInfoV2Context {

    /**
     * 评论信息
     */
    private TransientCommentDTO comment;

    /**
     * 进房公告信息
     */
    private EnterNoticeDTO enterNoticeEntry;

    /**
     * 评论勋章节点
     */
    private CommentCommonConfig commentConfig;

    /**
     * 直播间信息
     */
    private LiveBean liveBean;

    /**
     * 勋章展示区域，1：评论区，2：进房公告
     */
    private int medalShowArea;

    /**
     * 应用ID
     */
    private int appId;


    @Getter
    @NoArgsConstructor
    @AllArgsConstructor
    public enum MedalShowArea {
        /**
         * 评论区
         */
        COMMENT_AREA(1),
        /**
         * 进房公告
         */
        ENTER_NOTICE_AREA(2);

        private int area;
    }

}
