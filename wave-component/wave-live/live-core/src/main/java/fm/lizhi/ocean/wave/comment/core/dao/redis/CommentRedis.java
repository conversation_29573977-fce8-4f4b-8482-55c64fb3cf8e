package fm.lizhi.ocean.wave.comment.core.dao.redis;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import fm.lizhi.common.datastore.redis.client.RedisClient;
import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.ocean.wave.comment.core.model.vo.SpecialActivityCommentVO;
import fm.lizhi.ocean.wave.comment.core.remote.bean.EnterNoticeEntry;
import fm.lizhi.ocean.wave.comment.core.remote.bean.EnterNoticesParam;
import fm.lizhi.ocean.wave.comment.core.remote.bean.TransientComment;
import fm.lizhi.ocean.wave.comment.core.remote.param.GetCommentWithServerTimeParam;
import fm.lizhi.ocean.wave.common.constant.TimeConstant;
import fm.lizhi.ocean.wave.common.manager.RedisClientManager;
import fm.lizhi.ocean.wave.common.util.RedisLock;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import redis.clients.jedis.ScanParams;
import redis.clients.jedis.ScanResult;
import redis.clients.jedis.params.SetParams;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 评论redis
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class CommentRedis {

    @Autowired
    private RedisClientManager redisClientManager;

    /**
     * 评论id过期时间常量
     */
    public static final int COMMENT_ID_EXPIRE_TIME = 60 * 60 * 24;

    /**
     * 评论缓存时间常量
     */
    public static final int COMMENT_EXPIRE_TIME = 60 * 5;

    public static final int ONE_MIN_EXPIRE_SEC = 60;

    /**
     * 锁过期时间
     */
    private static final int expireMillis = 10000;

    /**
     * 获取锁超时时间
     */
    private static final int timeoutMillis = 10000;

    /**
     * 根据用户id保存评论id
     */
    public void saveCommentIdByUserId(Long userId, Long commentId) {
        RedisClient redisClient = redisClientManager.redisCacheClient();
        String key = CommentRedisKey.SEND_COMMENTID_SET.getKey(userId);
        Long sadd = redisClient.sadd(key, String.valueOf(commentId));
        if (sadd == null) {
            log.error("sadd fail. key={}, commentId={}", key, commentId);
        }

        // 设置过期时间
        Long expire = redisClient.expire(key, COMMENT_ID_EXPIRE_TIME);
        if (expire == null) {
            log.error("expire fail. key={}, commentId={}", key, commentId);
        }
    }

    /**
     * 根据用户ID保存评论ID列表
     *
     * @param userId
     * @param commentIds
     */
    public void batchSaveCommentIdByUserId(Long userId, List<String> commentIds) {
        RedisClient redisClient = redisClientManager.redisCacheClient();
        String key = CommentRedisKey.SEND_COMMENTID_SET.getKey(userId);
        Long sadd = redisClient.sadd(key, commentIds.toArray(new String[0]));
        if (sadd == null) {
            log.error("sadd fail. key={}, commentIds={}", key, JsonUtil.dumps(commentIds));
        }

        // 设置过期时间
        Long expire = redisClient.expire(key, COMMENT_ID_EXPIRE_TIME);
        if (expire == null) {
            log.error("expire fail. key={}, commentId={}", key, JsonUtil.dumps(commentIds));
        }
    }

    /**
     * 根据用户id检查评论id是否存在
     */
    public Boolean checkCommentIdByUserId(Long userId, Long commentId) {
        String key = CommentRedisKey.SEND_COMMENTID_SET.getKey(userId);
        Boolean sismember = redisClientManager.redisCacheClient().sismember(key, String.valueOf(commentId));
        if (sismember == null) {
            log.error("sismember fail. key={}, commentId={}", key, commentId);
            return false;
        }
        return sismember;
    }

    /**
     * 删除用户id下的评论id
     */
    public void deleteCommentIdByUserId(Long userId, Long commentId) {
        String key = CommentRedisKey.SEND_COMMENTID_SET.getKey(userId);
        Long srem = redisClientManager.redisCacheClient().srem(key, String.valueOf(commentId));
        if (srem == null) {
            log.error("srem fail. key={}, commentId={}", key, commentId);
        }
    }

    /**
     * 发送直播间房间公告幂等
     *
     * @param liveId    直播节目ID
     * @param uid       用户ID
     * @param expireSec 过期时间
     * @return true: 已经发送过，false: 未发送过
     */
    public boolean idempotentSendLiveIntro(long liveId, long uid, int expireSec) {
        //设置用户某段时间内只能有一次
        String key = CommentRedisKey.SEND_COME_ROOM_COMMENT.getKey(liveId, uid);
        String flag = redisClientManager.redisCacheClient().set(key, String.valueOf(1), SetParams.setParams().nx().ex(expireSec));
        return !"OK".equalsIgnoreCase(flag);
    }

    /**
     * 获取最新评论消息
     *
     * @param param 参数
     * @return 结果
     */
    public List<TransientComment> getCommentWithTime(GetCommentWithServerTimeParam param) {
        String key = getLatestCommentMsgKey(param.getAppId(), param.getLiveId());
        //这里不限定参数了，查询下游时，已经限定了该时间范围内的数据条数，不会很多
        Set<String> tuples = redisClientManager.redisCacheClient().zrangeByScore(key, param.getStartTime(), param.getEndTime());
        List<TransientComment> results = new ArrayList<>();
        if (tuples == null || tuples.isEmpty()) {
            return Lists.newArrayList();
        }
        for (String tuple : tuples) {
            TransientComment comment = JSONObject.parseObject(tuple, TransientComment.class);
            results.add(comment);
        }
        return results;
    }

    /**
     * 保存最新评论消息
     *
     * @param liveId            直播节目ID
     * @param transientComments 评论列表
     */
    public void saveLatestCommentMsg(int appId, long liveId, List<TransientComment> transientComments) {
        String key = getLatestCommentMsgKey(appId, liveId);
        Map<String, Double> commentInfoMap = transientComments.stream().collect(Collectors.toMap(JSONObject::toJSONString, b -> (double) b.getTime()));
        //批量插入，列表一般低于100条数据
        redisClientManager.redisCacheClient().zadd(key, commentInfoMap);
        redisClientManager.redisCacheClient().expire(key, ONE_MIN_EXPIRE_SEC * 5);
    }

    /**
     * 清理最新评论消息
     *
     * @param appId  应用ID
     * @param liveId 直播节目ID
     */
    public void setExpireLatestCommentTime(int appId, long liveId) {
        String key = getLatestCommentMsgKey(appId, liveId);
        redisClientManager.redisCacheClient().expire(key, COMMENT_EXPIRE_TIME);
    }

    /**
     * 删除某个时间范围内的缓存
     *
     * @param key       缓存key
     * @param startTime 开始时间
     * @param endTime   结束时间
     */
    public void remCacheData(String key, long startTime, long endTime) {
        redisClientManager.redisCacheClient().zremrangeByScore(key, startTime, endTime);
    }


    /**
     * 获取进房公告消息列表
     *
     * @param param 参数
     * @return 进房公告列表
     */
    public List<EnterNoticeEntry> getEnterNoticeEntries(EnterNoticesParam param) {
        String key = getEnterNoticeMsgKey(param.getAppId(), param.getLiveId());
        Set<String> tuples = redisClientManager.redisCacheClient().zrangeByScore(key, param.getStartTime(), param.getNoticeEndTime());
        List<EnterNoticeEntry> results = new ArrayList<>();
        if (tuples == null || tuples.isEmpty()) {
            return Lists.newArrayList();
        }
        for (String tuple : tuples) {
            EnterNoticeEntry enterNoticeEntry = JSONObject.parseObject(tuple, EnterNoticeEntry.class);
            results.add(enterNoticeEntry);
        }
        return results;
    }

    /**
     * 保存最新评论消息
     *
     * @param appId              应用ID
     * @param liveId             直播节目ID
     * @param enterNoticeEntries 进房公告列表
     */
    public void saveEnterNoticeMsg(int appId, long liveId, List<EnterNoticeEntry> enterNoticeEntries) {
        String key = getEnterNoticeMsgKey(appId, liveId);
        Map<String, Double> enterNoticeMap = enterNoticeEntries.stream().collect(Collectors.toMap(JSONObject::toJSONString, b -> (double) b.getTimeStamp()));
        //批量插入，列表一般低于100条数据
        redisClientManager.redisCacheClient().zadd(key, enterNoticeMap);
        //5分钟过期时间
        redisClientManager.redisCacheClient().expire(key, ONE_MIN_EXPIRE_SEC * 5);
    }

    /**
     * 清理最新进房消息
     *
     * @param appId  应用ID
     * @param liveId 直播节目ID
     */
    public void setExpireEnterNoticeTime(int appId, long liveId) {
        String key = getEnterNoticeMsgKey(appId, liveId);
        redisClientManager.redisCacheClient().expire(key, COMMENT_EXPIRE_TIME);
    }

    /**
     * 扫描缓存KEy
     *
     * @param key    key
     * @param cursor 游标
     * @return 结果
     */
    public ScanResult<String> scanCacheKey(String key, String cursor, int count) {
        return redisClientManager.redisCacheClient().scan(cursor, new ScanParams().match(key + "*").count(count));
    }


    /**
     * 获取评论清除分布式锁
     *
     * @param appId 应用ID
     * @return 锁对象
     */
    public RedisLock getCommentCleanLock(int appId) {
        return new RedisLock(redisClientManager.redisCacheClient(), CommentRedisKey.COMMENT_CLEAN_LOCK.getKey(appId), ONE_MIN_EXPIRE_SEC * 5, ONE_MIN_EXPIRE_SEC * 5);
    }

    /**
     * 获取最新评论消息key
     *
     * @param appId  应用ID
     * @param liveId 直播节目ID
     * @return 结果
     */
    public String getLatestCommentMsgKey(int appId, long liveId) {
        return CommentRedisKey.LATEST_COMMENT_MSG_SORT_SET.getKey(appId, liveId);
    }

    /**
     * 获取进房公告消息key
     *
     * @param appId  应用ID
     * @param liveId 直播节目ID
     * @return 结果
     */
    public String getEnterNoticeMsgKey(int appId, long liveId) {
        return CommentRedisKey.ENTER_NOTICE_MSG_SORT_SET.getKey(appId, liveId);
    }

    /**
     * 保存互动玩法评论消息
     *
     * @param appId     应用id
     * @param liveId    直播id
     * @param commentVO 评论信息
     */
    public void saveSpecialActivityCommentMsg(int appId, long liveId, SpecialActivityCommentVO commentVO) {
        String key = getSpecialActivityCommentMsgKey(appId, liveId);
        //批量插入，列表一般低于100条数据
        redisClientManager.redisCacheClient().zadd(key, commentVO.getTimeStamp(), JsonUtil.dumps(commentVO));
        redisClientManager.redisCacheClient().expire(key, TimeConstant.ONE_MINUTE * 15);
    }

    /**
     * 获取互动玩法评论消息
     *
     * @param appId  应用id
     * @param liveId 直播节目id
     * @return 数据列表
     */
    public List<SpecialActivityCommentVO> getSpecialActivityCommentMsg(int appId, long liveId, long startTime, long endTime) {
        String key = getSpecialActivityCommentMsgKey(appId, liveId);
        Set<String> tuples = redisClientManager.redisCacheClient().zrangeByScore(key, startTime, endTime);
        List<SpecialActivityCommentVO> results = new ArrayList<>();
        if (CollectionUtils.isEmpty(tuples)) {
            return Lists.newArrayList();
        }
        for (String tuple : tuples) {
            SpecialActivityCommentVO specialActivityCommentVO = JsonUtil.loads(tuple, SpecialActivityCommentVO.class);
            results.add(specialActivityCommentVO);
        }
        return results;
    }

    private String getSpecialActivityCommentMsgKey(int appId, long liveId) {
        return CommentRedisKey.SPECIAL_ACTIVITY_COMMENT_MSG_SORT_SET.getKey(appId, liveId);
    }
}
