package fm.lizhi.ocean.wave.comment.core.model.result;

import fm.lizhi.ocean.wave.comment.core.model.bean.ICommentBean;
import fm.lizhi.ocean.wave.comment.core.model.vo.CommentVO;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 最新的评论信息结果
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class LatestCommentsV2Result {
    /**
     * performanceId
     */
    private String performanceId;

    /**
     * 是否是最后一页
     */
    private boolean lastPage;

    /**
     * 查询间隔，单位毫秒
     */
    private Integer queryInterval;

    /**
     * 直播间评论信息列表
     */
    private List<ICommentBean> liveComment;

}
