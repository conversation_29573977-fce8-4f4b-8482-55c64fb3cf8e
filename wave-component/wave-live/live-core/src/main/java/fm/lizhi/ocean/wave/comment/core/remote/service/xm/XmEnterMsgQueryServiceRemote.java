package fm.lizhi.ocean.wave.comment.core.remote.service.xm;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.commons.service.client.rcode.GeneralRCode;
import fm.lizhi.ocean.wave.comment.core.model.dto.EnterNoticeDTO;
import fm.lizhi.ocean.wave.comment.core.remote.adapter.xm.XmEnterNoticesByRangeAdapter;
import fm.lizhi.ocean.wave.comment.core.remote.param.GetEnterNoticesByRangeParam;
import fm.lizhi.ocean.wave.comment.core.remote.param.GetNewFanNoticesByRangeParam;
import fm.lizhi.ocean.wave.comment.core.remote.param.GetVehicleEnterNoticesByRangeParam;
import fm.lizhi.ocean.wave.comment.core.remote.result.GetEnterNoticesResult;
import fm.lizhi.ocean.wave.comment.core.remote.result.GetNewFanNoticesResult;
import fm.lizhi.ocean.wave.comment.core.remote.result.GetVehicleEnterNoticesResult;
import fm.lizhi.ocean.wave.comment.core.remote.service.IEnterMsgQueryServiceRemote;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import xm.fm.lizhi.live.enternotice.api.EnterNoticeService;
import xm.fm.lizhi.live.enternotice.api.VehicleEnterService;
import xm.fm.lizhi.live.enternotice.protocol.EnternoticeProto;
import xm.fm.lizhi.live.enternotice.protocol.VehicleEnterProto;

import java.util.List;

/**
 * 进房公告服务
 *
 * <AUTHOR>
 * @date 2023/9/19 14:35
 */
@Slf4j
@Component
public class XmEnterMsgQueryServiceRemote implements IEnterMsgQueryServiceRemote {

    @Autowired
    private EnterNoticeService enterNoticeService;

    @Autowired
    private VehicleEnterService vehicleEnterService;

    @Autowired
    private XmEnterNoticesByRangeAdapter xmEnterNoticesByRangeAdapter;

    @Override
    public boolean support(BusinessEvnEnum evnEnum) {
        return BusinessEvnEnum.XIMI.equals(evnEnum);
    }

    @Override
    public Result<GetVehicleEnterNoticesResult> getVehicleEnterNoticesByRange(GetVehicleEnterNoticesByRangeParam param) {
        // 1. 请求获取座驾进房公告
        Result<VehicleEnterProto.ResponseVehicleEnterNoticesByRange> result =
                vehicleEnterService.vehicleEnterNoticesByRange(
                        param.getLiveId(), param.getStartTimeStamp(), param.getEndTimeStamp());

        if (result.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
            log.error("xm.vehicleEnterNoticesByRange fail rCode={}`param={}", result.rCode(), param);
            return new Result<>(result.rCode(), null);
        }

        // 2. 获取进房公告信息列表
        List<EnternoticeProto.EnterNoticeEntry> enterNoticesList = result.target().getEnterNoticesList();
        // 3. 创建进房公告信息列表
        List<EnterNoticeDTO> enterNoticeEntryBeanList = xmEnterNoticesByRangeAdapter.convertV2Result(enterNoticesList,param.getUserId());

        // 4. 创建进房公告结果
        GetVehicleEnterNoticesResult noticesResult = new GetVehicleEnterNoticesResult();
        noticesResult.setLiveId(result.target().getLiveId());
        noticesResult.setStartTimeStamp(result.target().getStartTimeStamp());
        noticesResult.setEndTimeStamp(result.target().getEndTimeStamp());
        noticesResult.setEnterNotices(enterNoticeEntryBeanList);

        return new Result<>(result.rCode(), noticesResult);
    }

    @Override
    public Result<GetNewFanNoticesResult> getNewFanNoticesByRange(GetNewFanNoticesByRangeParam param) {
        // 1. 请求获取新粉丝进房公告
        Result<EnternoticeProto.ResponseNewFanNoticesByRange> result = enterNoticeService.getNewFanNoticesByRange(param.getLiveId(),
                param.getStartTimeStamp(), param.getEndTimeStamp());
        if (result.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
            log.error("xm.getNewFanNoticesByRange fail rCode={}`param={}", result.rCode(), param);
            return new Result<>(result.rCode(), null);
        }

        // 2. 获取进房公告信息列表
        List<EnternoticeProto.EnterNoticeEntry> enterNoticesList = result.target().getEnterNoticesList();

        // 3. 创建进房公告信息列表
        List<EnterNoticeDTO> enterNoticeEntryBeanList = xmEnterNoticesByRangeAdapter.convertV2Result(enterNoticesList,param.getUserId());

        // 4. 创建进房公告结果
        GetNewFanNoticesResult noticesResult = new GetNewFanNoticesResult();
        noticesResult.setLiveId(result.target().getLiveId());
        noticesResult.setStartTimeStamp(result.target().getStartTimeStamp());
        noticesResult.setEndTimeStamp(result.target().getEndTimeStamp());
        noticesResult.setEnterNotices(enterNoticeEntryBeanList);

        return new Result<>(result.rCode(), noticesResult);
    }

    @Override
    public Result<GetEnterNoticesResult> getEnterNoticesByRange(GetEnterNoticesByRangeParam param) {
        // 1. 请求获取进房公告
        Result<EnternoticeProto.ResponseEnterNoticesByRange> result = enterNoticeService.getEnterNoticesByRange(param.getLiveId(),
                param.getStartTimeStamp(), param.getEndTimeStamp());
        if (result.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
            log.error("xm.getEnterNoticesByRange fail rCode={}`param={}", result.rCode(), param);
            return new Result<>(result.rCode(), null);
        }
        // 2. 获取进房公告列表
        List<EnternoticeProto.EnterNoticeEntry> enterNoticesList = result.target().getEnterNoticesList();

        // 3. 进房公告列表
        List<EnterNoticeDTO> enterNoticeEntryBeanList = xmEnterNoticesByRangeAdapter.convertV2Result(enterNoticesList,param.getUserId());

        // 4. 创建进房公告结果
        GetEnterNoticesResult noticesResult = new GetEnterNoticesResult();
        noticesResult.setLiveId(result.target().getLiveId());
        noticesResult.setStartTimeStamp(result.target().getStartTimeStamp());
        noticesResult.setEndTimeStamp(result.target().getEndTimeStamp());
        noticesResult.setEnterNotices(enterNoticeEntryBeanList);
        return new Result<>(result.rCode(), noticesResult);
    }

}
