package fm.lizhi.ocean.wave.comment.core.manager.handler.xm;

import fm.lizhi.ocean.wave.comment.core.config.CommentConfig;
import fm.lizhi.ocean.wave.comment.core.constants.WaveCommentType;
import fm.lizhi.ocean.wave.comment.core.convert.XmSpecialActivityCommentConvert;
import fm.lizhi.ocean.wave.comment.core.manager.handler.SpecialActivityCommentExtraHandler;
import fm.lizhi.ocean.wave.comment.core.model.bean.RoomPlayListCommentCardCommentExtra;
import fm.lizhi.ocean.wave.comment.core.model.vo.SpecialActivityCommentVO;
import fm.lizhi.ocean.wave.comment.core.remote.bean.RoomPlaylistCommentCardBean;
import fm.lizhi.ocean.wave.common.util.JsonUtils;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import xm.fm.lizhi.datacenter.comment.pp.bean.SpecialActivityCommentKafkaBean;

/**
 * 直播间精选评论卡片消息处理器
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class XmRoomPlayListCommentCardExtraHandler implements SpecialActivityCommentExtraHandler<RoomPlaylistCommentCardBean> {

    @Autowired
    private CommentConfig commentConfig;

    @Override
    public RoomPlaylistCommentCardBean convertExtraDTO(String json) {
        SpecialActivityCommentKafkaBean specialActivityCommentBean = JsonUtils.fromJsonString(json, SpecialActivityCommentKafkaBean.class);
        xm.fm.lizhi.datacenter.comment.pp.bean.RoomPlaylistCommentCardBean roomPlaylistBean = specialActivityCommentBean.getCommentInfo().getRoomPlaylistBean();
        if (roomPlaylistBean == null) {
            return null;
        }
        return XmSpecialActivityCommentConvert.I.convertRoomPlayListCommentBean(roomPlaylistBean);
    }

    @Override
    public void processActivityCommentExtra(RoomPlaylistCommentCardBean cardBean, SpecialActivityCommentVO commentVO) {
        if (cardBean == null || commentVO.getCommentType() != this.getCommentType()) {
            return;
        }
        //构建处理点唱玩法的评论信息
        RoomPlayListCommentCardCommentExtra commentExtra = XmSpecialActivityCommentConvert.I.convertRoomPlayListCommentBizExtra(cardBean);
        commentVO.setActivityExtra(commentExtra);
        commentVO.setContent(cardBean.getTitle() == null ? commentConfig.getXm().getRoomPlaylistCommentTitle() : cardBean.getTitle());
    }

    @Override
    public int getCommentType() {
        return WaveCommentType.LIVE_ROOM_PLAYLIST;
    }

    @Override
    public int getAppId() {
        return BusinessEvnEnum.XIMI.getAppId();
    }

}
