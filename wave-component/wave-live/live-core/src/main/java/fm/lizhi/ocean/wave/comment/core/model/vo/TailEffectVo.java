package fm.lizhi.ocean.wave.comment.core.model.vo;


import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Builder;
import lombok.Data;
import lombok.Getter;

/**
 * <AUTHOR>
 */
@Builder
@Getter
@Data
public class TailEffectVo {

    @JsonSerialize(using = ToStringSerializer.class)
    private long id;

    /**
     * url
     */
    private String effectUrl;
}
