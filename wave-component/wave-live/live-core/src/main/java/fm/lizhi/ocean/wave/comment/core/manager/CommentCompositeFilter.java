package fm.lizhi.ocean.wave.comment.core.manager;

import fm.lizhi.ocean.wave.comment.core.manager.filter.CommentV2Filter;
import fm.lizhi.ocean.wave.comment.core.manager.filter.MsgFilter;
import fm.lizhi.ocean.wave.comment.core.model.dto.TransientCommentDTO;
import fm.lizhi.ocean.wave.comment.core.remote.bean.TransientComment;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 评论过滤器组合器
 * <AUTHOR>
 */
public class CommentCompositeFilter implements CommentV2Filter {

    /**
     * 评论过滤器列表
     */
    private final List<CommentV2Filter> filters;

    public CommentCompositeFilter() {
        this.filters = new ArrayList<>();
    }

    /**
     * 添加过滤器
     * @param filter 过滤器
     */
    public void addFilter(CommentV2Filter filter) {
        filters.add(filter);
    }

    /**
     * 过滤
     * @param comments 评论列表
     * @return 过滤后的评论列表
     */
    @Override
    public List<TransientCommentDTO> filter(List<TransientCommentDTO> comments) {
        for (MsgFilter<TransientCommentDTO> filter : filters) {
            comments = filter.filter(comments);
        }
        return comments;
    }

}
