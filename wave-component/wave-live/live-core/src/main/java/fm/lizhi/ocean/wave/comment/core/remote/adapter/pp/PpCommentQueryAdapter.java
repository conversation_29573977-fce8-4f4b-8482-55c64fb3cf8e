package fm.lizhi.ocean.wave.comment.core.remote.adapter.pp;

import fm.lizhi.ocean.wave.comment.core.model.dto.TransientCommentDTO;
import fm.lizhi.ocean.wave.comment.core.model.dto.WealthLevelDTO;
import fm.lizhi.ocean.wave.comment.core.remote.bean.*;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;
import pp.fm.lizhi.datacenter.comment.pp.protocol.CommentProto;

import java.util.List;

/**
 * PP评论查询适配器
 *
 * <AUTHOR>
 */
@Mapper(
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        /* 未映射目标属性的报告策略 */
        unmappedTargetPolicy = ReportingPolicy.ERROR,
        /* 确定何时对bean映射的源属性值包含空检查 */
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface PpCommentQueryAdapter {

    PpCommentQueryAdapter I = Mappers.getMapper(PpCommentQueryAdapter.class);

    /**
     * 转换评论
     *
     * @param transientComment rpc相应体
     * @return 评论中间数据
     */
    @Mapping(target = "ppCommentExtra", ignore = true)
    @Mapping(target = "xmCommentExtra", ignore = true)
    @Mapping(target = "hyCommentExtra", ignore = true)
    @Mapping(target = "toUser", ignore = true)
    @Mapping(target = "aspectRatio", source = "imageAspectRatio")
    @Mapping(target = "simpleUser", expression =  "java(convertSimpleUser(transientComment.getSimpleUser()))")
    @Mapping(target = "wealthLevel", expression =  "java(convertWealthLevel(transientComment.getPpWealthLevel()))")
    @Mapping(target = "freshUser", expression =  "java(convertFreshUser(transientComment.getFreshUser()))")
    @Mapping(target = "medal", expression =  "java(convertMedalList(transientComment.getPpMedalList()))")
    TransientCommentDTO convertComment(CommentProto.TransientComment transientComment);

    /**
     * 转换为简单勋章列表
     *
     * @param simpleMedals 简单勋章列表
     * @return 结果
     */
    List<SimpleMedal> convertSimpleMedals(List<CommentProto.SimpleMedal> simpleMedals);

    /**
     * 转换为简单勋章
     *
     * @param simpleMedal 简单勋章
     * @return 结果
     */
    @Mapping(target = "longImage", ignore = true)
    SimpleMedal convertSimpleMedal(CommentProto.SimpleMedal simpleMedal);

    /**
     * 转换为财富等级
     *
     * @param wealthLevel 财富等级
     * @return 结果
     */
    WealthLevelDTO convertWealthLevel(CommentProto.PpWealthLevel wealthLevel);

    /**
     * 转换为成长关系等级列表
     *
     * @param growRelationLevelList 成长关系等级列表
     * @return 结果
     */
    List<GrowRelationLevel> convertGrowRelationLevelBeans(List<CommentProto.GrowRelationLevel> growRelationLevelList);


    /**
     * 生成守护勋章对象
     *
     * @param guardMedalUrl url
     * @return 守护勋章对象
     */
    default GuardMedal generateGuardMedal(String guardMedalUrl, float aspect) {
        GuardMedal medal = new GuardMedal();
        medal.setMedalUrl(guardMedalUrl);
        medal.setAspect(aspect);
        return medal;
    }

    /**
     * 转换为简单用户
     *
     * @param simpleUser 简单用户
     * @return 结果
     */
    @Mapping(target = "nameColorsList", ignore = true)
    @Mapping(target = "roomVipUrls", ignore = true)
    @Mapping(target = "userRoomVipStatus", ignore = true)
    SimpleUser convertSimpleUser(CommentProto.SimpleUser simpleUser);

    /**
     * 转换为新用户
     *
     * @param freshUser 新用户
     * @return 结果
     */
    @Mapping(target = "aspect", ignore = true)
    @Mapping(target = "url", ignore = true)
    FreshUser convertFreshUser( CommentProto.FreshUser freshUser);

    /**
     * 转换为贵族等级
     *
     * @param vipLevel 贵族等级
     * @return 结果
     */
    VipLevel convertVipLevel(CommentProto.PpVipLevel vipLevel);

    /**
     * 转换为用户勋章列表
     *
     * @param medalList 简单勋章列表
     * @return 结果
     */
    List<Medal> convertMedalList(List<CommentProto.PpMedal> medalList);

}
