package fm.lizhi.ocean.wave.comment.core.manager;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.commons.service.client.rcode.GeneralRCode;
import fm.lizhi.ocean.wave.comment.core.constants.WaveCommentType;
import fm.lizhi.ocean.wave.comment.core.manager.filter.CommentV2Filter;
import fm.lizhi.ocean.wave.comment.core.model.dto.TransientCommentDTO;
import fm.lizhi.ocean.wave.comment.core.remote.param.GetFansParam;
import fm.lizhi.ocean.wave.comment.core.remote.result.GetFansResult;
import fm.lizhi.ocean.wave.comment.core.remote.service.IRelationShipServiceRemote;

import java.util.ArrayList;
import java.util.List;

/**
 * 评论消息粉丝等级过滤器
 *
 * <AUTHOR>
 */
public class CommentFansFilter implements CommentV2Filter {

    private final IRelationShipServiceRemote relationShipServiceRemote;
    private final Long userId;
    private final Integer appId;

    public CommentFansFilter(IRelationShipServiceRemote relationShipServiceRemote,
                             Long userId,
                             Integer appId) {
        this.relationShipServiceRemote = relationShipServiceRemote;
        this.userId = userId;
        this.appId = appId;
    }

    @Override
    public List<TransientCommentDTO> filter(List<TransientCommentDTO> msg) {
        List<TransientCommentDTO> result = new ArrayList<>();
        GetFansParam param = new GetFansParam();
        //查询粉丝信息 <p>例如: 查询用户b是否是用户a的粉丝，
        // 则参数userId=a, toUserId=b，在rCode=0的情况下，判断下响应字段status=1
        param.setUserId(userId);
        param.setAppId(Long.valueOf(appId));
        for (TransientCommentDTO comment : msg) {
            if (comment.getCommentType() != WaveCommentType.CHANNEL_ENTER_COMMENT) {
                //不是渠道进房评论，不参与过滤
                result.add(comment);
            }
            param.setToUserId(comment.getUserId());
            Result<GetFansResult> getFansResultResult = relationShipServiceRemote.getFans(param);
            if (getFansResultResult.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
                continue;
            }

            GetFansResult getFansResult = getFansResultResult.target();
            if (getFansResult.getStatus() == 1) {
                result.add(comment);
            }
        }

        return result;
    }
}
