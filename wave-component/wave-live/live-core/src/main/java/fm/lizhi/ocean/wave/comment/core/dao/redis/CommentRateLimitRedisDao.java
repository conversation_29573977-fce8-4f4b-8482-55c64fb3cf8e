package fm.lizhi.ocean.wave.comment.core.dao.redis;

import com.lark.oapi.core.utils.Lists;
import fm.lizhi.common.datastore.redis.client.RedisClient;
import fm.lizhi.ocean.wave.common.constant.TimeConstant;
import fm.lizhi.ocean.wave.common.manager.RedisClientManager;
import fm.lizhi.ocean.wave.common.util.RedisLock;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class CommentRateLimitRedisDao {

    @Autowired
    private RedisClientManager redisClientManager;

    /**
     * 保存房间消息
     *
     * @param appId   应用ID
     * @param liveId  直播节目ID
     * @param msgType 消息类型
     * @param time    消息时间
     * @return 保存成功与否
     */
    public boolean saveLiveCommentInfo(int appId, long liveId, String msgType, long time) {
        String roomMsgCountKey = getLiveRoomMsgChangeKey(appId, msgType);
        RedisClient redisClient = redisClientManager.redisCacheClient();
        Long value = redisClient.zadd(roomMsgCountKey, time, String.valueOf(liveId));
        return value != null && value > 0;
    }

    /**
     * 判断是否存在直播评论信息
     *
     * @param appId   应用ID
     * @param liveId  直播节目ID
     * @param msgType 消息类型
     * @return 结果
     */
    public boolean existLiveCommentInfo(int appId, long liveId, String msgType) {
        String roomMsgCountKey = getLiveRoomMsgChangeKey(appId, msgType);
        RedisClient redisClient = redisClientManager.redisCacheClient();
        Double score = redisClient.zscore(roomMsgCountKey, String.valueOf(liveId));
        return score != null;
    }

    /**
     * 获取超时的直播ID列表
     *
     * @param appId      应用ID
     * @param msgType    消息类型
     * @param timeoutSec 超时时间，单位秒
     * @return 结果列表
     */
    public List<String> getTimeOutLiveIds(int appId, String msgType, int timeoutSec) {
        String roomMsgCountKey = getLiveRoomMsgChangeKey(appId, msgType);
        RedisClient redisClient = redisClientManager.redisCacheClient();
        //计算距离当前时间timeoutSec之前的时间戳
        long now = System.currentTimeMillis();
        long endTime = now - timeoutSec;
        Set<String> values = redisClient.zrangeByScore(roomMsgCountKey, -1, endTime);
        if (CollectionUtils.isEmpty(values)) {
            return Lists.newArrayList();
        }
        return new ArrayList<>(values);
    }

    /**
     * 删除超时的直播ID
     *
     * @param appId   应用ID
     * @param msgType 消息类型
     * @param liveId  直播ID列表
     * @return 结果
     */
    public void removeLiveId(int appId, String msgType, String liveId) {
        String roomMsgCountKey = getLiveRoomMsgChangeKey(appId, msgType);
        RedisClient redisClient = redisClientManager.redisCacheClient();
        redisClient.zrem(roomMsgCountKey, liveId);
    }

    /**
     * 保存直播间最新的评论时间
     *
     * @param appId   应用ID
     * @param liveId  直播节目ID
     * @param msgType 消息类型
     * @param time    时间
     * @return true: 成功，false: 失败
     */
    public boolean saveLiveRoomLatestCommentTime(int appId, long liveId, String msgType, long time) {
        String roomMsgCountKey = getLiveRoomLatestCommentTimeKey(appId, msgType);
        RedisClient redisClient = redisClientManager.redisCacheClient();
        Long value = redisClient.hset(roomMsgCountKey, String.valueOf(liveId), String.valueOf(time));
        return value != null;
    }

    /**
     * 批量获取最新的评论时间
     *
     * @param appId   应用ID
     * @param msgType 消息类型
     * @param liveId  直播间ID列表
     * @return 结果
     */
    public Long getLiveRoomLatestCommentTime(int appId, String msgType, Long liveId) {
        String roomMsgCountKey = getLiveRoomLatestCommentTimeKey(appId, msgType);
        RedisClient redisClient = redisClientManager.redisCacheClient();
        String value = redisClient.hget(roomMsgCountKey, String.valueOf(liveId));
        return StringUtils.isEmpty(value) ? 0 : Long.parseLong(value);
    }

    /**
     * 批量删除直播间最新的评论时间
     *
     * @param appId   应用ID
     * @param msgType 消息类型
     * @param liveId  直播间ID列表
     */
    public void removeLiveRoomLatestCommentTime(int appId, String msgType, String liveId) {
        String roomMsgCountKey = getLiveRoomLatestCommentTimeKey(appId, msgType);
        RedisClient redisClient = redisClientManager.redisCacheClient();
        //liveIds转字符串数组
        redisClient.hdel(roomMsgCountKey, liveId);
    }

    /**
     * 获取房间消息推送锁
     *
     * @param appId   应用ID
     * @param liveId  直播节目ID
     * @param msgType 消息类型
     * @return 锁
     */
    public RedisLock getRoomMsgPushLock(int appId, long liveId, String msgType) {
        String lockKey = getRoomMsgPushLockKey(appId, liveId, msgType);
        return new RedisLock(redisClientManager.redisCacheClient(), lockKey, TimeConstant.FIVE_SEC_MILLIS_SECONDS, TimeConstant.FIVE_SEC_MILLIS_SECONDS);
    }

    /**
     * 获取房间消息超时处理任务锁
     *
     * @param appId   应用ID
     * @param msgType 消息类型
     * @return 锁
     */
    public RedisLock getRoomMsgOutTimeTaskLock(int appId, String msgType) {
        String lockKey = getRoomMsgOutTimeTaskLockKey(appId, msgType);
        return new RedisLock(redisClientManager.redisCacheClient(), lockKey, TimeConstant.FIVE_SEC_MILLIS_SECONDS * 6, TimeConstant.FIVE_SEC_MILLIS_SECONDS * 6);
    }

    private String getLiveRoomMsgChangeKey(int appId, String msgType) {
        return CommentRedisKey.LIVE_ROOM_MSG_CHANGE.getKey(appId, msgType);
    }

    private String getRoomMsgPushLockKey(int appId, long liveId, String msgType) {
        return CommentRedisKey.LIVE_ROOM_MSG_PUSH_LOCK.getKey(appId, msgType, liveId);
    }

    private String getRoomMsgOutTimeTaskLockKey(int appId, String msgType) {
        return CommentRedisKey.ROOM_MSG_OUT_TIME_TASK_LOCK.getKey(appId, msgType);
    }

    private String getLiveRoomLatestCommentTimeKey(int appId, String msgType) {
        return CommentRedisKey.LIVE_ROOM_LATEST_COMMENT_TIME.getKey(appId, msgType);
    }

}
