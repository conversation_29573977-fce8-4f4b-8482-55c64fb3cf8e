package fm.lizhi.ocean.wave.comment.core.extension.medal.v2;

import com.google.common.collect.Lists;
import fm.lizhi.ocean.wave.comment.core.extension.medal.bean.GenMedalInfoV2Context;
import fm.lizhi.ocean.wave.comment.core.model.dto.TransientCommentDTO;
import fm.lizhi.ocean.wave.comment.core.model.vo.BadgeImageVO;
import fm.lizhi.ocean.wave.comment.core.remote.bean.GuardMedal;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;

/**
 * 心动玩法的守护等级勋章节点
 * <AUTHOR>
 */
@Component
public class PpGuardMedalV2Node implements ICommentV2MedalNode {
    @Override
    public Optional<List<BadgeImageVO>> buildMedalImageInfo(GenMedalInfoV2Context context) {
        //只有评论才有守护等级勋章
        TransientCommentDTO comment = context.getComment();
        GuardMedal guardMedal = comment.getPpCommentExtra().getGuardMedal();
        if (guardMedal == null || StringUtils.isBlank(guardMedal.getMedalUrl())) {
            return Optional.empty();
        }

        BadgeImageVO badgeImageVO = new BadgeImageVO();
        badgeImageVO.setBadgeAspect(guardMedal.getAspect());
        badgeImageVO.setBadgeUrl(guardMedal.getMedalUrl());
        return Optional.of(Lists.newArrayList(badgeImageVO));
    }
}
