package fm.lizhi.ocean.wave.comment.core.convert;

import fm.lizhi.ocean.wave.comment.core.config.CommentConfig;
import fm.lizhi.ocean.wave.comment.core.constants.CommentTypeMapping;
import fm.lizhi.ocean.wave.comment.core.model.dto.PpCommentExtraDTO;
import fm.lizhi.ocean.wave.comment.core.model.dto.TransientCommentDTO;
import fm.lizhi.ocean.wave.comment.core.model.dto.WealthLevelDTO;
import fm.lizhi.ocean.wave.comment.core.remote.bean.*;
import fm.lizhi.ocean.wave.comment.core.remote.bean.SimpleMedal;
import fm.lizhi.ocean.wave.comment.core.remote.bean.SimpleUser;
import fm.lizhi.ocean.wave.server.common.util.ContextUtils;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;
import pp.fm.lizhi.datacenter.comment.pp.bean.*;
import pp.fm.lizhi.datacenter.comment.pp.bean.TransientComment;

import java.util.ArrayList;
import java.util.List;

/**
 * PP特殊活动评论转换器
 *
 * <AUTHOR>
 */
@Mapper(
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        /* 未映射目标属性的报告策略 */
        unmappedTargetPolicy = ReportingPolicy.ERROR,
        /* 确定何时对bean映射的源属性值包含空检查 */
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface PpSpecialActivityCommentConvert {

    PpSpecialActivityCommentConvert I = Mappers.getMapper(PpSpecialActivityCommentConvert.class);

    /**
     * 转换评论
     *
     * @param transientComment rpc相应体
     * @return 评论中间数据
     */
    @Mapping(target = "ppCommentExtra", ignore = true)
    @Mapping(target = "xmCommentExtra", ignore = true)
    @Mapping(target = "hyCommentExtra", ignore = true)
    @Mapping(target = "bizType", source = "biz")
    @Mapping(target = "aspectRatio", source = "imageAspectRatio")
    @Mapping(target = "simpleUser", expression =  "java(convertSimpleUser(transientComment.getSimpleUser()))")
    @Mapping(target = "wealthLevel", expression =  "java(convertWealthLevel(transientComment.getPpWealthLevelBean()))")
    @Mapping(target = "freshUser", expression =  "java(convertFreshUser(transientComment.getFreshUserBean()))")
    @Mapping(target = "medal", expression =  "java(convertMedalList(transientComment.getPpMedalBeans()))")
    TransientCommentDTO convertComment(TransientComment transientComment);


    /**
     * 转换为黑叶特殊活动评论DTO
     *
     * @param transientComment 评论消息
     * @param commentConfig 评论配置
     * @return 黑叶特殊活动评论DTO
     */
    default TransientCommentDTO convert(TransientComment transientComment, CommentConfig commentConfig) {
        TransientCommentDTO transientCommentDTO = convertComment(transientComment);

        // 评论类型转换
        int commentType = CommentTypeMapping.getWaveCommentType(transientComment.getCommentType(),
                ContextUtils.getContext().getBusinessEvnEnum(),
                transientComment.getCommentTypeExtension());

        //构建简单勋章
        List<SimpleMedal> simpleMedals = convertSimpleMedals(transientComment.getSimpleMedals());
        //成长关系等级列表
        List<GrowRelationLevel> growRelationLevelBeans = convertGrowRelationLevelBeans(transientComment.getGrowRelationLevelBeans());
        //守护勋章
        GuardMedal guardMedal = generateGuardMedal(transientComment.getGuardMedalUrl(), commentConfig.getPp().getDefaultLevelMedalAspect());
        //贵族等级
        VipLevel vipLevel = convertVipLevel(transientComment.getPpVipLevelBean());

        PpCommentExtraDTO commentExtraDTO = new PpCommentExtraDTO();
        commentExtraDTO.setSimpleMedals(simpleMedals==null ? new ArrayList<>() : simpleMedals);
        commentExtraDTO.setGrowRelationLevelList(growRelationLevelBeans==null ? new ArrayList<>() : growRelationLevelBeans);
        commentExtraDTO.setGuardMedal(guardMedal);
        commentExtraDTO.setVipLevel(vipLevel);

        transientCommentDTO.setCommentType(commentType);
        transientCommentDTO.setPpCommentExtra(commentExtraDTO);
        return transientCommentDTO;
    }

    /**
     * 转换为简单勋章列表
     *
     * @param simpleMedals 简单勋章列表
     * @return 结果
     */
    List<SimpleMedal> convertSimpleMedals(List<pp.fm.lizhi.datacenter.comment.pp.bean.SimpleMedal> simpleMedals);

    /**
     * 转换为简单勋章
     *
     * @param simpleMedal 简单勋章
     * @return 结果
     */
    @Mapping(target = "longImage", ignore = true)
    SimpleMedal convertSimpleMedal(pp.fm.lizhi.datacenter.comment.pp.bean.SimpleMedal simpleMedal);

    /**
     * 转换为财富等级
     *
     * @param wealthLevel 财富等级
     * @return 结果
     */
    WealthLevelDTO convertWealthLevel(PpWealthLevelBean wealthLevel);

    /**
     * 转换为成长关系等级列表
     *
     * @param growRelationLevelBeans 成长关系等级列表
     * @return 结果
     */
    List<GrowRelationLevel> convertGrowRelationLevelBeans(List<GrowRelationLevelBean> growRelationLevelBeans);

    /**
     * 转换为成长关系等级
     *
     * @param growRelationLevelBean 成长关系等级
     * @return 结果
     */
    @Mapping(target = "levelId", source = "level")
    GrowRelationLevel convertGrowRelationLevel(GrowRelationLevelBean growRelationLevelBean);

       /**
     * 生成守护勋章对象
     *
     * @param guardMedalUrl url
     * @return 守护勋章对象
     */
    default GuardMedal generateGuardMedal(String guardMedalUrl, float aspect) {
        GuardMedal medal = new GuardMedal();
        medal.setMedalUrl(guardMedalUrl);
        medal.setAspect(aspect);
        return medal;
    }

    /**
     * 转换为简单用户
     *
     * @param simpleUser 简单用户
     * @return 结果
     */
    @Mapping(target = "nameColorsList", ignore = true)
    @Mapping(target = "roomVipUrls", ignore = true)
    @Mapping(target = "userRoomVipStatus", ignore = true)
    SimpleUser convertSimpleUser(pp.fm.lizhi.datacenter.comment.pp.bean.SimpleUser simpleUser);

    /**
     * 转换为新用户
     *
     * @param freshUser 新用户
     * @return 结果
     */
    @Mapping(target = "aspect", ignore = true)
    @Mapping(target = "url", ignore = true)
    FreshUser convertFreshUser(FreshUserBean freshUser);

    /**
     * 转换为贵族等级
     *
     * @param vipLevel 贵族等级
     * @return 结果
     */
    VipLevel convertVipLevel(PpVipLevelBean vipLevel);

    /**
     * 转换为用户勋章列表
     *
     * @param ppMedalList 简单勋章列表
     * @return 结果
     */
    List<Medal> convertMedalList(List<PpMedalBean> ppMedalList);

    /**
     * 转换为勋章
     *
     * @param ppMedal 勋章
     * @return 结果
     */
    @Mapping(target = "cover", source = "icon")
    Medal convertMedal(PpMedalBean ppMedal);
}
