package fm.lizhi.ocean.wave.comment.core.remote.service.hy;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.commons.service.client.rcode.GeneralRCode;
import fm.lizhi.hy.vip.api.CommentStyleService;
import fm.lizhi.hy.vip.protocol.CommentStyleProto;
import fm.lizhi.ocean.wave.comment.core.config.CommentConfig;
import fm.lizhi.ocean.wave.comment.core.constants.CommentTypeMapping;
import fm.lizhi.ocean.wave.comment.core.constants.WaveCommentType;
import fm.lizhi.ocean.wave.comment.core.model.dto.WealthLevelDTO;
import fm.lizhi.ocean.wave.comment.core.model.vo.ComeSourceVO;
import fm.lizhi.ocean.wave.comment.core.model.vo.TailEffectVo;
import fm.lizhi.ocean.wave.comment.core.remote.adapter.hy.HyAddCommentAdapter;
import fm.lizhi.ocean.wave.comment.core.remote.bean.*;
import fm.lizhi.ocean.wave.comment.core.remote.param.AddCommentImageParam;
import fm.lizhi.ocean.wave.comment.core.remote.param.GetCommentWithServerTimeParam;
import fm.lizhi.ocean.wave.comment.core.remote.param.GetTransientCommentReviewParam;
import fm.lizhi.ocean.wave.comment.core.remote.result.GetCommentWithServerTimeResult;
import fm.lizhi.ocean.wave.comment.core.remote.result.GetTransientCommentReviewResult;
import fm.lizhi.ocean.wave.comment.core.remote.service.ILiveCommentServiceRemote;
import fm.lizhi.ocean.wave.common.auto.route.common.facade.RemoteServiceInvokeFacade;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wave.server.common.util.ContextUtils;
import fm.lizhi.ocean.wave.user.api.UserService;
import fm.lizhi.ocean.wave.user.result.GetTailEffectResult;
import hy.fm.lizhi.datacenter.comment.pp.api.TransientCommentService;
import hy.fm.lizhi.datacenter.comment.pp.protocol.CommentProto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * 直播评论接口适配
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class HyLiveCommentServiceRemote extends RemoteServiceInvokeFacade implements ILiveCommentServiceRemote {

    @Autowired
    private TransientCommentService transientCommentService;

    @Autowired
    private CommentConfig commentConfig;


    @Autowired
    private UserService userService;

    @Autowired
    private CommentStyleService commentStyleService;

    @Override
    public boolean support(BusinessEvnEnum evnEnum) {
        return BusinessEvnEnum.HEI_YE.equals(evnEnum);
    }


    @Override
    public Result<List<CommentStyle>> getCommentStyleList() {
        Result<CommentStyleProto.ResponseGetCommentStyleList> result = commentStyleService.getCommentStyleList(0L);
        if (result.rCode() != 0 || result.target() == null) {
            log.warn("hy getCommentStyleList rCode={} targetIsNull={}",result.rCode(),result.target() == null);
            return new Result<>(GET_COMMENT_STYLE_ERROR, null);
        }
        List<CommentStyleProto.CommentStyle> commentStylesList = result.target().getCommentStylesList();
        List<CommentStyle> commentStyles = new ArrayList<>();
        for (CommentStyleProto.CommentStyle commentStyle : commentStylesList) {
            commentStyles.add(CommentStyle.builder()
                    .id(commentStyle.getId())
                    .andColor(commentStyle.getAndColor())
                    .andImage(commentStyle.getAndImage())
                    .iosColor(commentStyle.getIosColor())
                    .iosImage(commentStyle.getIosImage())
                    .build());
        }
        return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, commentStyles);
    }

    /**
     * 增加直播聊天(以对象方式传递参数,方便扩展)
     *
     * @param commentBean 评论参数
     * @return 评论结果
     */
    @Override
    public Result<Void> addCommentExtend(CommentBean commentBean) {
        return execute(commentBean, HyAddCommentAdapter.class,
                addCommentExtendParam -> getSpringInterfaceProxyBean(TransientCommentService.class)
                        .addCommentExtend((CommentProto.TransientCommentBean) addCommentExtendParam));
    }

    /**
     * 增加评论图片
     *
     * @param param 评论图片参数
     * @return 评论结果
     */
    @Override
    public Result<Void> addCommentImage(AddCommentImageParam param) {
        Result<Void> result = transientCommentService.addCommentImage(
                param.getCommentId(), param.getUserId(), param.getLiveId(),
                param.getImageHeight(), param.getImageWidth(), param.getImageSize(), param.getImageUrl(),
                param.getImageOriginal(), param.getCommentType());
        if (result.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
            log.error("addCommentImage failed, rCode={}`param={}", result.rCode(), param);
            return new Result<>(result.rCode(), null);
        }

        return new Result<>(result.rCode(), null);
    }

    /**
     * 获取评论列表
     *
     * @param param 获取评论列表参数
     * @return 评论列表
     */
    @Override
    public Result<GetCommentWithServerTimeResult> getCommentWithServerTime(GetCommentWithServerTimeParam param) {
        Result<CommentProto.ResponseGetTransientComment> result = transientCommentService.getCommentWithServerTime(
                param.getLiveId(),
                param.getStartTime(), param.getEndTime(),
                param.getCount(), param.isNjFirst(),
                param.isFirstEntry());

        if (result.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
            log.error("getCommentWithServerTime failed, rCode={}`param={}", result.rCode(), param);
            return new Result<>(result.rCode(), null);
        }

        CommentProto.ResponseGetTransientComment responseGetTransientComment = result.target();
        List<CommentProto.TransientComment> transientCommentList =
                responseGetTransientComment.getTransientCommentList();
        List<TransientComment> transientComments = new ArrayList<>();
        for (CommentProto.TransientComment transientComment : transientCommentList) {
            // 用户信息
            CommentProto.SimpleUser simpleUser = transientComment.getSimpleUser();
            // 用户佩戴的勋章（PP没有）
            List<CommentProto.SimpleMedal> simpleMedalList = transientComment.getSimpleMedalList();
            // 群马甲头像
            CommentProto.QunVest qunVest = transientComment.hasQunVest() ? transientComment.getQunVest() : null;
            // 财富等级
            CommentProto.SimpleUserWealthLevel level = transientComment.hasLevel() ? transientComment.getLevel() : null;
            //守护用户勋章信息
            CommentProto.GuardUserBadge guardUserBadge = transientComment.hasGuardUserBadge() ? transientComment.getGuardUserBadge() : null;
            //主播守护勋章信息
            CommentProto.GuardNjBadge guardNjBadge = transientComment.hasGuardNjBadge() ? transientComment.getGuardNjBadge() : null;
            //明星(主播)等级
            CommentProto.SimpleUserStarLevel starLevel = transientComment.hasStarLevel() ? transientComment.getStarLevel() : null;
            //粉丝等级勋章信息
            CommentProto.FansLevelBadge fansLevelBadge = transientComment.hasFansLevelBadge() ? transientComment.getFansLevelBadge() : null;
            // 发送的用户
            List<CommentProto.ToUser> toUserList = transientComment.getToUserList();
            // 财富等级勋章信息
            CommentProto.PpWealthLevel ppWealthLevel = transientComment.hasPpWealthLevel() ? transientComment.getPpWealthLevel() : null;
            //新用户信息
            CommentProto.FreshUser freshUser = transientComment.hasFreshUser() ? transientComment.getFreshUser() : null;
            //贵族等级
            CommentProto.PpVipLevel ppVipLevel = transientComment.hasPpVipLevel() ? transientComment.getPpVipLevel() : null;
            //大客户勋章
            CommentProto.VipMedal vipMedal = transientComment.hasVipMedal() ? transientComment.getVipMedal() : null;
            // 勋章
            List<CommentProto.PpMedal> ppMedalList = transientComment.getPpMedalList();
            //图片数据
            List<CommentProto.ImageData> imageDataList = transientComment.getImageDataList();
            //关系拍记录
            CommentProto.RelationPatRecord relationPatRecord = transientComment.getRelationPatRecord();
            //宝箱信息
            CommentProto.TreasureBox treasureBox = transientComment.getTreasureBox();
            //主播等级勋章
            CommentProto.AnthorLevel anthorLevel = transientComment.hasAnthorLevel() ? transientComment.getAnthorLevel() : null;
            //房间互动卡片数据
            CommentProto.RoomInteractionCard roomInteractionCard = transientComment.getRoomInteractionCard();
            // 尾灯
            TailEffectVo tailEffectVo = generateTailEffect(simpleUser.getUserId());
            //进房来源
            ComeSourceVO comeSource = generateComeSource(transientComment);

            // 多元素的评论内容
            List<CommentProto.MultiContentItem> multiContentItemsList = transientComment.getMultiContentItemsList();

            /**
             * 多元素的评论内容
             */
            List<MultiContentItem> contentItems = generateMultiContentItem(multiContentItemsList);

            SimpleUser simpleUserBean = generateSimpleUser(simpleUser);
            List<SimpleMedal> simpleMedals = generateSimpleMedals(simpleMedalList);
            QunVest qunVestBean = generateQunVest(qunVest);
            SimpleUserWealthLevel levelBean = generateSimpleUserWealthLevel(level);
            GuardUserBadge guardUserBadgeBean = generateGuardUserBadge(guardUserBadge);
            GuardNjBadge guardNjBadgeBean = generateGuardNjBadge(guardNjBadge);
            SimpleUserStarLevel starLevelBean = generateSimpleUserStarLevel(starLevel);
            FansLevelBadge fansLevelBadgeBean = generateFansLevelBadge(fansLevelBadge);
            List<Long> toUserIds = generateToUserIdList(toUserList);
            WealthLevelDTO wealthLevelBean = generateWealthLevel(ppWealthLevel);
            FreshUser freshUserBean = generateFreshUser(freshUser);
            VipLevel vipLevelBean = generateVipLevel(ppVipLevel);
            VipMedal vipMedalBean = generateVipMedal(vipMedal);
            List<Medal> medalBeans = generateMedals(ppMedalList);
            List<ImageData> imageDataBeans = generateImageData(imageDataList);
            RoomInteractionCard roomInteractionCardBean = generateRoomInteractionCard(roomInteractionCard);
            RelationPatRecord relationPatRecordBean = generateRelationPatRecord(relationPatRecord);
            TreasureBox treasureBoxBean = generateTreasureBox(treasureBox);
            AnthorLevel anthorLevelBean = generateAnthorLevel(anthorLevel);


            TransientComment bean = new TransientComment();
            bean.setCommentId(transientComment.getCommentId());
            bean.setContent(transientComment.getContent());
            bean.setTime(transientComment.getTime());
            bean.setIosColor(transientComment.getIosColor());
            bean.setAndroidColor(transientComment.getAndroidColor());
            bean.setUserRoles(transientComment.getUserRolesList());
            bean.setSimpleUser(simpleUserBean);
            bean.setQunVest(qunVestBean);
            bean.setLevel(levelBean);
            bean.setGuardUserBadge(guardUserBadgeBean);
            bean.setGuardNjBadge(guardNjBadgeBean);
            bean.setContentItems(contentItems);
            bean.setTailEffect(tailEffectVo); // 尾灯
            // 评论类型转换
            int commentType = CommentTypeMapping.getWaveCommentType(transientComment.getCommentType(),
                    BusinessEvnEnum.from(ContextUtils.getContext().getHeader().getAppId()),
                    transientComment.getCommentTypeExtension());
            bean.setCommentType(commentType);
            bean.setCommentTypeExtension(transientComment.getCommentTypeExtension());
            bean.setImageHeight(transientComment.getImageHeight());
            bean.setImageWidth(transientComment.getImageWidth());
            bean.setImageSize(transientComment.getImageSize());
            bean.setImageUrl(transientComment.getImageUrl());
            bean.setImageOriginal(transientComment.getImageOriginal());
            bean.setSimpleMedals(simpleMedals);
            bean.setGoldNjBadgeUrl(transientComment.getGoldNjBadgeUrl());
            bean.setExclusiveBadgeUrl(transientComment.getExclusiveBadgeUrl());
            bean.setStarLevel(starLevelBean);
            bean.setFansLevelBadge(fansLevelBadgeBean);
            bean.setEmotionId(transientComment.getEmotionId());
            bean.setRepeatStopImageIndex(transientComment.getRepeatStopImageIndex());
            bean.setToUser(toUserIds);
            bean.setStyleId(transientComment.getStyleId());
            bean.setBizType(transientComment.getBizType());
            bean.setWealthLevel(wealthLevelBean);
            bean.setFreshUser(freshUserBean);
            bean.setVipLevel(vipLevelBean);
            bean.setVipMedal(vipMedalBean);
            bean.setMedal(medalBeans);
            bean.setImageData(imageDataBeans);
            bean.setRoomInteractionCard(roomInteractionCardBean);
            bean.setRelationPatRecord(relationPatRecordBean);
            bean.setTreasureBox(treasureBoxBean);
            bean.setPotential(transientComment.getPotential());
            bean.setTargetUserId(transientComment.getTargetUserId());
            bean.setAnthorLevel(anthorLevelBean);
            bean.setOperatorUserId(transientComment.getOperatorUserId());
            //如果评论类型是系统评论，且触发系统评论的用户ID存在，则使用触发者的头像
            long userId = bean.getCommentType() == WaveCommentType.SYSTEM_NOTICE_COMMENT && transientComment.getOperatorUserId() > 0 ?
                    transientComment.getOperatorUserId() : transientComment.getUserId();
            bean.setUserId(userId);
            bean.setComeSource(comeSource);
            transientComments.add(bean);
        }
        GetCommentWithServerTimeResult getCommentWithServerTimeResult = new GetCommentWithServerTimeResult();
        getCommentWithServerTimeResult.setTransientComments(transientComments);
        getCommentWithServerTimeResult.setCommentServerTime(Math.min(responseGetTransientComment.getCommentServerTime(), param.getEndTime()));
        getCommentWithServerTimeResult.setLastPage(param.getCount() > transientComments.size());
        return new Result<>(result.rCode(), getCommentWithServerTimeResult);
    }

    /**
     * 获取评论审核状态
     *
     * @param param 获取评论审核状态参数
     * @return 评论审核状态
     */
    @Override
    public Result<List<GetTransientCommentReviewResult>> getTransientCommentReview(
            GetTransientCommentReviewParam param) {
        Result<CommentProto.ResponseGetTransientCommentReview> result =
                transientCommentService.getTransientCommentReview(param.getLiveId(), param.getCommentIds());
        if (result.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
            log.error("getTransientCommentReview fail, rCode={}`param={}", result.rCode(), param);
            return new Result<>(result.rCode(), null);
        }

        List<CommentProto.TransientCommentReview> commentReviewList =
                result.target().getTransientCommentReviewList();
        List<GetTransientCommentReviewResult> transientCommentReviewResults = new ArrayList<>();
        for (CommentProto.TransientCommentReview transientCommentReview : commentReviewList) {
            GetTransientCommentReviewResult commentReviewResult = new GetTransientCommentReviewResult();
            commentReviewResult.setCommentId(transientCommentReview.getCommentId());
            commentReviewResult.setReviewStatus(transientCommentReview.getReviewStatus());

            transientCommentReviewResults.add(commentReviewResult);
        }

        return new Result<>(result.rCode(), transientCommentReviewResults);
    }


    private List<MultiContentItem> generateMultiContentItem(List<CommentProto.MultiContentItem> multiContentItemsList){
        List<MultiContentItem> contentItems = new ArrayList<>();
        if(CollectionUtils.isEmpty(multiContentItemsList)){
            return contentItems;
        }
        for (CommentProto.MultiContentItem contentItem : multiContentItemsList) {
            MultiContentItem item = new MultiContentItem();
            item.setAction(contentItem.getAction());
            item.setContentType(contentItem.getContentType());
            item.setTextColor(contentItem.getTextColor());
            if (contentItem.getContentType() == 1) {
                item.setText(contentItem.getContent());
            } else {
                item.setImg(contentItem.getContent());
            }
            contentItems.add(item);
        }
        return contentItems;
    }

    private AnthorLevel generateAnthorLevel(CommentProto.AnthorLevel anthorLevel) {
        if (anthorLevel == null) {
            return null;
        }
        AnthorLevel anthorLevelBean = new AnthorLevel();
        anthorLevelBean.setLevel(anthorLevel.getLevel());
        anthorLevelBean.setCover(anthorLevel.getCover());
        anthorLevelBean.setAspect(anthorLevel.getAspect());
        return anthorLevelBean;
    }

    private TreasureBox generateTreasureBox(CommentProto.TreasureBox treasureBox) {
        TreasureBox treasureBoxBean = new TreasureBox();
        treasureBoxBean.setTreasureBoxId(treasureBox.getTreasureBoxId());
        treasureBoxBean.setUserId(treasureBox.getUserId());
        treasureBoxBean.setLiveId(treasureBox.getLiveId());
        treasureBoxBean.setTreasureBoxName(treasureBox.getTreasureBoxName());
        treasureBoxBean.setType(treasureBox.getType());
        treasureBoxBean.setCommand(treasureBox.getCommand());
        treasureBoxBean.setAmountLimit(treasureBox.getAmountLimit());
        treasureBoxBean.setGuestLimit(treasureBox.getGuestLimit());
        return treasureBoxBean;
    }

    private RelationPatRecord generateRelationPatRecord(CommentProto.RelationPatRecord relationPatRecord) {
        RelationPatRecord relationPatRecordBean = new RelationPatRecord();
        relationPatRecordBean.setFromUid(relationPatRecord.getFromUid());
        relationPatRecordBean.setTargetUid(relationPatRecord.getTargetUid());
        relationPatRecordBean.setAnimation(relationPatRecord.getAnimation());
        return relationPatRecordBean;
    }

    private RoomInteractionCard generateRoomInteractionCard(CommentProto.RoomInteractionCard roomInteractionCard) {
        RoomInteractionCard roomInteractionCardBean = new RoomInteractionCard();
        roomInteractionCardBean.setTitle(roomInteractionCard.getTitle());
        roomInteractionCardBean.setPortrait(roomInteractionCard.getPortrait());
        roomInteractionCardBean.setSeatNum(roomInteractionCard.getSeatNum());
        roomInteractionCardBean.setRightTxt(roomInteractionCard.getRightTxt());
        roomInteractionCardBean.setCompereUserId(roomInteractionCard.getCompereUserId());
        return roomInteractionCardBean;
    }

    private List<ImageData> generateImageData(List<CommentProto.ImageData> imageDataList) {
        List<ImageData> imageDataBeans = new ArrayList<>();
        for (CommentProto.ImageData imageData : imageDataList) {
            ImageData imageDataBean = new ImageData();
            imageDataBean.setIndex(imageData.getIndex());
            imageDataBean.setImagesUrl(imageData.getImagesUrlList());
            imageDataBeans.add(imageDataBean);
        }
        return imageDataBeans;
    }

    private List<Medal> generateMedals(List<CommentProto.PpMedal> ppMedalList) {
        List<Medal> medalBeans = new ArrayList<>();
        for (CommentProto.PpMedal ppMedal : ppMedalList) {
            Medal medalBean = new Medal();
            medalBean.setCover(ppMedal.getCover());
            medalBean.setAspect(ppMedal.getAspect());
            medalBeans.add(medalBean);
        }
        return medalBeans;
    }

    private VipMedal generateVipMedal(CommentProto.VipMedal vipMedal) {
        if (vipMedal == null) {
            return null;
        }
        VipMedal vipMedalBean = new VipMedal();
        vipMedalBean.setImage(vipMedal.getImage());
        vipMedalBean.setAspect(vipMedal.getAspect());
        vipMedalBean.setPriority(vipMedal.getPriority());
        return vipMedalBean;
    }

    private VipLevel generateVipLevel(CommentProto.PpVipLevel ppVipLevel) {
        if (ppVipLevel == null) {
            return null;
        }
        VipLevel vipLevelBean = new VipLevel();
        vipLevelBean.setLevel(ppVipLevel.getLevel());
        vipLevelBean.setCover(ppVipLevel.getCover());
        vipLevelBean.setAspect(ppVipLevel.getAspect());
        return vipLevelBean;
    }

    private FreshUser generateFreshUser(CommentProto.FreshUser freshUser) {
        if (freshUser == null) {
            return null;
        }
        FreshUser freshUserBean = new FreshUser();
        freshUserBean.setIs(freshUser.getIs());
        freshUserBean.setUrl(freshUser.getUrl());
        freshUserBean.setAspect(freshUser.getAspect());
        return freshUserBean;
    }

    private WealthLevelDTO generateWealthLevel(CommentProto.PpWealthLevel ppWealthLevel) {
        if (ppWealthLevel == null) {
            return null;
        }
        WealthLevelDTO wealthLevelBean = new WealthLevelDTO();
        wealthLevelBean.setLevel(ppWealthLevel.getLevel());
        wealthLevelBean.setCover(ppWealthLevel.getCover());
        wealthLevelBean.setAspect(ppWealthLevel.getAspect());
        return wealthLevelBean;
    }

    private List<Long> generateToUserIdList(List<CommentProto.ToUser> toUserList) {
        List<Long> toUserIds = new ArrayList<>();
        for (CommentProto.ToUser toUser : toUserList) {
            toUserIds.add(toUser.getUserId());
        }
        return toUserIds;
    }

    private FansLevelBadge generateFansLevelBadge(CommentProto.FansLevelBadge fansLevelBadge) {
        if (fansLevelBadge == null) {
            return null;
        }
        FansLevelBadge fansLevelBadgeBean = new FansLevelBadge();
        fansLevelBadgeBean.setImage(fansLevelBadge.getImage());
        fansLevelBadgeBean.setAspect(fansLevelBadge.getAspect());
        return fansLevelBadgeBean;
    }

    private SimpleUserStarLevel generateSimpleUserStarLevel(CommentProto.SimpleUserStarLevel starLevel) {
        if (starLevel == null) {
            return null;
        }
        SimpleUserStarLevel starLevelBean = new SimpleUserStarLevel();
        starLevelBean.setLevel(starLevel.getLevel());
        starLevelBean.setCover(starLevel.getCover());
        starLevelBean.setAspect(starLevel.getAspect());
        return starLevelBean;
    }

    private GuardNjBadge generateGuardNjBadge(CommentProto.GuardNjBadge guardNjBadge) {
        if (guardNjBadge == null) {
            return null;
        }
        GuardNjBadge guardNjBadgeBean = new GuardNjBadge();
        guardNjBadgeBean.setImage(guardNjBadge.getImage());
        guardNjBadgeBean.setAspect(guardNjBadge.getAspect());
        return guardNjBadgeBean;
    }

    private GuardUserBadge generateGuardUserBadge(CommentProto.GuardUserBadge guardUserBadge) {
        if (guardUserBadge == null) {
            return null;
        }
        GuardUserBadge guardUserBadgeBean = new GuardUserBadge();
        guardUserBadgeBean.setImage(guardUserBadge.getImage());
        guardUserBadgeBean.setAspect(guardUserBadge.getAspect());
        return guardUserBadgeBean;
    }

    private SimpleUserWealthLevel generateSimpleUserWealthLevel(CommentProto.SimpleUserWealthLevel level) {
        if (level == null) {
            return null;
        }
        SimpleUserWealthLevel levelBean = new SimpleUserWealthLevel();
        levelBean.setLevel(level.getLevel());
        levelBean.setCover(level.getCover());
        levelBean.setAspect(level.getAspect());
        return levelBean;
    }

    private QunVest generateQunVest(CommentProto.QunVest qunVest) {
        if (qunVest == null) {
            return null;
        }
        QunVest qunVestBean = new QunVest();
        qunVestBean.setImage(qunVest.getImage());
        qunVestBean.setAspect(qunVest.getAspect());
        return qunVestBean;
    }

    private List<SimpleMedal> generateSimpleMedals(List<CommentProto.SimpleMedal> simpleMedalList) {
        List<SimpleMedal> simpleMedals = new ArrayList<>();
        for (CommentProto.SimpleMedal simpleMedal : simpleMedalList) {
            SimpleMedal simpleMedalBean = new SimpleMedal();
            simpleMedalBean.setImage(simpleMedal.getImage());
            simpleMedalBean.setAspect(simpleMedal.getAspect());
            simpleMedalBean.setPriority(simpleMedal.getPriority());
            simpleMedalBean.setLongImage(simpleMedal.getLongImage());
            simpleMedals.add(simpleMedalBean);
        }
        return simpleMedals;
    }

    private SimpleUser generateSimpleUser(CommentProto.SimpleUser simpleUser) {
        SimpleUser simpleUserBean = new SimpleUser();
        simpleUserBean.setUserId(simpleUser.getUserId());
        simpleUserBean.setName(simpleUser.getName());
        simpleUserBean.setPortrait(simpleUser.getPortrait());
        simpleUserBean.setGender(simpleUser.getGender());
        return simpleUserBean;
    }


    private TailEffectVo generateTailEffect(long userId){
        Result<GetTailEffectResult> result = userService.getTailEffect(userId);
        if(result.rCode()!=GeneralRCode.GENERAL_RCODE_SUCCESS || result.target()==null){
            return null;
        }
        GetTailEffectResult target = result.target();
        return TailEffectVo.builder()
                .effectUrl(target.getTailEffectUrl())
                .id(target.getId())
                .build();
    }


    private ComeSourceVO generateComeSource(CommentProto.TransientComment transientComment){
        ComeSourceVO comeSource = new ComeSourceVO();
        comeSource.setComeSource(transientComment.getComeSource() <= 0 ? 0 : transientComment.getComeSource());
        comeSource.setComeSourceTgtUid(transientComment.getComeSourceTgtUid() <= 0 ? 0 : transientComment.getComeSourceTgtUid());
        return comeSource;
    }
}
