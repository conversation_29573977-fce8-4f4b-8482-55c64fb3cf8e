package fm.lizhi.ocean.wave.comment.core.extension.medal.v2;

import com.google.common.collect.Lists;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wave.comment.core.extension.medal.ICommentMedalNode;
import fm.lizhi.ocean.wave.comment.core.extension.medal.bean.GenMedalInfoContext;
import fm.lizhi.ocean.wave.comment.core.extension.medal.bean.GenMedalInfoV2Context;
import fm.lizhi.ocean.wave.comment.core.model.vo.BadgeImageVO;
import fm.lizhi.ocean.wave.common.config.CommonProviderConfig;
import fm.lizhi.ocean.wave.user.api.OfficialCertifiedService;
import fm.lizhi.ocean.wave.user.bean.OfficialCertified;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;

/**
 * 官方认证标签节点
 * <AUTHOR>
 */
@Component
public class HyOfficialCertifiedMedaV2lNode implements ICommentV2MedalNode {

    @Autowired
    private CommonProviderConfig commonProviderConfig;

    @Autowired
    private OfficialCertifiedService officialCertifiedService;

    @Override
    public Optional<List<BadgeImageVO>> buildMedalImageInfo(GenMedalInfoV2Context context) {
        Long userId = null;

        if (context.getMedalShowArea() == GenMedalInfoV2Context.MedalShowArea.COMMENT_AREA.getArea()) {
            userId = context.getComment().getUserId();
        }

        if (userId == null || userId <= 0L) {
            return Optional.empty();
        }

        Result<List<OfficialCertified>> result = officialCertifiedService.getUserOfficialCertified(userId);

        if (result.rCode() == 0 && CollectionUtils.isNotEmpty(result.target())) {
            OfficialCertified officialCertified = result.target().get(0);

            if (officialCertified != null) {
                BadgeImageVO badgeImageVO = new BadgeImageVO();
                badgeImageVO.setBadgeUrl(officialCertified.getBadgeUrl());
                badgeImageVO.setBadgeAspect(officialCertified.getBadgeAspect());
                return Optional.of(Lists.newArrayList(badgeImageVO));
            }
        }

        return Optional.empty();
    }
}
