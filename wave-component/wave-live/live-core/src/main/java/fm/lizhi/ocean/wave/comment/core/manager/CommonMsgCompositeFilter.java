package fm.lizhi.ocean.wave.comment.core.manager;

import fm.lizhi.ocean.wave.comment.core.manager.filter.MsgFilter;
import fm.lizhi.ocean.wave.comment.core.remote.bean.TransientComment;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * 评论过滤器组合器
 * <AUTHOR>
 */
@Deprecated
public class CommonMsgCompositeFilter implements CommentFilter {

    /**
     * 评论过滤器列表
     */
    private final List<MsgFilter<TransientComment>> filters;

    /**
     * 构造函数
     */
    public CommonMsgCompositeFilter() {
        filters = new ArrayList<>(Collections.singletonList(new MsgTypeFilter()));
    }

    /**
     * 添加过滤器
     * @param filter 过滤器
     */
    public void addFilter(MsgFilter<TransientComment> filter) {
        filters.add(filter);
    }

    /**
     * 过滤
     * @param comments 评论列表
     * @return 过滤后的评论列表
     */
    @Override
    public List<TransientComment> filter(List<TransientComment> comments) {
        for (MsgFilter<TransientComment> filter : filters) {
            comments = filter.filter(comments);
        }
        return comments;
    }
}
