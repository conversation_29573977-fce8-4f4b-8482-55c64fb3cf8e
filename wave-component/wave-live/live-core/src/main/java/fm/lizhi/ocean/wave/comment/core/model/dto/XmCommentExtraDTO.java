package fm.lizhi.ocean.wave.comment.core.model.dto;

import fm.lizhi.ocean.wave.comment.core.model.vo.TailEffectVo;
import fm.lizhi.ocean.wave.comment.core.remote.bean.*;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class XmCommentExtraDTO {

    /**
     * 尾灯 黑叶独有的
     */
    private TailEffectVo tailEffect;

    /**
     * 艾特的用户信息
     */
    private List<AtUser> atUsers;

    /**
     * 是否匿名
     */
    private boolean anonymous;
}
