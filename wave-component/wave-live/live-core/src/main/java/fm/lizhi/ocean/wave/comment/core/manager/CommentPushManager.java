package fm.lizhi.ocean.wave.comment.core.manager;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.commons.service.client.rcode.GeneralRCode;
import fm.lizhi.ocean.wave.comment.core.model.vo.PushCommentVo;
import fm.lizhi.ocean.wave.comment.core.model.vo.PushRoomMsgVo;
import fm.lizhi.ocean.wave.comment.core.model.vo.SpecialActivityCommentVO;
import fm.lizhi.ocean.wave.common.util.RomaTopicUtils;
import fm.lizhi.ocean.wave.common.util.RpcResult;
import fm.lizhi.ocean.wave.live.core.constants.PushConstants;
import fm.lizhi.ocean.wave.server.common.push.manager.RomePushManager;
import fm.lizhi.ocean.wave.server.common.push.vo.PushVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * Description:
 *
 * <AUTHOR>
 * Created in  2023/6/6
 */
@Slf4j
@Component
public class CommentPushManager {

    @Autowired
    private RomePushManager pushManager;


    /**
     * 推送评论消息
     *
     * @param liveId 直播节目ID
     * @param appId  应用ID
     */
    public int pushCommentMsgChange(long liveId, int appId, String biz, Long latestCommentTime) {
        latestCommentTime = latestCommentTime == null ? System.currentTimeMillis() : latestCommentTime;
        PushVO<PushRoomMsgVo> pushVo = new PushVO<>();
        pushVo.setBiz(biz);
        pushVo.setData(PushRoomMsgVo.builder().liveId(String.valueOf(liveId)).lastMsgTime(latestCommentTime).build());
        String topic = RomaTopicUtils.getTopic(PushConstants.LIVE_ROOM_MSG_CHANGE_PREFIX, appId, liveId);
        Result<Void> result = pushManager.pushWithCompHighTimeliness(topic, pushVo);
        return result.rCode();
    }


    /**
     * 推送需要删除的评论
     */
    public void pushDeleteComment(long liveId, long commentId, int appId) {
        PushVO<PushCommentVo> pushVo = new PushVO<>();
        pushVo.setBiz("delete-comment");
        pushVo.setData(PushCommentVo.builder().commentId(String.valueOf(commentId)).build());
        String topic = RomaTopicUtils.getTopic(PushConstants.LIVE_PUSH_TOPIC_PREFIX, appId, liveId);
        pushManager.pushWithCompHighTimeliness(topic, pushVo);
    }

    /**
     * 推送互动玩法评论消息
     *
     * @param appId     应用ID
     * @param liveId    直播节目id
     * @param biz       业务类型
     * @param commentVO 评论
     */
    public int pushSpecialActivityComment(int appId, long liveId, String biz, SpecialActivityCommentVO commentVO) {
        PushVO<SpecialActivityCommentVO> pushVo = new PushVO<>();
        pushVo.setBiz(biz);
        pushVo.setData(commentVO);
        String topic = RomaTopicUtils.getTopic(PushConstants.LIVE_ROOM_MSG_CHANGE_PREFIX, appId, liveId);
        Result<Void> result = pushManager.pushWithCompMediumTimeliness(topic, pushVo);
        return result.rCode();
    }

}
