package fm.lizhi.ocean.wave.comment.core.manager.handler;

import fm.lizhi.ocean.wave.comment.core.model.vo.SpecialActivityCommentVO;

/**
 * 活动玩法评论扩展字段处理器
 *
 * <AUTHOR>
 */
public interface SpecialActivityCommentExtraHandler<T> {

    /**
     * 转换互动玩法特殊的扩展字段
     *
     * @param json 评论json
     * @return 扩展DTO
     */
    T convertExtraDTO(String json);

    /**
     * 处理活动玩法评论扩展字段信息
     * 补全相关数据
     *
     * @param param          业务参数
     * @param commentBuilder 返回的评论信息
     */
    void processActivityCommentExtra(T param, SpecialActivityCommentVO commentBuilder);

    /**
     * 获取评论类型
     *
     * @return 类型值
     */
    int getCommentType();

    int getAppId();

}

