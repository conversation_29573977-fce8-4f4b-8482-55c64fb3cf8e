package fm.lizhi.ocean.wave.comment.core.model.vo;

import fm.lizhi.ocean.wave.comment.core.model.bean.CommentExtraInfo;
import fm.lizhi.ocean.wave.comment.core.remote.bean.MultiContentItem;
import lombok.Data;

import java.util.List;

/**
 * 黑叶评论扩展信息
 *
 * <AUTHOR>
 */
@Data
public class HyCommentExtraInfoVO implements CommentExtraInfo {

    /**
     * 多元素的评论内容
     */
    private List<MultiContentItem> contentItems;

    /**
     * 尾灯
     */
    private TailEffectVo tailEffect;

}
