package fm.lizhi.ocean.wave.comment.core.remote.adapter.hy;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import fm.lizhi.ocean.wave.comment.core.model.dto.TransientCommentDTO;
import fm.lizhi.ocean.wave.comment.core.model.dto.WealthLevelDTO;
import fm.lizhi.ocean.wave.comment.core.model.vo.ComeSourceVO;
import fm.lizhi.ocean.wave.comment.core.remote.bean.*;
import hy.fm.lizhi.datacenter.comment.pp.protocol.CommentProto;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.ArrayList;
import java.util.List;

/**
 * PP评论查询适配器
 *
 * <AUTHOR>
 */
@Mapper(
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        /* 未映射目标属性的报告策略 */
        unmappedTargetPolicy = ReportingPolicy.ERROR,
        /* 确定何时对bean映射的源属性值包含空检查 */
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface HyCommentQueryAdapter {

    HyCommentQueryAdapter I = Mappers.getMapper(HyCommentQueryAdapter.class);

    /**
     * 转换评论
     *
     * @param transientComment rpc相应体
     * @return 评论中间数据
     */
    @Mapping(target = "ppCommentExtra", ignore = true)
    @Mapping(target = "xmCommentExtra", ignore = true)
    @Mapping(target = "hyCommentExtra", ignore = true)
    @Mapping(target = "toUser", ignore = true)
    @Mapping(target = "aspectRatio", ignore = true)
    @Mapping(target = "simpleUser", expression =  "java(convertSimpleUser(transientComment.getSimpleUser()))")
    @Mapping(target = "wealthLevel", expression =  "java(convertWealthLevel(transientComment.getPpWealthLevel()))")
    @Mapping(target = "freshUser", expression =  "java(convertFreshUser(transientComment.getFreshUser()))")
    @Mapping(target = "medal", ignore = true)
    TransientCommentDTO convertComment(CommentProto.TransientComment transientComment);

    /**
     * 转换为简单用户
     *
     * @param simpleUser 简单用户
     * @return 结果
     */
    @Mapping(target = "nameColorsList", ignore = true)
    @Mapping(target = "roomVipUrls", ignore = true)
    @Mapping(target = "userRoomVipStatus", ignore = true)
    SimpleUser convertSimpleUser(CommentProto.SimpleUser simpleUser);

    /**
     * 多元素评论信息转换
     *
     * @param multiContentItemsList 多元素评论信息
     * @return 结果
     */
    default List<MultiContentItem> generateMultiContentItem(List<CommentProto.MultiContentItem> multiContentItemsList) {
        List<MultiContentItem> contentItems = new ArrayList<>();
        if (CollectionUtils.isEmpty(multiContentItemsList)) {
            return contentItems;
        }
        for (CommentProto.MultiContentItem contentItem : multiContentItemsList) {
            MultiContentItem item = new MultiContentItem();
            item.setAction(contentItem.getAction());
            item.setContentType(contentItem.getContentType());
            item.setTextColor(contentItem.getTextColor());
            if (contentItem.getContentType() == 1) {
                item.setText(contentItem.getContent());
            } else {
                item.setImg(contentItem.getContent());
            }
            contentItems.add(item);
        }
        return contentItems;
    }

    List<SimpleMedal> convertSimpleMedals(List<CommentProto.SimpleMedal> simpleMedalList);


    /**
     * 转换为主播等级
     *
     * @param anchorLevel 主播等级
     * @return 转换结果
     */
    AnthorLevel convertAnthorLevel(CommentProto.AnthorLevel anchorLevel);

    /**
     * 转换为评论来源
     *
     * @param transientComment 评论
     * @return 转换为评论来源
     */
    default ComeSourceVO convertComeSource(CommentProto.TransientComment transientComment) {
        ComeSourceVO comeSource = new ComeSourceVO();
        comeSource.setComeSource(Math.max(transientComment.getComeSource(), 0));
        comeSource.setComeSourceTgtUid(transientComment.getComeSourceTgtUid() <= 0 ? 0 : transientComment.getComeSourceTgtUid());
        return comeSource;
    }

    /**
     * 转换为新用户
     *
     * @param freshUser 新用户
     * @return 结果
     */
    FreshUser convertFreshUser(CommentProto.FreshUser freshUser);

    /**
     * 转换为财富等级
     *
     * @param wealthLevel 财富等级
     * @return 结果
     */
    WealthLevelDTO convertWealthLevel(CommentProto.PpWealthLevel wealthLevel);


}
