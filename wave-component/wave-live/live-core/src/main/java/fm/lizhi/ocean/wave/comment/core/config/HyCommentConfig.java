package fm.lizhi.ocean.wave.comment.core.config;

import lombok.Data;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class HyCommentConfig implements CommentCommonConfig {


    /**
     * 用户的vip勋章的占比
     */
    private float userVipAspect = 0.33f;

    /**
     * 拍一拍动画路径
     */
    private String patAnimationUrl = "";

    // 拍一拍评论颜色 FFB8E986
    private String patIosColor = "";
    private String patAndroidColor = "";

    /**
     * 拍一拍评论内容
     */
    private String patCommentText = "${userName} 拍了拍 ${targetName}";

    /**
     * 是否限制海外IP
     * 关联原配置 app_hy_live
     */
    private boolean overseaLimitSwitch = true;

    /**
     * 海外IP测试白名单，json格式，key：userId，value：ip，示例：{"123456":"***************"}
     * 关联原配置 app_hy_live
     */
    private String overseaIpTestWhitelist;

    /**
     * 社交行为次数上限（无手机号的情况）
     */
    private int socialOperationLimitWithoutPhone = 20;

    /**
     * 大主播的房间号, 多个用英文逗号分割
     */
    private List<Long> bigStarLiveRoomIds = Collections.emptyList();
    /**
     * 互动表情 - 分组表情信息
     */
    private String interactiveMotionGroupEmotion = "";

    /**
     * 互动表情版本
     */
    private Integer interactiveEmotionVersion = 6;

    /**
     * 海外用户手机是否受限
     */
    private boolean overseaPhoneNumSwitch = true;
    /**
     * 海外用户手机APP受限白名单
     */
    private String overseaPhoneNumAppWhiteList = "57333013";
    /**
     * 海外用户id受限白名单
     */
    private String overseaUserIdWhiteList = "";
    /**
     * 风控功能提示
     */
    private String checkRiskFunctionNotice = "您好，我们正在对系统进行升级维护，个别用户登陆后，将可能暂时无法使用部分功能。具体的恢复时间待定，感谢您的支持和理解。";
    /**
     * 风控风险提示
     */
    private String checkRiskNotice = "行为存在风险。";
    /**
     * 大主播节目ID，不判断是否在娱乐模式
     */
    private String bigStarLiveLiveIds = "";
    /**
     * 大主播用户id 多个用逗号分隔
     */
    private String bigStarUserIds = "";
    /**
     * 大主播获取评论频控，每秒单实例
     */
    private int bigStarGetCommentFrequency = 10000;
    /**
     * 第一次进入直播间获取评论的开始时间与当前时间的差值, 默认5400000毫秒
     */
    private long differenceValue = 5400000;
    /**
     * 直播间改版1.0.1 轮询 评论 间隔时间（秒）
     **/
    private int requestInterval = 2;
    /**
     * 直播评论数量的回复上限
     */
    private int liveCommentsRespLimit = 20;
    /**
     * 进房公共开关
     */
    private boolean enterNoticeOn = true;
    /**
     * 进房公告获取数据间隔
     */
    private int enterNoticeSecond = 2000;
    /**
     * v420 付费率 - 进房消息勋章 - 显示顺序 - 最多显示2个
     * <p>
     * 默认顺序 新人1 > 财富等级2 > 粉钻3 > VIP4 > 回归5 > 陪玩勋章6
     */
    private String payRateMedalShowSortJSONArr = "[1,2,3,4,5,6]";
    /**
     * v420 付费率 - 进房消息勋章 - 勋章信息 - 粉钻组ID
     */
    private long payRateMedalPinkDiamondGroupId = 5259686445528454271L;

    /**
     * v420 付费率 - 进房消息勋章 - 勋章信息 - VIP组ID
     */
    private long payRateMedalVipGroupId = 5243024330253673087L;

    /**
     * v420 付费率 - 进房消息勋章 - 勋章信息 - 回归组ID
     */
    private long payRateMedalComeBackGroupId = 5252637461388988543L;

    /**
     * v420 付费率 - 进房消息勋章 - 勋章信息 - 陪玩组ID
     */
    private long payRateMedalPlayerGroupId = 5245460972589607039L;
    /**
     * 进房公告新用户Icon的宽高比（PP）
     * <p>
     * 默认 0.5f
     */
    private float ppEnterNoticeFreshUserIconAspect = 0.5F;
    /**
     * 默认等级勋章高宽比
     *
     * @return
     */
    private float defaultLevelMedalAspect = 0.4f;
    /**
     * 默认等级勋章高宽比开关
     *
     * @return
     */
    private boolean defaultLevelMedalAspectFlag = true;

    /**
     * 老用户欢迎文案
     */
    private String payRateWelcomeOldUserText = "你来了，其实我没有等多久，大概是一千零一夜;月亮不会奔向你，但我会，不远万里那种;Hi，我和别人不一样，我的心不在左边，在你那边";

    /**
     * 新用户欢迎文案
     */
    private String payRateWelcomeNewUserText = "${from} 向 ${to} 送了爱心泡泡";

    /**
     * 新用户欢迎 - 送礼特效 - 爱心泡泡
     */
    private String payRateWelcomeNewUserSvga = "ppclientres/2022/09/22/2964846839834836992.svga";


    /**
     * 老用户欢迎 - 送礼特效 - 飘带
     */
    private String payRateWelcomeOldUserSvga = "ppclientres/2022/09/22/2964847054583202816.svga";

    /**
     * 是否有欢迎功能
     */
    public boolean isOpenWelcomeSwitch = false;

    /**
     * 欢迎按钮次数限制
     */
    public boolean isWelcomeCountLimitSwitch = true;


    /**
     * 房间超级管理图标
     */
    private String roomSuperManagerIconUrl = "https://cdn.lizhilive.com/sociality/2024/12/09/3114935240608788028.png";

    /**
     * 房间管理图标
     */
    private String roomManagerIconUrl = "https://cdn.lizhilive.com/sociality/2024/12/12/3115479582616533564.png";

    /**
     * 房主图片
     */
    private String roomOwnerIconUrl = "https://cdn.lizhilive.com/studio/2021/09/13/2895423306104679990_108x48.png";

    /**
     * 房主、管理图标高宽比
     */
    private float userRoleImageBadgeAspect = 0.44444f;

    /**
     * 勋章图标高宽比默认值
     */
    private float defaultMedalIconAspect = 1F;

    /**
     * 财富等级特有cdn
     */
    private String wealthLevelCdn = "https://fepublic.lizhilive.com/";

    /**
     * 新用户Icon的资源链接（PP）
     */
    public String freshUserIconUrl = "";

    /**
     * 新用户Icon的宽高比（PP）
     * <p>
     * 默认 0.5f
     */
    public float freshUserIconAspect = 1F;

    /**
     * 表情默认发送评论
     */
    public String emotionDefaultComment = "升级至新版本查看该消息";

    /**
     * 互动表情 旧版本表情提示文案
     */
    private String interactiveMotionImproveText = "[未知表情,请更新到最新版本查看]";

    /**
     * 互动表情分组icon
     */
    private String actionEmotionGroupIconUrl = "https://cdnimg101.gzlzfm.com/web_res/activityimage/2023/11/23/3044021025126515712.png";

    /**
     * 替换互动表情的缩略图
     * key=emotionId,value=imageUrl
     * {
     * "1":"https://cdn.lizhilive.com/sociality/2023/02/15/2991880568547808316.png",
     * "2":"https://cdn.lizhilive.com/sociality/2023/02/15/2991880714578922044.png"
     * }
     */
    private String actionEmotionImageJson = "{\n" +
            "    \"1\":\"https://cdn.lizhilive.com/sociality/2023/02/15/2991880568547808316.png\",\n" +
            "    \"2\":\"https://cdn.lizhilive.com/sociality/2023/02/15/2991880714578922044.png\"\n" +
            "}";

    /**
     * 评论缓存开关
     */
    private boolean commentCacheSwitch;

    /**
     * 进房公告缓存开关
     */
    private boolean enterNoticeCacheSwitch;

    /**
     * 获取调整评论查询数量的时间间隔，单位ms
     */
    private int updateCommentCountTimeInterval = 4000;

    /**
     * 获取查询评论的最大条数
     */
    private int maxLiveCommentsRespCount = 40;

    private String reviewEmotionUrl = "";

    /**
     * 发送评论校验角色开关，，默认关
     */
    private boolean sendCommentRoleCheckSwitch;

    /**
     * 评论日志开关
     */
    private boolean commentMsgLogSwitch;


    /**
     * 座驾黑名单ID列表，存在的话就不显示座驾
     */
    private String mountBlackListIds = "";

    /**
     * Vip表情包配置
     */
    private String vipEmotionJson = "";


    /**
     * 新用户图标
     */
    private String freshUserIcon = "https://cdn.lizhifm.cn/sociality/2025/04/10/3137596606901682236.png";

    /**
     * 新人进房标签
     */
    private String freshUserRoomIcon = "https://cdn.lizhifm.cn/sociality/2025/04/10/3137596606901682748.png";

    /**
     * 进房文案
     */
    private String enterRoomText = "我是萌新，请多关照～";

    /**
     * 互动玩法评论类型
     */
    private List<Integer> specialActivityCommentTypes = new ArrayList<>();

    /**
     * 歌手匹配卡 进房文案
     */
    private String singerEnterRoomText = "传说中才华横溢的偶像，跟我聊聊吧！";

    private List<Long> printCommentLogUserIds = new ArrayList<>();

    /**
     * 进房消息日志输出开关
     */
    private boolean enterMsgLogSwitch = true;
}
