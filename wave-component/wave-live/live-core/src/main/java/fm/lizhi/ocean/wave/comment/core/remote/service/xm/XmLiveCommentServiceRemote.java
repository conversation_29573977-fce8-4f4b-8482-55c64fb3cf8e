package fm.lizhi.ocean.wave.comment.core.remote.service.xm;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.commons.service.client.rcode.GeneralRCode;
import fm.lizhi.ocean.wave.comment.core.config.CommentConfig;
import fm.lizhi.ocean.wave.comment.core.constants.CommentTypeMapping;
import fm.lizhi.ocean.wave.comment.core.constants.WaveCommentType;
import fm.lizhi.ocean.wave.comment.core.model.vo.TailEffectVo;
import fm.lizhi.ocean.wave.comment.core.model.dto.WealthLevelDTO;
import fm.lizhi.ocean.wave.comment.core.remote.adapter.xm.RoomCommentConvert;
import fm.lizhi.ocean.wave.comment.core.remote.adapter.xm.XmAddCommentAdapter;
import fm.lizhi.ocean.wave.comment.core.remote.bean.*;
import fm.lizhi.ocean.wave.comment.core.remote.param.AddCommentImageParam;
import fm.lizhi.ocean.wave.comment.core.remote.param.GetCommentWithServerTimeParam;
import fm.lizhi.ocean.wave.comment.core.remote.param.GetTransientCommentReviewParam;
import fm.lizhi.ocean.wave.comment.core.remote.result.GetCommentWithServerTimeResult;
import fm.lizhi.ocean.wave.comment.core.remote.result.GetTransientCommentReviewResult;
import fm.lizhi.ocean.wave.comment.core.remote.service.ILiveCommentServiceRemote;
import fm.lizhi.ocean.wave.common.auto.route.common.facade.RemoteServiceInvokeFacade;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wave.server.common.util.ContextUtils;
import fm.lizhi.ocean.wave.user.api.UserService;
import fm.lizhi.ocean.wave.user.result.GetTailEffectResult;
import fm.lizhi.xm.vip.bean.decorate.DecorateExtInfoDto;
import fm.lizhi.xm.vip.bean.decorate.DecorateTypeEnum;
import fm.lizhi.xm.vip.bean.decorate.req.GetBubbleDecorateListReq;
import fm.lizhi.xm.vip.bean.decorate.resp.GetBubbleDecorateListResp;
import fm.lizhi.xm.vip.bean.decorate.resp.PageDecorateDto;
import fm.lizhi.xm.vip.services.DecorateService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import xm.fm.lizhi.datacenter.comment.pp.api.TransientCommentService;
import xm.fm.lizhi.datacenter.comment.pp.constant.CommentVisibleClientType;
import xm.fm.lizhi.datacenter.comment.pp.protocol.CommentProto;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 直播评论接口适配
 */
@Slf4j
@Component
public class XmLiveCommentServiceRemote extends RemoteServiceInvokeFacade implements ILiveCommentServiceRemote {

    @Autowired
    private TransientCommentService transientCommentService;

    @Autowired
    private XmAddCommentAdapter xmAddCommentAdapter;

    @Autowired
    private CommentConfig commentConfig;

    @Autowired
    private DecorateService decorateService;
    @Autowired
    private UserService userService;


    @Override
    public boolean support(BusinessEvnEnum evnEnum) {
        return BusinessEvnEnum.XIMI.equals(evnEnum);
    }


    @Override
    public Result<List<CommentStyle>> getCommentStyleList() {

        GetBubbleDecorateListReq req = new GetBubbleDecorateListReq();
        req.setTimes(0L);
        Result<GetBubbleDecorateListResp> result = decorateService.getBubbleDecorateList(req);
        if (result.rCode() != 0 || result.target() == null) {
            log.warn("xm getCommentStyleList rCode={} targetIsNull={}", result.rCode(), result.target() == null);

        }
        List<PageDecorateDto> commentStylesList = result.target().getDecorateDtoList();
        List<CommentStyle> commentStyles = new ArrayList<>();
        for (PageDecorateDto commentStyle : commentStylesList) {
            CommentStyle.CommentStyleBuilder builder = CommentStyle.builder();
            builder.id(Long.valueOf(commentStyle.getId()));

            List<DecorateExtInfoDto> extInfos = JSONObject.parseArray(commentStyle.getExtInfo(), DecorateExtInfoDto.class);
            if (!CollectionUtils.isEmpty(extInfos)) {
                Optional<DecorateExtInfoDto> bubbleColor = extInfos.stream().filter(e -> DecorateTypeEnum.BUBBLE.getType() == e.getType() && e.getKey().equals("bubbleColor")).findFirst();
                if (bubbleColor.isPresent()) {
                    builder.iosColor(String.valueOf(Long.valueOf(bubbleColor.get().getValue(), 16)));
                    builder.andColor(String.valueOf(Long.valueOf(bubbleColor.get().getValue(), 16)));
                }
            }
            builder.iosImage(commentStyle.getThumbUrl());
            builder.andColor(commentStyle.getThumbUrl());
            commentStyles.add(builder.build());
        }
        return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, commentStyles);
    }

    /**
     * 增加直播聊天(以对象方式传递参数,方便扩展)
     *
     * @param commentBean 评论参数
     * @return 评论结果
     */
    @Override
    public Result<Void> addCommentExtend(CommentBean commentBean) {
        CommentProto.TransientCommentBean param = xmAddCommentAdapter.convertParam(commentBean);
        Result<Void> result = transientCommentService.addCommentExtend(param);
        if (result.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
            log.warn("xm addCommentExtend commentBean={},rCode={}", commentBean, result.rCode());
        }
        return xmAddCommentAdapter.convertResult(result);
    }

    /**
     * 增加评论图片
     *
     * @param param 评论图片参数
     * @return 评论结果
     */
    @Override
    public Result<Void> addCommentImage(AddCommentImageParam param) {
        Result<Void> result = transientCommentService.addCommentImage(
                param.getCommentId(), param.getUserId(), param.getLiveId(),
                param.getImageHeight(), param.getImageWidth(), param.getImageSize(), param.getImageUrl(),
                param.getImageOriginal(), param.getCommentType());
        if (result.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
            log.warn("xm addCommentImage failed, rCode={}`param={}", result.rCode(), param);
        }
        return new Result<>(result.rCode(), null);
    }

    /**
     * 获取评论列表
     *
     * @param param 获取评论列表参数
     * @return 评论列表
     */
    @Override
    public Result<GetCommentWithServerTimeResult> getCommentWithServerTime(GetCommentWithServerTimeParam param) {
        Result<CommentProto.ResponseGetTransientComment> result = transientCommentService.getCommentWithServerTime(
                param.getLiveId(),
                param.getStartTime(), param.getEndTime(),
                param.getCount(), param.isNjFirst(),
                param.isFirstEntry());

        if (result.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
            log.error("getCommentWithServerTime failed, rCode={}`param={}", result.rCode(), param);
            return new Result<>(result.rCode(), null);
        }

        CommentProto.ResponseGetTransientComment responseGetTransientComment = result.target();
        List<CommentProto.TransientComment> transientCommentList =
                responseGetTransientComment.getTransientCommentList();
        List<TransientComment> transientComments = new ArrayList<>();
        // 过滤掉PC端不可见评论
        transientCommentList = CollectionUtils.isEmpty(transientCommentList) ? transientCommentList : transientCommentList
                .stream().filter(entity -> !(entity.getVisibleClientType() == CommentVisibleClientType.APP_CLIENT.getCode())).collect(Collectors.toList());
        for (CommentProto.TransientComment transientComment : transientCommentList) {
            // 用户信息
            CommentProto.SimpleUser simpleUser = transientComment.getSimpleUser();
            // 群马甲头像
            CommentProto.QunVest qunVest = transientComment.hasQunVest() ? transientComment.getQunVest() : null;
            // 财富等级
            CommentProto.SimpleUserWealthLevel level = transientComment.hasLevel() ? transientComment.getLevel() : null;
            //明星(主播)等级
            CommentProto.SimpleUserStarLevel starLevel = transientComment.hasStarLevel() ? transientComment.getStarLevel() : null;
            //粉丝等级勋章信息
            CommentProto.FansLevelBadge fansLevelBadge = transientComment.hasFansLevelBadge() ? transientComment.getFansLevelBadge() : null;
            // 发送的用户
            List<CommentProto.ToUser> toUserList = transientComment.getToUserList();
            // 财富等级勋章信息
            CommentProto.PpWealthLevel ppWealthLevel = transientComment.hasPpWealthLevel() ? transientComment.getPpWealthLevel() : null;
            //新用户信息
            CommentProto.FreshUser freshUser = transientComment.hasFreshUser() ? transientComment.getFreshUser() : null;
            //大客户勋章
            CommentProto.VipMedal vipMedal = transientComment.hasVipMedal() ? transientComment.getVipMedal() : null;
            // 勋章
            List<CommentProto.PpMedal> ppMedalList = transientComment.getPpMedalList();
            //图片数据
            List<CommentProto.ImageData> imageDataList = transientComment.getImageDataList();
            //关系拍记录
            CommentProto.RelationPatRecord relationPatRecord = transientComment.getRelationPatRecord();
            //宝箱信息
            CommentProto.TreasureBox treasureBox = transientComment.getTreasureBox();
            //房间互动卡片数据
            CommentProto.RoomInteractionCard roomInteractionCard = transientComment.getRoomInteractionCard();
            // 佩戴中的勋章墙勋章列表
            List<CommentProto.SimpleMedal> medalWallMedalList = transientComment.getMedalWallMedalList();
            // 曲库点唱
            CommentProto.OrderSongInfo orderSongInfo = transientComment.getOrderSongInfo();
            // 房间精选歌单
            CommentProto.RoomPlaylistCard roomPlaylistCard = transientComment.getRoomPlaylistCard();
            //房间轮盘
            CommentProto.RouletteWheelExtra rouletteWheelExtra = transientComment.getRouletteWheelExtra();
            // 新用户偏好信息
            CommentProto.FreshUserInterestInfo freshUserInterestInfo = transientComment.getFreshUserInterestInfo();
            // 作品内容信息
            CommentProto.FeedContentInfo feedContentInfo = transientComment.getFeedContentInfo();

            SimpleUser simpleUserBean = generateSimpleUser(simpleUser);
            QunVest qunVestBean = generateQunVest(qunVest);
            SimpleUserWealthLevel levelBean = generateSimpleUserWealthLevel(level);
            SimpleUserStarLevel starLevelBean = generateSimpleUserStarLevel(starLevel);
            FansLevelBadge fansLevelBadgeBean = generateFansLevelBadge(fansLevelBadge);
            List<Long> toUserIds = generateToUserIdList(toUserList);
            WealthLevelDTO wealthLevelBean = generateWealthLevel(ppWealthLevel);
            FreshUser freshUserBean = generateFreshUser(freshUser);
            VipMedal vipMedalBean = generateVipMedal(vipMedal);
            List<Medal> medalBeans = generateMedals(ppMedalList, medalWallMedalList);
            List<ImageData> imageDataBeans = generateImageData(imageDataList);
            RoomInteractionCard roomInteractionCardBean = generateRoomInteractionCard(roomInteractionCard);
            RelationPatRecord relationPatRecordBean = generateRelationPatRecord(relationPatRecord);
            TreasureBox treasureBoxBean = generateTreasureBox(treasureBox);
            //曲库点唱歌曲信息
            SongInfo songInfo = generateSongInfo(orderSongInfo);
            // 房间精选歌单信息
            RoomPlaylistCommentCardBean roomPlaylistCommentCardBean = generateRoomPlaylist(roomPlaylistCard);
            // 房间轮盘信息
            RoomRouletteWheelCommentCardBean rouletteWheelCommentCardBean = RoomCommentConvert.I.toRoomRouletteWheelCommentCardBean(rouletteWheelExtra);
            // 新用户偏好信息
            FreshUserInterestBean userInterestBean = generateFreshUserInterestInfo(freshUserInterestInfo);
            // 作品内容信息
            FeedContentInfoBean feedContentInfoBean = generateFeedContentInfoBean(feedContentInfo);
            // 尾灯
            TailEffectVo tailEffectVo = generateTailEffect(simpleUser.getUserId());

            TransientComment bean = new TransientComment();
            bean.setCommentId(transientComment.getCommentId());
            bean.setContent(transientComment.getContent());
            bean.setTime(transientComment.getTime());
            bean.setIosColor(transientComment.getIosColor());
            bean.setAndroidColor(transientComment.getAndroidColor());
            bean.setUserRoles(transientComment.getUserRolesList());
            bean.setSimpleUser(simpleUserBean);
            bean.setQunVest(qunVestBean);
            bean.setLevel(levelBean);

            int commentType = CommentTypeMapping.getWaveCommentType(transientComment.getCommentType(),
                    BusinessEvnEnum.from(ContextUtils.getContext().getHeader().getAppId()),
                    transientComment.getCommentTypeExtension());

            bean.setCommentType(commentType);
            bean.setCommentTypeExtension(transientComment.getCommentTypeExtension());
            bean.setImageHeight(transientComment.getImageHeight());
            bean.setImageWidth(transientComment.getImageWidth());
            bean.setImageSize(transientComment.getImageSize());
            bean.setImageUrl(transientComment.getImageUrl());
            bean.setImageOriginal(transientComment.getImageOriginal());
            bean.setGoldNjBadgeUrl(transientComment.getGoldNjBadgeUrl());
            bean.setExclusiveBadgeUrl(transientComment.getExclusiveBadgeUrl());
            bean.setStarLevel(starLevelBean);
            bean.setFansLevelBadge(fansLevelBadgeBean);
            bean.setEmotionId(transientComment.getEmotionId());
            bean.setRepeatStopImageIndex(transientComment.getRepeatStopImageIndex());
            bean.setToUser(toUserIds);
            bean.setStyleId(transientComment.getStyleId());
            bean.setBizType(transientComment.getBizType());
            bean.setWealthLevel(wealthLevelBean);
            bean.setFreshUser(freshUserBean);
            bean.setVipMedal(vipMedalBean);
            bean.setMedal(medalBeans);
            bean.setImageData(imageDataBeans);
            bean.setRoomInteractionCard(roomInteractionCardBean);
            bean.setRelationPatRecord(relationPatRecordBean);
            bean.setTreasureBox(treasureBoxBean);
            bean.setTargetUserId(transientComment.getTargetUserId());
            bean.setOperatorUserId(transientComment.getOperatorUserId());
            bean.setSongInfo(songInfo);
            bean.setRoomPlaylistCommentCardBean(roomPlaylistCommentCardBean);
            bean.setRouletteWheelCommentCardBean(rouletteWheelCommentCardBean);
            bean.setFreshUserInterestBean(userInterestBean);
            bean.setFeedContentInfoBean(feedContentInfoBean);
            bean.setTailEffect(tailEffectVo); // 尾灯
            //如果评论类型是系统评论，且触发系统评论的用户ID存在，则使用触发者的头像
            long userId = bean.getCommentType() == WaveCommentType.SYSTEM_NOTICE_COMMENT && transientComment.getOperatorUserId() > 0 ?
                    transientComment.getOperatorUserId() : transientComment.getUserId();
            bean.setUserId(userId);
            bean.setAnonymous(transientComment.getAnonymous());

            List<CommentProto.AtUser> atusersList = transientComment.getAtusersList();
            if (!CollectionUtils.isEmpty(atusersList)) {
                bean.setAtUsers(atusersList.stream()
                        .map(v -> new AtUser().setUserId(v.getUserId()).setName(v.getName()))
                        .collect(Collectors.toList())
                );
            }

            transientComments.add(bean);
        }

        GetCommentWithServerTimeResult getCommentWithServerTimeResult = new GetCommentWithServerTimeResult();
        getCommentWithServerTimeResult.setTransientComments(transientComments);
        getCommentWithServerTimeResult.setCommentServerTime(Math.min(responseGetTransientComment.getCommentServerTime(), param.getEndTime()));
        getCommentWithServerTimeResult.setLastPage(param.getCount() > transientComments.size());
        return new Result<>(result.rCode(), getCommentWithServerTimeResult);
    }

    /**
     * 获取评论审核状态
     *
     * @param param 获取评论审核状态参数
     * @return 评论审核状态
     */
    @Override
    public Result<List<GetTransientCommentReviewResult>> getTransientCommentReview(
            GetTransientCommentReviewParam param) {
        Result<CommentProto.ResponseGetTransientCommentReview> result =
                transientCommentService.getTransientCommentReview(param.getLiveId(), param.getCommentIds());
        if (result.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
            log.warn("xm getTransientCommentReview fail, rCode={}`param={}", result.rCode(), param);
            return new Result<>(result.rCode(), null);
        }

        List<CommentProto.TransientCommentReview> commentReviewList =
                result.target().getTransientCommentReviewList();
        List<GetTransientCommentReviewResult> transientCommentReviewResults = new ArrayList<>();
        for (CommentProto.TransientCommentReview transientCommentReview : commentReviewList) {
            GetTransientCommentReviewResult commentReviewResult = new GetTransientCommentReviewResult();
            commentReviewResult.setCommentId(transientCommentReview.getCommentId());
            commentReviewResult.setReviewStatus(transientCommentReview.getReviewStatus());

            transientCommentReviewResults.add(commentReviewResult);
        }

        return new Result<>(result.rCode(), transientCommentReviewResults);
    }


    private TreasureBox generateTreasureBox(CommentProto.TreasureBox treasureBox) {
        TreasureBox treasureBoxBean = new TreasureBox();
        treasureBoxBean.setTreasureBoxId(treasureBox.getTreasureBoxId());
        treasureBoxBean.setUserId(treasureBox.getUserId());
        treasureBoxBean.setLiveId(treasureBox.getLiveId());
        treasureBoxBean.setTreasureBoxName(treasureBox.getTreasureBoxName());
        treasureBoxBean.setType(treasureBox.getType());
        treasureBoxBean.setCommand(treasureBox.getCommand());
        treasureBoxBean.setAmountLimit(treasureBox.getAmountLimit());
        treasureBoxBean.setGuestLimit(treasureBox.getGuestLimit());
        return treasureBoxBean;
    }

    private RelationPatRecord generateRelationPatRecord(CommentProto.RelationPatRecord relationPatRecord) {
        RelationPatRecord relationPatRecordBean = new RelationPatRecord();
        relationPatRecordBean.setFromUid(relationPatRecord.getFromUid());
        relationPatRecordBean.setTargetUid(relationPatRecord.getTargetUid());
        relationPatRecordBean.setAnimation(relationPatRecord.getAnimation());
        return relationPatRecordBean;
    }

    private RoomInteractionCard generateRoomInteractionCard(CommentProto.RoomInteractionCard roomInteractionCard) {
        RoomInteractionCard roomInteractionCardBean = new RoomInteractionCard();
        roomInteractionCardBean.setTitle(roomInteractionCard.getTitle());
        roomInteractionCardBean.setPortrait(roomInteractionCard.getPortrait());
        roomInteractionCardBean.setSeatNum(roomInteractionCard.getSeatNum());
        roomInteractionCardBean.setRightTxt(roomInteractionCard.getRightTxt());
        roomInteractionCardBean.setCompereUserId(roomInteractionCard.getCompereUserId());
        return roomInteractionCardBean;
    }

    private List<ImageData> generateImageData(List<CommentProto.ImageData> imageDataList) {
        List<ImageData> imageDataBeans = new ArrayList<>();
        for (CommentProto.ImageData imageData : imageDataList) {
            ImageData imageDataBean = new ImageData();
            imageDataBean.setIndex(imageData.getIndex());
            imageDataBean.setImagesUrl(imageData.getImagesUrlList());
            imageDataBeans.add(imageDataBean);
        }
        return imageDataBeans;
    }

    private List<Medal> generateMedals(List<CommentProto.PpMedal> ppMedalList, List<CommentProto.SimpleMedal> medalWallMedalList) {
        List<Medal> medalBeans = new ArrayList<>();
        for (CommentProto.PpMedal ppMedal : ppMedalList) {
            Medal medalBean = new Medal();
            medalBean.setCover(ppMedal.getCover());
            medalBean.setAspect(ppMedal.getAspect());
            medalBeans.add(medalBean);
        }
        for (CommentProto.SimpleMedal simpleMedal : medalWallMedalList) {
            Medal medalBean = new Medal();
            medalBean.setCover(simpleMedal.getImage());
            medalBean.setAspect(simpleMedal.getAspect());
            medalBeans.add(medalBean);
        }
        return medalBeans;
    }

    private VipMedal generateVipMedal(CommentProto.VipMedal vipMedal) {
        if (vipMedal == null) {
            return null;
        }
        VipMedal vipMedalBean = new VipMedal();
        vipMedalBean.setImage(vipMedal.getImage());
        vipMedalBean.setAspect(vipMedal.getAspect());
        vipMedalBean.setPriority(vipMedal.getPriority());
        return vipMedalBean;
    }

    private VipLevel generateVipLevel(CommentProto.PpVipLevel ppVipLevel) {
        if (ppVipLevel == null) {
            return null;
        }
        VipLevel vipLevelBean = new VipLevel();
        vipLevelBean.setLevel(ppVipLevel.getLevel());
        vipLevelBean.setCover(ppVipLevel.getCover());
        vipLevelBean.setAspect(ppVipLevel.getAspect());
        return vipLevelBean;
    }

    private FreshUser generateFreshUser(CommentProto.FreshUser freshUser) {
        if (freshUser == null) {
            return null;
        }
        FreshUser freshUserBean = new FreshUser();
        freshUserBean.setIs(freshUser.getIs());
        return freshUserBean;
    }

    private WealthLevelDTO generateWealthLevel(CommentProto.PpWealthLevel ppWealthLevel) {
        if (ppWealthLevel == null) {
            return null;
        }
        WealthLevelDTO wealthLevelBean = new WealthLevelDTO();
        wealthLevelBean.setLevel(ppWealthLevel.getLevel());
        wealthLevelBean.setCover(ppWealthLevel.getCover());
        wealthLevelBean.setAspect(ppWealthLevel.getAspect());
        return wealthLevelBean;
    }

    private List<Long> generateToUserIdList(List<CommentProto.ToUser> toUserList) {
        List<Long> toUserIds = new ArrayList<>();
        for (CommentProto.ToUser toUser : toUserList) {
            toUserIds.add(toUser.getUserId());
        }
        return toUserIds;
    }

    private FansLevelBadge generateFansLevelBadge(CommentProto.FansLevelBadge fansLevelBadge) {
        if (fansLevelBadge == null) {
            return null;
        }
        FansLevelBadge fansLevelBadgeBean = new FansLevelBadge();
        fansLevelBadgeBean.setImage(fansLevelBadge.getImage());
        fansLevelBadgeBean.setAspect(fansLevelBadge.getAspect());
        return fansLevelBadgeBean;
    }

    private SimpleUserStarLevel generateSimpleUserStarLevel(CommentProto.SimpleUserStarLevel starLevel) {
        if (starLevel == null) {
            return null;
        }
        SimpleUserStarLevel starLevelBean = new SimpleUserStarLevel();
        starLevelBean.setLevel(starLevel.getLevel());
        starLevelBean.setCover(starLevel.getCover());
        starLevelBean.setAspect(starLevel.getAspect());
        return starLevelBean;
    }

    private SimpleUserWealthLevel generateSimpleUserWealthLevel(CommentProto.SimpleUserWealthLevel level) {
        if (level == null) {
            return null;
        }
        SimpleUserWealthLevel levelBean = new SimpleUserWealthLevel();
        levelBean.setLevel(level.getLevel());
        levelBean.setCover(level.getCover());
        levelBean.setAspect(level.getAspect());
        return levelBean;
    }

    private QunVest generateQunVest(CommentProto.QunVest qunVest) {
        if (qunVest == null) {
            return null;
        }
        QunVest qunVestBean = new QunVest();
        qunVestBean.setImage(qunVest.getImage());
        qunVestBean.setAspect(qunVest.getAspect());
        return qunVestBean;
    }

    private SimpleUser generateSimpleUser(CommentProto.SimpleUser simpleUser) {
        SimpleUser simpleUserBean = new SimpleUser();
        simpleUserBean.setUserId(simpleUser.getUserId());
        simpleUserBean.setName(simpleUser.getName());
        simpleUserBean.setPortrait(simpleUser.getPortrait());
        simpleUserBean.setGender(simpleUser.getGender());
        List<String> roomVipUrlsList = simpleUser.getRoomVipUrlsList();
        // 贵宾卡标识
        simpleUserBean.setRoomVipUrls(roomVipUrlsList);

        //用户的昵称变色信息
        List<Long> nameColorsList = simpleUser.getUserNameColorsList();
        simpleUserBean.setNameColorsList(nameColorsList);
        //贵宾等级
        simpleUserBean.setUserRoomVipStatus(simpleUser.getUserRoomVipStatus());
        return simpleUserBean;
    }

    private SongInfo generateSongInfo(CommentProto.OrderSongInfo orderSongInfo) {
        if (orderSongInfo == null) {
            return null;
        }
        SongInfo songInfo = new SongInfo();
        songInfo.setSingerName(orderSongInfo.getSingerName());
        songInfo.setSongCover(orderSongInfo.getSongCover());
        songInfo.setSongName(orderSongInfo.getSongName());
        songInfo.setTitle(orderSongInfo.hasTitle() ? orderSongInfo.getTitle() : null);
        songInfo.setTargetUserId(orderSongInfo.getTargetUserId());
        songInfo.setSongId(orderSongInfo.getSongId());
        songInfo.setAvatar(orderSongInfo.getAvatar());
        songInfo.setSeatIndex(orderSongInfo.getSeatIndex());
        return songInfo;
    }

    private RoomPlaylistCommentCardBean generateRoomPlaylist(CommentProto.RoomPlaylistCard roomPlaylistCard) {
        if (roomPlaylistCard == null) {
            return null;
        }
        List<CommentProto.RoomSongInfo> songListList = roomPlaylistCard.getSongListList();
        List<RoomPlaylistCommentCardBean.RoomSongInfo> roomSongInfos = Lists.newArrayList();
        for (CommentProto.RoomSongInfo roomSongInfo : songListList) {
            roomSongInfos.add(RoomPlaylistCommentCardBean.RoomSongInfo.builder()
                    .poster(roomSongInfo.getPoster())
                    .songName(roomSongInfo.getSongName())
                    .build()
            );
        }
        return RoomPlaylistCommentCardBean.builder()
                .buttonTitle(roomPlaylistCard.getButtonTitle())
                .title(roomPlaylistCard.getTitle())
                .subTitle(roomPlaylistCard.getSubTitle())
                .songList(roomSongInfos)
                .build();
    }

    private FreshUserInterestBean generateFreshUserInterestInfo(CommentProto.FreshUserInterestInfo interestInfo) {
        if (interestInfo == null) {
            return null;
        }
        FreshUserInterestBean userInterestBean = new FreshUserInterestBean();
        userInterestBean.setTitle(StringUtils.isEmpty(interestInfo.getTitle()) ? "" : interestInfo.getTitle());
        userInterestBean.setDisposition(StringUtils.isEmpty(interestInfo.getDisposition()) ? "" : interestInfo.getDisposition());
        userInterestBean.setThing(StringUtils.isEmpty(interestInfo.getThing()) ? "" : interestInfo.getThing());
        userInterestBean.setVoice(StringUtils.isEmpty(interestInfo.getVoice()) ? "" : interestInfo.getVoice());
        return userInterestBean;
    }

    private FeedContentInfoBean generateFeedContentInfoBean(CommentProto.FeedContentInfo feedContentInfo) {
        if (feedContentInfo == null) {
            return null;
        }
        FeedContentInfoBean feedContentInfoBean = new FeedContentInfoBean();
        feedContentInfoBean.setType(feedContentInfo.getType());
        feedContentInfoBean.setFeedContentId(feedContentInfo.getFeeContentId());
        feedContentInfoBean.setDescription(StringUtils.isEmpty(feedContentInfo.getDescription()) ? "" : feedContentInfo.getDescription());
        feedContentInfoBean.setPreview(StringUtils.isEmpty(feedContentInfo.getPreview()) ? "" : feedContentInfo.getPreview());
        return feedContentInfoBean;
    }
    /**
     * 生成尾灯
     * @param userId
     * @return
     */
    private TailEffectVo generateTailEffect(long userId){
        Result<GetTailEffectResult> result = userService.getTailEffect(userId);
        if(result.rCode()!=GeneralRCode.GENERAL_RCODE_SUCCESS || result.target()==null){
            return null;
        }
        GetTailEffectResult target = result.target();
        return TailEffectVo.builder()
                .effectUrl(target.getTailEffectUrl())
                .id(target.getId())
                .build();
    }

}
