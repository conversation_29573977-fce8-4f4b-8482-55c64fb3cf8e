package fm.lizhi.ocean.wave.comment.core.convert;

import fm.lizhi.ocean.wave.comment.core.model.dto.EnterNoticeDTO;
import fm.lizhi.ocean.wave.comment.core.model.vo.EnterMsgVO;
import fm.lizhi.ocean.wave.comment.core.model.vo.MsgUserVO;
import fm.lizhi.ocean.wave.comment.core.model.vo.UserVO;
import fm.lizhi.ocean.wave.comment.core.remote.bean.EnterNoticeEntry;
import fm.lizhi.ocean.wave.user.bean.SimpleUser;
import fm.lizhi.ocean.wave.user.bean.UserAvatarWidget;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.Map;

/**
 * <AUTHOR>
 */
@Mapper(collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        /* 未映射目标属性的报告策略 */
        unmappedTargetPolicy = ReportingPolicy.ERROR,
        /* 确定何时对bean映射的源属性值包含空检查 */
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface EnterNoticeMsgConvert {

    EnterNoticeMsgConvert I = Mappers.getMapper(EnterNoticeMsgConvert.class);

    /**
     * 转换进房消息
     *
     * @param enterNoticeEntry 进房消息实体
     * @param userVO           用户信息
     * @return 结果
     */
    @Mapping(target = "user", source = "userVO")
    @Mapping(target = "content", source = "enterNoticeEntry.notice")
    @Mapping(target = "userId", source = "enterNoticeEntry.userId")
    @Mapping(target = "isShowButton", source = "enterNoticeEntry.hyEnterNoticeExtraDTO.showButton")
    EnterMsgVO convertNoticeVO(EnterNoticeDTO enterNoticeEntry, MsgUserVO<?> userVO);

    /**
     * 获取用户信息
     *
     * @param enterNoticeEntry    进房消息
     * @param userAvatarWidgetMap 用户头像框map
     * @param simpleUserMap       简单用户信息map
     * @param userVO              用户信息VO
     * @return 结果
     */
    default MsgUserVO<?> buildUserVO(EnterNoticeDTO enterNoticeEntry, Map<Long, UserAvatarWidget> userAvatarWidgetMap,
                                     Map<Long, SimpleUser> simpleUserMap, MsgUserVO<?> userVO) {
        // 获取用户信息
        SimpleUser user = simpleUserMap.get(enterNoticeEntry.getUserId());
        if (user == null) {
            return null;
        }

        UserAvatarWidget avatarWidget = userAvatarWidgetMap.get(enterNoticeEntry.getUserId());
        // 处理用户信息
        userVO.setUserAvatarWidget(avatarWidget);
        userVO.setUserId(String.valueOf(user.getUserId()));
        userVO.setUserName(user.getNickName());
        userVO.setUserAvatar(user.getAvatar());
        if (!StringUtils.isEmpty(enterNoticeEntry.getUserCover())) {
            userVO.setUserId(String.valueOf(enterNoticeEntry.getUserId()));
            userVO.setUserAvatar(enterNoticeEntry.getUserCover());
        }

        return userVO;
    }

}
