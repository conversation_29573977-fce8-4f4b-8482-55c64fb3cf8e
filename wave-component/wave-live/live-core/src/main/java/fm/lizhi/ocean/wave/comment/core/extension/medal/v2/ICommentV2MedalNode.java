package fm.lizhi.ocean.wave.comment.core.extension.medal.v2;

import fm.lizhi.ocean.wave.comment.core.extension.medal.bean.GenMedalInfoV2Context;
import fm.lizhi.ocean.wave.comment.core.model.vo.BadgeImageVO;

import java.util.List;
import java.util.Optional;

/**
 * 评论勋章节点
 * <AUTHOR>
 */
public interface ICommentV2MedalNode {

    /**
     * 构建财富勋章图片信息
     *
     * @param context 评论信息
     * @return 结果
     */
    Optional<List<BadgeImageVO>> buildMedalImageInfo(GenMedalInfoV2Context context);
}
