package fm.lizhi.ocean.wave.comment.core.manager.filter;

import fm.lizhi.ocean.wave.comment.core.model.dto.TransientCommentDTO;

import java.util.ArrayList;
import java.util.List;

/**
 * 评论过滤器 - 指定用户
 * <AUTHOR>
 */
public class CommentMsgToUserFilter implements CommentV2Filter {

    /**
     * 指定用户id
     */
    private final Long toUserId;

    public CommentMsgToUserFilter(Long toUserId) {
        this.toUserId = toUserId;
    }

    @Override
    public List<TransientCommentDTO> filter(List<TransientCommentDTO> comments) {
        List<TransientCommentDTO> result = new ArrayList<>();
        for (TransientCommentDTO comment : comments) {
            if (comment.getToUser().isEmpty()) {
                result.add(comment);
            } else {
                for (Long userId : comment.getToUser()) {
                    if (this.toUserId.equals(userId)) {
                        result.add(comment);
                        break;
                    }
                }
            }
        }

        return result;
    }
}
