package fm.lizhi.ocean.wave.comment.core.extension.comment;

import fm.lizhi.ocean.wave.comment.core.config.CommentCommonConfig;
import fm.lizhi.ocean.wave.comment.core.extension.medal.v2.ICommentV2MedalNode;
import fm.lizhi.ocean.wave.comment.core.model.bean.ICommentBean;
import fm.lizhi.ocean.wave.comment.core.model.dto.TransientCommentDTO;
import fm.lizhi.ocean.wave.comment.core.model.vo.MsgUserVO;
import fm.lizhi.ocean.wave.comment.core.model.vo.CommentVO;
import fm.lizhi.ocean.wave.common.extension.BusinessEnvAwareProcessor;
import fm.lizhi.ocean.wave.server.common.vo.ResultVO;

import java.util.List;

/**
 * Description: 获取直播间消息处理类（评论和进房公告）
 *
 * <AUTHOR>
 */
public interface IGetCommentMsgProcessor extends BusinessEnvAwareProcessor {

    @Override
    default Class<? extends BusinessEnvAwareProcessor> getBaseBusinessProcessor() {
        return IGetCommentMsgProcessor.class;
    }

    /**
     * 创建用户信息和补全扩展信息
     *
     * @param transientComment 评论数据
     * @return 结果
     */
    MsgUserVO<?> createUserAndFillBizExtra(TransientCommentDTO transientComment);

    /**
     * 适配用户信息
     *
     * @param transientComment 评论数据
     * @return 结果
     */
    void adapterUserExtra(TransientCommentDTO transientComment, MsgUserVO<?> userVO);

    /**
     * 获取业务公共评论配置
     *
     * @return 评论配置
     */
    CommentCommonConfig getCommentConfig();

    /**
     * 获取评论勋章节点
     *
     * @return 勋章节点
     */
    List<ICommentV2MedalNode> getBuildCommentMedalNodes();

    /**
     * 重置评论用户属性信息
     *
     * @param transientComment 评论信息
     * @return 结果
     */
    ResultVO<TransientCommentDTO> resetCommentUserProperty(TransientCommentDTO transientComment);

    /**
     * 补充进房消息其他的勋章
     *
     * @param transientComment 评论消息
     */
    void fillEnterCommentMedal(TransientCommentDTO transientComment);

    /**
     * 补全评论的扩展信息
     *
     * @param commentVO        评论结果
     * @param transientComment 评论数据流
     */
    void fillCommentExtraInfo(CommentVO commentVO, TransientCommentDTO transientComment);

    /**
     * 处理互动进房消息
     *
     * @param transientCommentDTOList 消息中间层数据
     * @param commentVOList           评论结果
     * @return 结果
     */
    List<ICommentBean> processSpecialEnterMsg(List<TransientCommentDTO> transientCommentDTOList, List<CommentVO> commentVOList);

}
