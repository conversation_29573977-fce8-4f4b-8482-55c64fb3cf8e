package fm.lizhi.ocean.wave.comment.core.extension.comment;

import fm.lizhi.ocean.wave.comment.core.config.CommentCommonConfig;
import fm.lizhi.ocean.wave.comment.core.extension.medal.v2.ICommentV2MedalNode;
import fm.lizhi.ocean.wave.comment.core.model.dto.EnterNoticeDTO;
import fm.lizhi.ocean.wave.comment.core.model.vo.EnterMsgVO;
import fm.lizhi.ocean.wave.comment.core.model.vo.MsgUserVO;
import fm.lizhi.ocean.wave.common.extension.BusinessEnvAwareProcessor;

import java.util.List;

/**
 * Description: 获取进房公告特殊消息
 *
 * <AUTHOR>
 */
public interface IEnterNoticeMsgProcessor extends BusinessEnvAwareProcessor {

    @Override
    default Class<? extends BusinessEnvAwareProcessor> getBaseBusinessProcessor() {
        return IEnterNoticeMsgProcessor.class;
    }

    /**
     * 适配用户信息
     *
     * @param enterNoticeEntry 进房消息
     * @return 结果
     */
    MsgUserVO<?> buildUserVoAndAdapterExtra(EnterNoticeDTO enterNoticeEntry);

    /**
     * 获取业务公共评论配置
     *
     * @return 评论配置
     */
    CommentCommonConfig getCommentConfig();

    /**
     * 过滤进房公告特效
     *
     * @param enterNoticeEntries 用户ID
     * @return 过滤后的进房公告
     */
    List<EnterNoticeDTO> filterHideEnterNotice(List<EnterNoticeDTO> enterNoticeEntries);

    /**
     * 获取进房公告勋章节点
     *
     * @return 勋章节点
     */
    List<ICommentV2MedalNode> getBuildEnterNoticeMedalNodes();

    /**
     * 是否展示按钮
     *
     * @param liveId           直播节目ID
     * @param enterNoticeEntry 进房公告
     * @param userId           请求的用户id
     * @param enterNoticeVO    进房消息转换结果
     */
    void setShowButton(long liveId, long userId, EnterNoticeDTO enterNoticeEntry, EnterMsgVO enterNoticeVO);

    /**
     * 是否有权限查询进房公告
     *
     * @param liveId 直播节目ID
     * @return true: 是可以获取
     */
    boolean isCanGetEnterNotice(long liveId);

    /**
     * 大主播屏蔽座驾
     *
     * @param njId 主播ID
     * @return 结果
     */
    boolean isBigStarNjHideMount(long njId);
}
