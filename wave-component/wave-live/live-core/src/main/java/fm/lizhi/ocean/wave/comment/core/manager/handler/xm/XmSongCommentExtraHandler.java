package fm.lizhi.ocean.wave.comment.core.manager.handler.xm;

import fm.lizhi.ocean.wave.comment.core.config.CommentConfig;
import fm.lizhi.ocean.wave.comment.core.constants.WaveCommentType;
import fm.lizhi.ocean.wave.comment.core.convert.XmSpecialActivityCommentConvert;
import fm.lizhi.ocean.wave.comment.core.manager.handler.SpecialActivityCommentExtraHandler;
import fm.lizhi.ocean.wave.comment.core.model.bean.xm.XmSongSpecialActivityCommentExtra;
import fm.lizhi.ocean.wave.comment.core.model.vo.SpecialActivityCommentVO;
import fm.lizhi.ocean.wave.comment.core.remote.bean.SongInfo;
import fm.lizhi.ocean.wave.common.config.CommonProviderConfig;
import fm.lizhi.ocean.wave.common.util.JsonUtils;
import fm.lizhi.ocean.wave.common.util.UrlUtils;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import xm.fm.lizhi.datacenter.comment.pp.bean.OrderSongInfoBean;
import xm.fm.lizhi.datacenter.comment.pp.bean.SpecialActivityCommentKafkaBean;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class XmSongCommentExtraHandler implements SpecialActivityCommentExtraHandler<SongInfo> {

    @Autowired
    private CommentConfig commentConfig;

    @Autowired
    private CommonProviderConfig commonProviderConfig;

    @Override
    public SongInfo convertExtraDTO(String json) {
        SpecialActivityCommentKafkaBean specialActivityCommentBean = JsonUtils.fromJsonString(json, SpecialActivityCommentKafkaBean.class);
        OrderSongInfoBean orderSongInfoBean = specialActivityCommentBean.getCommentInfo().getOrderSongInfoBean();
        if (orderSongInfoBean == null) {
            return null;
        }

        return XmSpecialActivityCommentConvert.I.convertSongInfo(orderSongInfoBean);
    }

    @Override
    public void processActivityCommentExtra(SongInfo songInfo, SpecialActivityCommentVO commentVO) {
        if (songInfo == null || commentVO.getCommentType() != this.getCommentType()) {
            return;
        }
        //构建处理点唱玩法的评论信息
        String cdnHost = commonProviderConfig.getBusinessConfig(BusinessEvnEnum.XIMI.getAppId()).getCdnHost();
        XmSongSpecialActivityCommentExtra commentExtra = XmSpecialActivityCommentConvert.I.convertSongCommentBizExtra(songInfo);
        commentExtra.setSongCover(UrlUtils.addCdnHost(cdnHost, songInfo.getSongCover()));
        commentExtra.setAvatar(UrlUtils.addCdnHost(cdnHost, songInfo.getAvatar()));
        commentVO.setActivityExtra(commentExtra);
        commentVO.setContent(songInfo.getTitle() == null ? commentConfig.getXm().getOrderSongCommentTitle() : songInfo.getTitle());
    }

    @Override
    public int getCommentType() {
        return WaveCommentType.ORDER_SONG;
    }

    @Override
    public int getAppId() {
        return BusinessEvnEnum.XIMI.getAppId();
    }

}
