package fm.lizhi.ocean.wave.comment.core.model.vo;

import fm.lizhi.ocean.wave.comment.core.model.bean.MsgUserExtraInfo;
import fm.lizhi.ocean.wave.comment.core.remote.bean.UserMountEntry;
import lombok.Data;

/**
 * 进房公告VO
 *
 * <AUTHOR>
 */
@Data
public class EnterMsgVO {
    /**
     * 用户ID
     */
    private String userId;
    /**
     * 用户信息
     */
    private MsgUserVO<? extends MsgUserExtraInfo> user;
    /**
     * 内容
     */
    private String content;
    /**
     * 数量
     */
    private Integer count;

    /**
     * 是否展示欢迎按钮
     */
    private Boolean isShowButton;

    /**
     * 进房时间戳
     */
    private Long timeStamp;

    /**
     * 用户座驾信息
     */
    private UserMountEntry userMountEntry;

}
