package fm.lizhi.ocean.wave.comment.core.manager;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Strings;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.commons.service.client.rcode.GeneralRCode;
import fm.lizhi.commons.util.DateUtil;
import fm.lizhi.commons.util.GuidGenerator;
import fm.lizhi.ocean.wave.comment.core.config.CommentCommonConfig;
import fm.lizhi.ocean.wave.comment.core.config.CommentConfig;
import fm.lizhi.ocean.wave.comment.core.constants.*;
import fm.lizhi.ocean.wave.comment.core.dao.redis.CommentRedis;
import fm.lizhi.ocean.wave.comment.core.extension.comment.*;
import fm.lizhi.ocean.wave.comment.core.extension.comment.bean.*;
import fm.lizhi.ocean.wave.comment.core.extension.emotion.IEmotionProcessor;
import fm.lizhi.ocean.wave.comment.core.extension.emotion.bean.EmotionPostBean;
import fm.lizhi.ocean.wave.comment.core.extension.medal.ICommentMedalNode;
import fm.lizhi.ocean.wave.comment.core.extension.medal.bean.GenMedalInfoContext;
import fm.lizhi.ocean.wave.comment.core.manager.handler.CommentTypeHandler;
import fm.lizhi.ocean.wave.comment.core.manager.handler.CommentTypeHandlerFactory;
import fm.lizhi.ocean.wave.comment.core.model.dto.CommentTypeDTO;
import fm.lizhi.ocean.wave.comment.core.model.mapper.CommentMapper;
import fm.lizhi.ocean.wave.comment.core.model.param.*;
import fm.lizhi.ocean.wave.comment.core.model.result.EmotionResult;
import fm.lizhi.ocean.wave.comment.core.model.result.LatestCommentsResult;
import fm.lizhi.ocean.wave.comment.core.model.result.SyncCommentResult;
import fm.lizhi.ocean.wave.comment.core.model.vo.*;
import fm.lizhi.ocean.wave.comment.core.remote.bean.*;
import fm.lizhi.ocean.wave.comment.core.remote.param.*;
import fm.lizhi.ocean.wave.comment.core.remote.result.GetCommentWithServerTimeResult;
import fm.lizhi.ocean.wave.comment.core.remote.result.GetTotalEmotionsResult;
import fm.lizhi.ocean.wave.comment.core.remote.result.GetTransientCommentReviewResult;
import fm.lizhi.ocean.wave.comment.core.remote.service.IFamilyServiceRemote;
import fm.lizhi.ocean.wave.comment.core.remote.service.ILiveCommentServiceRemote;
import fm.lizhi.ocean.wave.comment.core.remote.service.ILiveEmotionServiceRemote;
import fm.lizhi.ocean.wave.comment.core.remote.service.IPlayerQuestServiceRemote;
import fm.lizhi.ocean.wave.common.auto.route.common.remote.MyAutowired;
import fm.lizhi.ocean.wave.common.config.CommonProviderConfig;
import fm.lizhi.ocean.wave.common.extension.ProcessorFactory;
import fm.lizhi.ocean.wave.common.extension.ProcessorV2Factory;
import fm.lizhi.ocean.wave.common.model.BusinessConfig;
import fm.lizhi.ocean.wave.common.util.UrlUtils;
import fm.lizhi.ocean.wave.live.core.remote.bean.LiveBean;
import fm.lizhi.ocean.wave.live.core.remote.param.GetLiveRemoteParam;
import fm.lizhi.ocean.wave.live.core.remote.result.GetLiveRemoteResult;
import fm.lizhi.ocean.wave.live.core.remote.service.ILiveServiceRemote;
import fm.lizhi.ocean.wave.platform.api.live.param.GetKickOutStatusReq;
import fm.lizhi.ocean.wave.platform.api.live.service.LiveKickOutService;
import fm.lizhi.ocean.wave.server.common.util.ContextUtils;
import fm.lizhi.ocean.wave.server.common.vo.ResultVO;
import fm.lizhi.ocean.wave.user.api.UserService;
import fm.lizhi.ocean.wave.user.api.UserWealthLevelService;
import fm.lizhi.ocean.wave.user.bean.SimpleUser;
import fm.lizhi.ocean.wave.user.bean.UserAvatarWidget;
import fm.lizhi.ocean.wave.user.param.BatchGetUserParam;
import fm.lizhi.ocean.wave.user.result.BatchGetSimpleUserResult;
import fm.lizhi.ocean.wave.user.result.FreshUserResult;
import fm.lizhi.ocean.wave.user.result.GetSimpleUserResult;
import fm.lizhi.ocean.wave.user.result.WealthLevelResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.StopWatch;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static fm.lizhi.ocean.wave.comment.core.constants.WaveCommentType.GENERAL_COMMENT;

/**
 * 评论
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class CommentManager {

    @MyAutowired
    private ILiveEmotionServiceRemote liveEmotionServiceRemote;
    @Autowired
    private UserService userService;
    @MyAutowired
    private ILiveServiceRemote liveServiceRemote;
    @MyAutowired
    private ILiveCommentServiceRemote liveCommentServiceRemote;
    @Autowired
    private GuidGenerator guidGenerator;
    @Autowired
    private CommentConfig commentConfig;
    @Autowired
    private CommonProviderConfig commonConfig;
    @Autowired
    private CommentFilterManager commentFilterManager;

    @Autowired
    private ProcessorFactory processorFactory;
    @Autowired
    private CommentRedis commentRedis;

    @Autowired
    private UserWealthLevelService userWealthLevelService;

    @Autowired
    private CommentTypeHandlerFactory commentTypeHandlerFactory;

    @Resource
    private ProcessorV2Factory processorV2Factory;

    @Resource
    private IFamilyServiceRemote familyServiceRemote;

    @MyAutowired
    private IPlayerQuestServiceRemote playerQuestServiceRemote;

    @Autowired
    private EnterNoticeManager enterNoticeManager;

    @Autowired
    private PhoneNumCheckManager phoneNumCheckManager;

    @Autowired
    private RushCheckManager rushCheckManager;

    @Autowired
    private CommentMonitorManager commentMonitorManager;

    @Autowired
    private LiveKickOutService liveKickOutService;


    /**
     * 获取表情包结果
     *
     * @param emotionVersion 表情宝版本好
     * @return 表情包列表
     */
    public EmotionResult getEmotionResult(int emotionVersion, Integer type, Integer loadDiyEmotion) {

        // 平台自身前置检验
        BusinessConfig businessConfig = commonConfig.getBusinessConfig(
                ContextUtils.getContext().getHeader().getAppId());
        if (businessConfig == null) {
            return null;
        }

        IEmotionProcessor processor = processorFactory.getProcessor(
                businessConfig.getAppId(), IEmotionProcessor.class);

        // 业务前置检验
        ResultVO<Void> preprocessor = processor.preprocessor(null);
        if (preprocessor.getRCode() != 0) {
            return null;
        }

        // 核心接口调用
        GetTotalEmotionsParam getTotalEmotionsParam = new GetTotalEmotionsParam();
        getTotalEmotionsParam.setVersion(emotionVersion);
        getTotalEmotionsParam.setType(type);
        Result<GetTotalEmotionsResult> resp = liveEmotionServiceRemote.getTotalEmotions(getTotalEmotionsParam);
        if (resp.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
            return null;
        }

        GetTotalEmotionsResult totalEmotionsResult = resp.target();
        // 业务后置处理
        EmotionPostBean emotionPostBean = new EmotionPostBean();
        emotionPostBean.setGetTotalEmotionsResult(totalEmotionsResult);
        emotionPostBean.setEmotionVersion(emotionVersion);
        emotionPostBean.setType(type);
        emotionPostBean.setBusinessConfig(businessConfig);
        emotionPostBean.setLoadDiyEmotion(loadDiyEmotion);

        ResultVO<EmotionResult> postprocessor = processor.postprocessor(emotionPostBean);
        if (!postprocessor.isOK()) {
            return null;
        }

        return postprocessor.getData();
    }

    /**
     * 生成评论id
     *
     * @return 评论id
     */
    public Long generateCommentId() {
        long commentId = guidGenerator.genProgCommentId();
        // 用户id
        long userId = ContextUtils.getContext().getUserId();
        commentRedis.saveCommentIdByUserId(userId, commentId);
        return commentId;
    }

    /**
     * 批量生成评论id
     *
     * @return 评论id
     */
    public List<String> batchGenerateCommentId() {
        int genCommentIdLimit = commentConfig.getGenCommentIdLimit();
        List<String> ids = new ArrayList<>();
        for (int i = 0; i < genCommentIdLimit; i++) {
            ids.add(String.valueOf(guidGenerator.genProgCommentId()));
        }

        // 用户id
        long userId = ContextUtils.getContext().getUserId();
        commentRedis.batchSaveCommentIdByUserId(userId, ids);
        return ids;
    }

    /**
     * 发送评论
     *
     * @return 评论结果
     */
    public Pair<Integer, String> sendComment(int appId, SendWordCommentParam param) {
        Long commentId = param.getCommentId();
        Long liveId = param.getLiveId();
        String content = param.getContent();
        Integer commentType = param.getCommentType();
        Long targetUserId = param.getTargetUserId();

        // 1.0 获取直播信息
        LiveBean liveInfo = getLiveInfo(liveId);
        if (liveInfo == null) {
            log.warn("sendComment live not exist, liveId:{}", liveId);
            return Pair.of(CommentMsgCodes.LIVE_NOT_EXIST.getCode(), CommentMsgCodes.LIVE_NOT_EXIST.getMsg());
        }

        // 1.1 检查是否被踢出
        if (commentConfig.isCheckKickOut()) {
            Result<Boolean> kickOutResult = liveKickOutService.isKickOut(GetKickOutStatusReq.builder().liveRoomId(liveInfo.getLiveRoomId()).targetUserId(ContextUtils.getContext().getUserId()).build());
            // 调用失败默认放行
            if (kickOutResult.rCode() == GeneralRCode.GENERAL_RCODE_SUCCESS && kickOutResult.target()) {
                log.info("user sendComment but kickOut, liveRoomId={}, targetUserId={}", liveInfo.getLiveRoomId(), targetUserId);
                return Pair.of(CommentMsgCodes.SEND_COMMENT_KICK_OUT.getCode(), CommentMsgCodes.SEND_COMMENT_KICK_OUT.getMsg());
            }
        }

        // 1 平台自身前置检验
        // 1.1 获取用户信息
        SimpleUser userInfo = getUser(ContextUtils.getContext().getUserId());
        if (userInfo == null) {
            return Pair.of(CommentMsgCodes.SEND_COMMENT_ERROR.getCode(), CommentMsgCodes.SEND_COMMENT_ERROR.getMsg());
        }

        // 1.2 检查评论id是否存在
        if (!commentRedis.checkCommentIdByUserId(userInfo.getUserId(), commentId)) {
            log.warn("commentId not exist, commentId:{}", commentId);
            return Pair.of(CommentMsgCodes.COMMENT_ID_NOT_EXIST.getCode(), CommentMsgCodes.SEND_COMMENT_ERROR.getMsg());
        }

        // 海外手机受限检测
        ResultVO<Void> checkOverseaPhoneNumResultVO = phoneNumCheckManager.checkOverseaPhoneNumForbidden(CheckPhoneNumberParam.builder()
                .appId(appId)
                .checkPhoneNum(userInfo.getPhoneNum()).userId(ContextUtils.getContext().getUserId()).build());
        if (!checkOverseaPhoneNumResultVO.isOK()) {
            return Pair.of(checkOverseaPhoneNumResultVO.getRCode(), checkOverseaPhoneNumResultVO.getPrompt().getMsg());
        }

        /**
         * 检测无绑定手机号时是否限制
         */
        ResultVO<Void> checkSocialResultVO = phoneNumCheckManager.checkSocialLimitWithoutPhone(CheckSocialLimitParam.builder().appId(appId)
                .phoneNum(userInfo.getPhoneNum())
                .userId(ContextUtils.getContext().getUserId()).build());
        if (!checkSocialResultVO.isOK()) {
            return Pair.of(checkSocialResultVO.getRCode(), checkSocialResultVO.getPrompt().getMsg());
        }


        ISendTextCommentProcessor processor = processorFactory.getProcessor(
                appId, ISendTextCommentProcessor.class);

        //获取风控的检测参数
        String riskParams = getRiskParams(processor, param, userInfo.getNickName(), Optional.ofNullable(liveInfo).map(LiveBean::getUserId).orElse(0L));

        ResultVO<Void> guardActivityResultVO = rushCheckManager.guardActivityAntiRush(AntiRushParam.builder()
                .appId(appId)
                .deviceId(ContextUtils.getContext().getHeader().getDeviceId())
                .eventId(AntiCheatConstant.LZ_LIVE_MSG)
                .loss(0)
                .ip(ContextUtils.getContext().getHeader().getIp())
                .riskParamsJson(riskParams)
                .strategy(AntiCheatStrategyEnum.STRICT)
                .userId(ContextUtils.getContext().getUserId())
                .build());
        if (!guardActivityResultVO.isOK()) {
            return Pair.of(guardActivityResultVO.getRCode(), guardActivityResultVO.getPrompt().getMsg());
        }


        // 2 业务前置检验
        ResultVO<Void> preprocessor = processor.preprocessor(SendTextCommentPreBean.builder()
                .userInfo(userInfo)
                .liveId(liveId)
                .liveInfo(liveInfo)
                .commentType(commentType)
                .content(content)
                .commentId(commentId)
                .build());
        if (!preprocessor.isOK()) {
            return Pair.of(preprocessor.getRCode(), preprocessor.getPrompt().getMsg());
        }

        //获取评论列表
        CommentCommonConfig commentConfig = processor.getCommentConfig();

        // 3. 核心接口调用
        CommentBean commentBean = processor.genCommentBean(GenCommentParam.builder()
                .content(content)
                .commentId(commentId)
                .userId(userInfo.getUserId())
                .liveId(liveId)
                .emotionId(param.getEmotionId())
                .targetUserId(targetUserId)
                .commentType(commentType)
                .atUsers(param.getAtUsers())
                .build());
        //3.1 根据不同的评论类型处理器，转化评论信息
        CommentTypeHandler commentTypeHandler = commentTypeHandlerFactory.getHandler(commentType);
        ResultVO<CommentTypeDTO> transformVo = commentTypeHandler.fillCommentInfo(commentBean, commentConfig);
        if (!transformVo.isOK()) {
            return Pair.of(transformVo.getRCode(), transformVo.getPrompt().getMsg());
        }

        Result<Void> result = liveCommentServiceRemote.addCommentExtend(commentBean);
        CommentTypeDTO commentTypeDTO = transformVo.getData();
        commentTypeDTO.setSendCommentCode(result.rCode());
        //3.2 发送评论成功后，不同评论类型的处理
        ResultVO<Void> afterSuccessProcessor = commentTypeHandler.afterSuccessProcessor(commentTypeDTO);
        if (!afterSuccessProcessor.isOK()) {
            return Pair.of(afterSuccessProcessor.getRCode(), afterSuccessProcessor.getPrompt().getMsg());
        }

        // 4. 业务后置处理
        SendTextCommentPostBean sendTextCommentPostBean = new SendTextCommentPostBean();
        sendTextCommentPostBean.setRCode(result.rCode());
        sendTextCommentPostBean.setCommentBean(commentBean);

        ResultVO<Long> postprocessor = processor.postprocessor(sendTextCommentPostBean);
        if (!postprocessor.isOK()) {
            return Pair.of(postprocessor.getRCode(), postprocessor.getPrompt().getMsg());
        }

        // 5. 平台后置处理
        // 5.1 删除评论id
        commentRedis.deleteCommentIdByUserId(userInfo.getUserId(), commentId);

        // 6. 记录评论次数
        if (preprocessor.getRCode() == 0) {
            processor.sendSuccessPostProcess();
        }

        return Pair.of(postprocessor.getRCode(), String.valueOf(postprocessor.getData()));
    }

    private LiveBean getLiveInfo(long liveId) {
        Result<GetLiveRemoteResult> liveResult = liveServiceRemote.getLiveByCache(GetLiveRemoteParam.builder()
                .liveId(liveId)
                .build());
        if (liveResult.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
            log.warn("getAnchorId fail, liveId:{},rCode:{}", liveId, liveResult.rCode());
            return null;
        }
        return Optional.ofNullable(liveResult.target()).map(GetLiveRemoteResult::getLiveBean).orElse(null);
    }


    /**
     * 发送评论
     *
     * @return 评论结果
     */
    public Pair<Integer, String> sendPatComment(int appId, SendPatCommentParam param) {
        Long commentId = param.getCommentId();
        Long liveId = param.getLiveId();
        Long targetUserId = param.getTargetUserId();

        /**
         * 用户基础信息
         */
        SimpleUser userInfo = getUser(ContextUtils.getContext().getUserId());

        SimpleUser targetUser = getUser(targetUserId);
        if (userInfo == null || targetUser == null) {
            return Pair.of(CommentMsgCodes.SEND_COMMENT_ERROR.getCode(), CommentMsgCodes.SEND_COMMENT_ERROR.getMsg());
        }

        // 检查评论id是否存在
        if (!commentRedis.checkCommentIdByUserId(userInfo.getUserId(), commentId)) {
            log.warn("commentId not exist, commentId:{}", commentId);
            return Pair.of(CommentMsgCodes.COMMENT_ID_NOT_EXIST.getCode(), CommentMsgCodes.SEND_COMMENT_ERROR.getMsg());
        }

        // 平台自身前置检验
        LiveBean live = getLive(liveId);
        if (live == null) {
            log.warn("sendPatComment live not exist, liveId:{}", liveId);
            return Pair.of(CommentMsgCodes.LIVE_NOT_EXIST.getCode(), CommentMsgCodes.LIVE_NOT_EXIST.getMsg());
        }
        ISendPatCommentProcessor processor = processorFactory.getProcessor(
                appId, ISendPatCommentProcessor.class);

        SendPatCommentBean sendPatCommentBean = SendPatCommentBean.builder()
                .userInfo(userInfo)
                .targetUserInfo(targetUser)
                .liveId(liveId)
                .njId(live.getUserId())
                .commentId(commentId)
                .build();

        // 业务前置检验
        ResultVO<Void> preprocessor = processor.preprocessor(sendPatCommentBean);
        if (!preprocessor.isOK()) {
            return Pair.of(preprocessor.getRCode(), preprocessor.getPrompt().getMsg());
        }

        ResultVO<Long> postprocessor = processor.postprocessor(sendPatCommentBean);
        if (!postprocessor.isOK()) {
            return Pair.of(postprocessor.getRCode(), postprocessor.getPrompt().getMsg());
        }
        // 删除评论id
        commentRedis.deleteCommentIdByUserId(userInfo.getUserId(), commentId);
        return Pair.of(postprocessor.getRCode(), String.valueOf(postprocessor.getData()));
    }


    private String getRiskParams(ISendTextCommentProcessor processor, SendWordCommentParam param, String nickName, long anchorId) {
        Map<String, Object> map = new HashMap<>(8);
        map.put("name", nickName);
        map.put("liveId", param.getLiveId());
        map.put("anchorId", anchorId);
        map.put("msgType", processor.getMsgType(SendTextCommentGetMsgTypeParam.builder()
                .commentType(param.getCommentType())
                .content(param.getContent()).build()));
        map.put("msg", param.getContent());
        map.put("commenType", param.getCommentType());
        map.put("realAppId", ContextUtils.getContext().getHeader().getAppId());
        return JSONObject.toJSONString(map);
    }


    /**
     * 发送图片评论
     *
     * @param param 发送图片评论参数
     * @return 结果
     */
    public Pair<Integer, String> sendImageComment(int appId, SendPicCommentParam param) {
        // 1 平台自身前置检验
        // 1.1 检查评论id是否存在
        // 用户id
        long userId = ContextUtils.getContext().getUserId();

        if (!commentRedis.checkCommentIdByUserId(userId, param.getCommentId())) {
            log.warn("commentId not exist, commentId:{}", param.getCommentId());
            return Pair.of(CommentMsgCodes.COMMENT_ID_NOT_EXIST.getCode(), CommentMsgCodes.SEND_COMMENT_ERROR.getMsg());
        }
        ISendImageCommentProcessor processor = processorFactory.getProcessor(appId, ISendImageCommentProcessor.class);

        ResultVO<Void> preprocessorResult = processor.preprocessor(SendImageCommentPreBean.builder()
                .liveId(param.getLiveId())
                .userId(userId).build());
        if (!preprocessorResult.isOK()) {
            return Pair.of(preprocessorResult.getRCode(), preprocessorResult.getPrompt().getMsg());
        }

        // 2. 核心业务逻辑
        AddCommentImageParam addCommentImageParam = new AddCommentImageParam();
        addCommentImageParam.setCommentId(param.getCommentId());
        addCommentImageParam.setUserId(userId);
        addCommentImageParam.setLiveId(param.getLiveId());
        addCommentImageParam.setImageHeight(param.getImageHeight());
        addCommentImageParam.setImageWidth(param.getImageWidth());
        addCommentImageParam.setImageSize(param.getImageSize());
        addCommentImageParam.setImageUrl(UrlUtils.getUriFromUrl(param.getImageUrl()));
        addCommentImageParam.setImageOriginal(param.isImageOriginal());
        addCommentImageParam.setCommentType(GENERAL_COMMENT);

        Result<Void> resp = liveCommentServiceRemote.addCommentImage(addCommentImageParam);

        // 3. 平台后置处理
        ResultVO<Long> postprocessor = processor.postprocessor(SendImageCommentPostBean.builder()
                .commentId(param.getCommentId())
                .rCode(resp.rCode())
                .build());
        if (!postprocessor.isOK()) {
            return Pair.of(postprocessor.getRCode(), postprocessor.getPrompt().getMsg());
        }

        // 3.1 删除评论id
        commentRedis.deleteCommentIdByUserId(userId, param.getCommentId());

        // 0表示成功
        if (resp.rCode() == 0) {
            processor.sendSuccessPostProcess();
        }

        return Pair.of(resp.rCode(), String.valueOf(addCommentImageParam.getCommentId()));
    }

    /**
     * 获取最近评论列表
     *
     * @param liveId        直播id
     * @param performanceId performanceId
     * @return 最近消息结果
     */
    public ResultVO<LatestCommentsResult> latestComments(Long liveId, String performanceId) {
        return getComments(liveId, performanceId, false);
    }


    /**
     * 获取最近过滤评论列表
     *
     * @param liveId        直播id
     * @param performanceId performanceId
     * @return
     */
    public ResultVO<LatestCommentsResult> latestFilterComments(Long liveId, String performanceId) {
        return getComments(liveId, performanceId, true);
    }

    /**
     * 获取评论列表
     *
     * @param liveId        直播id
     * @param performanceId performanceId
     * @return 评论列表
     */
    public ResultVO<LatestCommentsResult> getComments(Long liveId, String performanceId, boolean isFilter) {
        StopWatch stopWatch = new StopWatch();
        long userId = ContextUtils.getContext().getUserId();

        IGetLiveMsgProcessor processor = processorV2Factory.getProcessor(IGetLiveMsgProcessor.class);

        // 2 平台自身前置检验
        LiveBean live = getLive(liveId);
        if (live == null) {
            log.warn("getComments live not exist, liveId:{}", liveId);
            return ResultVO.failure(CommentMsgCodes.GET_LATEST_COMMENT_LIVE_NOT_EXIST.getCode(),
                    CommentMsgCodes.GET_LATEST_COMMENT_LIVE_NOT_EXIST.getMsg());
        }

        // 3 查询出评论列表
        stopWatch.start();
        ResultVO<GetCommentWithServerTimeResult> commentListRes = getCommentList(liveId, performanceId, processor.getCommentConfig());
        if (!commentListRes.isOK()) {
            log.warn("getComments commentListRes fail, liveId:{}, performanceId:{}", liveId, performanceId);
            return ResultVO.failure(CommentMsgCodes.GET_LATEST_COMMENT_FAIL.getCode(),
                    CommentMsgCodes.GET_LATEST_COMMENT_FAIL.getMsg());
        }

        // 4 查询出进房公告列表
        ResultVO<List<EnterNoticeEntry>> enterNoticeListRes = getEnterNoticeList(live, performanceId, commentListRes.getData().getCommentServerTime(), isFilter);
        if (!enterNoticeListRes.isOK()) {
            log.warn("getComments enterNoticeListRes fail, liveId:{}, performanceId:{}, commentServerTime:{}",
                    liveId, performanceId, commentListRes.getData().getCommentServerTime());
            return ResultVO.failure(CommentMsgCodes.GET_LATEST_COMMENT_FAIL.getCode(),
                    CommentMsgCodes.GET_LATEST_COMMENT_FAIL.getMsg());
        }
        stopWatch.stop();
        if (stopWatch.getTime() > commentConfig.getPrintCommentCostLogLimit()) {
            log.info("getComments query data cost time:{}", stopWatch.getTime());
        }
        stopWatch.reset();


        //5 将进房评论消息归类到进房公告消息
        stopWatch.start();
        Pair<List<TransientComment>, List<EnterNoticeEntry>> classifyEnterMsg = classifyEnterMsg(commentListRes.getData().getTransientComments(),
                enterNoticeListRes.getData(), processor, userId, isFilter);
        stopWatch.stop();
        if (stopWatch.getTime() > commentConfig.getPrintCommentCostLogLimit()) {
            log.info("getComments classifyEnterMsg cost time:{}", stopWatch.getTime());
        }
        stopWatch.reset();

        //5.1 过滤开启了隐身的进房消息
        stopWatch.start();
        List<EnterNoticeEntry> enterNoticeEntries = processor.filterHideEnterNotice(classifyEnterMsg.getRight());

        //6 过滤并对评论排序
        List<TransientComment> filterComments = filterAndSortComments(classifyEnterMsg.getLeft(), ContextUtils.getContext().getUserId(), isFilter);
        stopWatch.stop();
        if (stopWatch.getTime() > commentConfig.getPrintCommentCostLogLimit()) {
            log.info("getComments filter operator cost time:{}", stopWatch.getTime());
        }
        stopWatch.reset();

        //7 转换评论列表
        stopWatch.start();
        List<LiveCommentVO> liveCommentVOS = transformCommentsToLiveComment(live, filterComments, processor);
        stopWatch.stop();
        if (stopWatch.getTime() > commentConfig.getPrintCommentCostLogLimit()) {
            log.info("getComments transformCommentsToLiveComment cost time:{}, commentSize={}", stopWatch.getTime(), filterComments.size());
        }
        stopWatch.reset();

        //8 转换进场消息
        //计算进房消息的时间
        stopWatch.start();
        List<EnterNoticeVO> enterNoticeVOS = transformNoticeToVO(live, enterNoticeEntries, processor, userId);
        stopWatch.stop();
        if (stopWatch.getTime() > commentConfig.getPrintCommentCostLogLimit()) {
            log.info("getComments transformNoticeToVO cost time:{}, commentSize={}", stopWatch.getTime(), enterNoticeEntries.size());
        }

        LatestCommentsResult latestCommentsResult = new LatestCommentsResult();
        latestCommentsResult.setPerformanceId(String.valueOf(commentListRes.getData().getCommentServerTime()));
        latestCommentsResult.setLiveComment(liveCommentVOS);
        latestCommentsResult.setEnterNotice(enterNoticeVOS);
        List<Long> printCommentLogUserIds = processor.getCommentConfig().getPrintCommentLogUserIds();
        if (processor.getCommentConfig().isCommentMsgLogSwitch() || printCommentLogUserIds.contains(userId)) {
            log.info("getComments success, liveId:{}, userId={}, performanceId:{}, commentServerTime:{}, liveComment:{}, enterNotice:{}",
                    liveId, userId, performanceId, commentListRes.getData().getCommentServerTime(),
                    JSONObject.toJSONString(liveCommentVOS), JSONObject.toJSONString(enterNoticeVOS));
        }

        return ResultVO.success(latestCommentsResult);
    }

    /**
     * 转换评论信息列表
     *
     * @param live              直播信息
     * @param transientComments 评论信息列表
     * @return 转换后的评论信息列表
     */
    private List<LiveCommentVO> transformCommentsToLiveComment(LiveBean live,
                                                               List<TransientComment> transientComments,
                                                               IGetLiveMsgProcessor processor) {
        CommentCommonConfig commentConfig = processor.getCommentConfig();
        List<ICommentMedalNode> commentMedalNodes = processor.getBuildCommentMedalNodes();
        BusinessConfig businessConfig = commonConfig.getBusinessConfig(ContextUtils.getContext().getHeader().getAppId());
        List<LiveCommentVO> liveCommentVOS = new ArrayList<>();

        List<Long> userIds = transientComments.stream().map(TransientComment::getUserId).collect(Collectors.toList());
        Map<Long, UserAvatarWidget> userAvatarWidgetMap = userService.batchGetUserAvatarWidget(userIds).target();

        //批量查询用户
        Map<Long, SimpleUser> simpleUserMap = batchGetUser(userIds);
        for (TransientComment transientComment : transientComments) {
            SimpleUser user = simpleUserMap.getOrDefault(transientComment.getUserId(), null);
            if (user == null) {
                log.warn("transformCommentsToLiveComment user not exist,liveId={}, userId:{}, content={}",
                        live.getId(), transientComment.getUserId(), transientComment.getContent());
                continue;
            }

            LiveCommentVO liveCommentVO = new LiveCommentVO();

            // 如果是系统公告评论，并且存在操作人ID，则需要重置用户属性信息（此时的用户属性信息都是主播的）
            ResultVO<TransientComment> resetRes = processor.resetCommentUserProperty(transientComment);
            if (!resetRes.isOK()) {
                log.warn("transformCommentsToLiveComment resetCommentUserProperty fail,liveId={}, userId:{}, content={}",
                        live.getId(), transientComment.getUserId(), transientComment.getContent());
                continue;
            }

            GenMedalInfoContext medalInfoContext = GenMedalInfoContext.builder().comment(transientComment).medalShowArea(1)
                    .liveBean(live).appId(ContextUtils.getContext().getHeader().getAppId()).commentConfig(commentConfig).build();

            // 处理用户勋章
            List<BadgeImageVO> imgIcons = handelBadgeImageVOS(medalInfoContext, commentMedalNodes);

            if (isShowUserInfo(transientComment.getCommentType())) {
                UserAvatarWidget avatarWidget = userAvatarWidgetMap.get(transientComment.getUserId());
                // 处理用户信息
                UserVO userVO = new UserVO();
                userVO.setUserAvatarWidget(avatarWidget);
                userVO.setUserId(String.valueOf(user.getUserId()));
                userVO.setUserName(user.getNickName());
                userVO.setUserAvatar(user.getAvatar());
                userVO.setUserIcons(imgIcons);

                // 适配差异化用户信息
                processor.adaptUserVo(transientComment, userVO);
                liveCommentVO.setUser(userVO);
            }

            // 小陪伴独有  多元素评论
            liveCommentVO.setContentItems(transientComment.getContentItems());

            // 处理图片评论
            if (StringUtils.isNotBlank(transientComment.getImageUrl())) {
                ImageVO imageVO = new ImageVO();
                String imgUrl = UrlUtils.addCdnHost(businessConfig.getCdnHost(), transientComment.getImageUrl());
                imageVO.setImageUrl(imgUrl);
                imageVO.setImageHeight(transientComment.getImageHeight());
                imageVO.setImageWidth(transientComment.getImageWidth());
                imageVO.setImageSize(transientComment.getImageSize());
                imageVO.setAspectRatio(transientComment.getAspectRatio());
                liveCommentVO.setImage(imageVO);
            }

            // 处理表情评论
            if (transientComment.getEmotionId() > 0) {
                EmotionVO emotionVO = new EmotionVO();
                emotionVO.setEmotionId(String.valueOf(transientComment.getEmotionId()));
                emotionVO.setRepeatStopImageIndex(transientComment.getRepeatStopImageIndex());
                liveCommentVO.setEmotion(emotionVO);
            }

            liveCommentVO.setContent(transientComment.getContent());
            liveCommentVO.setTailEffect(transientComment.getTailEffect());
            liveCommentVO.setBubbleEffectId(transientComment.getStyleId());
            liveCommentVO.setCommentId(String.valueOf(transientComment.getCommentId()));
            liveCommentVO.setCommentType(transientComment.getCommentType());
            liveCommentVO.setTimeStamp(transientComment.getTime());
            liveCommentVO.setAndroidColor(transientComment.getAndroidColor());
            liveCommentVO.setIosColor(transientComment.getIosColor());
            liveCommentVO.setXmCommonBizExtra(XmCommentComBizExtra.builder()
                    .anonymous(transientComment.isAnonymous()).build());
            liveCommentVO.setPpCommonBizExtra(
                    PpCommentComBizExtra.builder().masked(transientComment.getMaskStatus() == 1).build());
            if (CollectionUtils.isNotEmpty(transientComment.getAtUsers())) {
                liveCommentVO.setAtUsers(transientComment.getAtUsers().stream()
                        .map(v->new AtUserVO().setUserId(v.getUserId()).setName(v.getName()))
                        .collect(Collectors.toList())
                );
            }

            //补全点唱歌曲信息
            processor.fillSongInfoVO(liveCommentVO, transientComment.getSongInfo(), transientComment.getCommentType());
            processor.fillRoomPlaylistVO(liveCommentVO, transientComment.getRoomPlaylistCommentCardBean(), transientComment.getCommentType());
            processor.fillRoomRouletteWheelVO(liveCommentVO, transientComment.getRouletteWheelCommentCardBean(), transientComment.getCommentType());
            processor.fillFreshUserInterestInfoVO(liveCommentVO, transientComment.getFreshUserInterestBean(), transientComment.getCommentType());
            processor.fillFeedContentInfoVO(liveCommentVO, transientComment.getFeedContentInfoBean(), transientComment.getCommentType());

            liveCommentVOS.add(liveCommentVO);
        }

        return liveCommentVOS;
    }

    /**
     * 过滤并对评论排序
     *
     * @param transientComments 评论列表
     * @param userId            用户id
     * @param isFilter          是否过滤
     * @return 过滤后的评论列表
     */
    @Deprecated
    private List<TransientComment> filterAndSortComments(List<TransientComment> transientComments, long userId, boolean isFilter) {
        // 过滤评论
        CommonMsgCompositeFilter commentCompositeFilter = new CommonMsgCompositeFilter();
        CommentFilter commentToUserFilter = new MsgToUserFilter(userId);

        commentCompositeFilter.addFilter(commentToUserFilter);

        // 增加过滤器
        if (isFilter) {
            CommentFilter msgGiftTypeFilter = new MsgGiftTypeFilter();
            commentCompositeFilter.addFilter(msgGiftTypeFilter);
        }

        List<TransientComment> filterComment = commentCompositeFilter.filter(transientComments);

        // 按时间排序
        filterComment.sort(Comparator.comparing(TransientComment::getTime));
        return filterComment;
    }

    /**
     * 获取评论列表
     *
     * @param liveId        直播id
     * @param performanceId performanceId
     * @return 结果
     */
    @Deprecated
    private ResultVO<GetCommentWithServerTimeResult> getCommentList(Long liveId, String performanceId, CommentCommonConfig config) {
        //构建查询参数
        GetCommentWithServerTimeParam param = buildQueryParam(liveId, performanceId, config);
        //先从缓存中获取评论
        ResultVO<GetCommentWithServerTimeResult> commentListCacheRes = getCommentListFromCache(param);
        if (commentListCacheRes.isOK()) {
            return commentListCacheRes;
        }

        //监控打点
        commentMonitorManager.commentQueryState(1, false);

        //查询评论列表
        Result<GetCommentWithServerTimeResult> result = liveCommentServiceRemote.getCommentWithServerTime(param);
        if (result.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
            log.warn("getCommentWithServerTime error, param:{}", param);
            return ResultVO.failure("获取评论列表失败");
        }

        //差别比较大的，需要调整条数
        if ((param.getEndTime() - param.getStartTime()) >= config.getUpdateCommentCountTimeInterval()) {
            log.info("getCommentWithServerTime, liveId={},userId={},performanceId={} startTime={},endTime={},res={}",
                    liveId, ContextUtils.getContext().getUserId(), performanceId,
                    param.getStartTime(), param.getEndTime(), JSONObject.toJSONString(result.target()));
        }

        //后置操作
        getCommentAfterProcessor(param, result.target().getTransientComments());
        return ResultVO.success(result.target());
    }

    /**
     * 获取进房公告列表
     *
     * @param liveBean      直播信息
     * @param performanceId performanceId
     * @param endTime       结束时间
     * @param isFilter      是否可以过滤
     * @return 结果
     */
    private ResultVO<List<EnterNoticeEntry>> getEnterNoticeList(LiveBean liveBean, String performanceId, long endTime, boolean isFilter) {
        LasterEnterNoticeParam enterNoticeParam = new LasterEnterNoticeParam();
        enterNoticeParam.setPerformanceId(performanceId);
        enterNoticeParam.setLive(liveBean);
        enterNoticeParam.setUserId(ContextUtils.getContext().getUserId());
        enterNoticeParam.setIsFilter(isFilter);
        enterNoticeParam.setEndTime(endTime);
        ResultVO<List<EnterNoticeEntry>> enterNoticeResult = enterNoticeManager.latestEnterNotice(enterNoticeParam);
        if (!enterNoticeResult.isOK()) {
            log.error("getEnterNoticeList error, param:{}", enterNoticeParam);
            return ResultVO.failure("获取进场消息失败");
        }

        List<EnterNoticeEntry> data = enterNoticeResult.getData();
        Integer appId = ContextUtils.getContext().getHeader().getAppId();
        //对进房公告进行过滤
        List<EnterNoticeEntry> enterNoticeEntries = filterEnterNotice(isFilter, enterNoticeParam.getUserId(), appId, data);
        return ResultVO.success(enterNoticeEntries);
    }

    /**
     * 同步评论
     *
     * @param liveId     直播id
     * @param commentIds 评论id
     * @return 同步结果
     */
    public ResultVO<List<SyncCommentResult>> syncComments(int appId, Long liveId, List<Long> commentIds) {
        // 获取直播评论图片审核信息
        GetTransientCommentReviewParam param = new GetTransientCommentReviewParam();
        param.setLiveId(liveId);
        param.setCommentIds(commentIds);

        ISyncCommentProcessor processor = processorFactory.getProcessor(appId, ISyncCommentProcessor.class);

        SyncCommentPreBean syncCommentPreBean = new SyncCommentPreBean().setLiveId(liveId).setUserId(ContextUtils.getContext().getUserId());
        ResultVO<Void> preprocessorResult = processor.preprocessor(syncCommentPreBean);
        // 这里前置校验 只是判断是否是进入熔断或者关闭同步接口 需要返回正常的码
        if (!preprocessorResult.isOK()) {
            return ResultVO.success(Collections.emptyList());
        }

        Result<List<GetTransientCommentReviewResult>> result =
                liveCommentServiceRemote.getTransientCommentReview(param);
        if (result.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
            log.error("getTransientCommentReview error, param:{}", param);
            return ResultVO.failure("同步评论异常");
        }

        List<GetTransientCommentReviewResult> commentReviewResults = result.target();
        if (CollectionUtils.isEmpty(commentReviewResults)) {
            return ResultVO.failure("同步评论异常");
        }

        List<SyncCommentResult> syncCommentResults = new ArrayList<>();
        for (GetTransientCommentReviewResult commentReviewResult : commentReviewResults) {
            SyncCommentResult syncCommentResult = new SyncCommentResult();
            syncCommentResult.setCommentId(commentReviewResult.getCommentId());
            syncCommentResult.setState(commentReviewResult.getReviewStatus() == 0 ? 0 : 1);
            syncCommentResults.add(syncCommentResult);

        }

        // 输出过滤结果
        return ResultVO.success(syncCommentResults);
    }

    /**
     * 获取直播信息
     *
     * @param liveId 直播id
     * @return 直播信息
     */
    private LiveBean getLive(Long liveId) {
        GetLiveRemoteParam getLiveRemoteParam = new GetLiveRemoteParam();
        getLiveRemoteParam.setLiveId(liveId);
        //getLiveWithCache底层也是加了本地缓存，如果能上游控制，就不在下游控制，同时减少网络IO
        Result<GetLiveRemoteResult> liveResult = liveServiceRemote.getLiveByCache(getLiveRemoteParam);
        if (liveResult.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
            log.error("getLive error, param:{}", getLiveRemoteParam);
            return null;
        }

        return liveResult.target().getLiveBean();
    }


    /**
     * 获取用户信息
     *
     * @param userId 用户id
     * @return 用户信息
     */
    private SimpleUser getUser(long userId) {
        Result<GetSimpleUserResult> simpleUserResult = userService.getSimpleUserByCache(userId);
        if (simpleUserResult.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
            log.warn("getSimpleUser error, userId:{}", userId);
            return null;
        }

        // 获取用户信息
        return simpleUserResult.target().getSimpleUser();
    }

    /**
     * 获取用户信息
     *
     * @param userIdList 用户ID列表
     * @return 用户信息
     */
    private Map<Long, SimpleUser> batchGetUser(List<Long> userIdList) {
        if (CollectionUtils.isEmpty(userIdList)) {
            return new HashMap<>();
        }
        BatchGetUserParam userParam = BatchGetUserParam.builder().userIdList(userIdList).build();
        Result<BatchGetSimpleUserResult> result = userService.batchGetSimpleUserByCache(userParam);
        if (result.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
            log.warn("batchGetUser error, userIdList:{}", userIdList);
            return new HashMap<>();
        }
        List<SimpleUser> simpleUsers = result.target().getUserList();
        if (CollectionUtils.isEmpty(simpleUsers)) {
            return new HashMap<>();
        }
        //simpleUsers转成map
        // 获取用户信息
        return simpleUsers.stream().collect(Collectors.toMap(SimpleUser::getUserId, Function.identity()));
    }


    /**
     * 进房消息归类
     *
     * @param comments           评论消息列表
     * @param enterNoticeEntries 进房公告消息列表
     * @param userId             主播ID
     * @param isFilter           是否过滤，true: 过滤
     * @return left: 评论列表，right: 进房公告列表
     */
    public Pair<List<TransientComment>, List<EnterNoticeEntry>> classifyEnterMsg(List<TransientComment> comments,
                                                                                 List<EnterNoticeEntry> enterNoticeEntries,
                                                                                 IGetLiveMsgProcessor processor,
                                                                                 long userId, boolean isFilter) {
        //获取comments中的userId列表
        List<Long> userIds = comments.stream().filter(processor::isEnterComment).map(TransientComment::getUserId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(userIds)) {
            // 没有需要处理的进房消息，直接返回
            return Pair.of(comments, enterNoticeEntries);
        }

        Integer appId = ContextUtils.getContext().getHeader().getAppId();
        Iterator<TransientComment> iterator = comments.iterator();
        boolean isExistEnterRoomComment = false;

        //获取财富等级信息
        Result<Map<Long, WealthLevelResult>> wealthLevelResultMapRes = userWealthLevelService.batchGetWealthLevelFromCache(userIds);
        Map<Long, WealthLevelResult> wealthLevelResultMap = wealthLevelResultMapRes.target();

        while (iterator.hasNext()) {
            TransientComment comment = iterator.next();
            if (processor.isEnterComment(comment)) {

                List<Long> toUser = comment.getToUser();
                if (CollectionUtils.isNotEmpty(toUser) && !toUser.contains(userId)) {
                    continue;
                }

                EnterNoticeEntry noticeEntry = new EnterNoticeEntry();
                noticeEntry.setUserId(comment.getUserId());
                noticeEntry.setShowFreshUserWelcome(true);
                if (null != comment.getComeSource()) {
                    noticeEntry.setComeSource(comment.getComeSource().getComeSource());
                    noticeEntry.setComeSourceTgtUid(comment.getComeSource().getComeSourceTgtUid());
                }
                noticeEntry.setTimeStamp(comment.getTime());
                noticeEntry.setNotice(comment.getContent());
                //进场评论迁移到进房公告时，是不存在财富等级信息和新人信息，需要补充
                if (noticeEntry.getFreshUserEntry() == null) {
                    //补充新人信息
                    Result<FreshUserResult> freshUser = userService.isFreshUserByCache(noticeEntry.getUserId());
                    if (freshUser.rCode() == 0 && freshUser.target() != null) {
                        FreshUserResult userResult = freshUser.target();
                        FreshUser user = FreshUser.builder().is(userResult.isIs()).url(userResult.getUrl()).aspect(userResult.getAspect()).build();
                        noticeEntry.setFreshUserEntry(user);
                    }
                }
                //补充财富等级
                WealthLevelResult wealthLevelResult = wealthLevelResultMap.get(noticeEntry.getUserId());
                noticeEntry.setWealthInfo(CommentMapper.I.toWealthLevelDTO(wealthLevelResult));
                //补充其他的勋章
                ResultVO<EnterNoticeEntry> fillRes = processor.fillEnterNoticeMedal(noticeEntry);
                if (fillRes.isOK()) {
                    noticeEntry = fillRes.getData();
                }

                enterNoticeEntries.add(noticeEntry);
                iterator.remove();
                isExistEnterRoomComment = true;
            }
        }

        //如果存在进房评论迁移到了进房公告消息列表，且需要过滤，则根据用户设置做进房公告消息过滤
        if (isFilter && isExistEnterRoomComment) {
            enterNoticeEntries = commentFilterManager.filterEnterNoticeByUserSetting(userId, appId, enterNoticeEntries);
        }
        return Pair.of(comments, enterNoticeEntries);
    }

    /**
     * 处理用户图标
     *
     * @param context           上下文
     * @param commentMedalNodes 评论勋章节点
     * @return 用户图标列表
     */
    private List<BadgeImageVO> handelBadgeImageVOS(GenMedalInfoContext context, List<ICommentMedalNode> commentMedalNodes) {
        List<BadgeImageVO> imgIcons = new ArrayList<>();
        if (CollectionUtils.isEmpty(commentMedalNodes)) {
            return imgIcons;
        }

        //按顺序构建勋章图标
        for (ICommentMedalNode medalNode : commentMedalNodes) {
            try {
                Optional<List<BadgeImageVO>> listOptional = medalNode.buildMedalImageInfo(context);
                listOptional.ifPresent(imgIcons::addAll);
            } catch (Exception e) {
                log.error("buildMedalImageInfo error, userId={}, comment={}", context.getComment().getUserId(), context.getComment(), e);
            }
        }
        return imgIcons;
    }

    /**
     * 是否展示用户信息
     *
     * @param commentType 评论类型
     * @param commentType 评论类型扩展
     * @return true: 展示，false: 不展示
     */
    private boolean isShowUserInfo(int commentType) {
        //活动评论不展示头像
        if (commentType == WaveCommentType.ACTIVITY_COMMENT) {
            return false;
        }
        //进房评论不展示头像
        return commentType != WaveCommentType.LIVE_INTRO;
    }

    /**
     * 转换成VO
     *
     * @param live               直播节目ID
     * @param enterNoticeEntries 进场通知列表
     * @param processor          差异化处理器
     * @return 进场通知VO列表
     */
    private List<EnterNoticeVO> transformNoticeToVO(LiveBean live,
                                                    List<EnterNoticeEntry> enterNoticeEntries,
                                                    IGetLiveMsgProcessor processor, long userId) {
        List<ICommentMedalNode> enterMedalNodes = processor.getBuildEnterNoticeMedalNodes();
        CommentCommonConfig commentConfig = processor.getCommentConfig();
        Map<Long, UserAvatarWidget> userAvatarWidgetMap = userService.batchGetUserAvatarWidget(enterNoticeEntries.stream().map(EnterNoticeEntry::getUserId).collect(Collectors.toList())).target();

        return enterNoticeEntries.stream()
                .map(enterNoticeEntry -> {
                    //构建勋章图标上下文
                    GenMedalInfoContext context = GenMedalInfoContext.builder()
                            .appId(ContextUtils.getContext().getHeader().getAppId())
                            .commentConfig(commentConfig)
                            .enterNoticeEntry(enterNoticeEntry)
                            .liveBean(live)
                            .medalShowArea(2)
                            .build();

                    //生成进房公告勋章图标
                    List<BadgeImageVO> imgIcons = generateMedalIcons(context, enterMedalNodes);
                    UserVO userVO = getUserVO(enterNoticeEntry, userAvatarWidgetMap);
                    if (userVO == null) {
                        log.warn("transformNoticeToVO user not exist,liveId={}, userId:{}, content={}",
                                live.getId(), enterNoticeEntry.getUserId(), enterNoticeEntry.getNotice());
                        return null;
                    }
                    userVO.setUserIcons(imgIcons);
                    EnterNoticeVO enterNoticeVO = new EnterNoticeVO();
                    enterNoticeVO.setUser(userVO);
                    enterNoticeVO.setUserId(enterNoticeEntry.getUserId().toString());
                    enterNoticeVO.setContent(enterNoticeEntry.getNotice());
                    enterNoticeVO.setCount(enterNoticeEntry.getCount());
                    if (processor.isShowButton(live.getId(), userId, enterNoticeEntry)) {
                        enterNoticeVO.setIsShowButton(enterNoticeEntry.getIsShowButton());
                    }
                    enterNoticeVO.setTimeStamp(enterNoticeEntry.getTimeStamp());
                    enterNoticeVO.setUserMountEntry(enterNoticeEntry.getUserMountEntry());

                    enterNoticeVO.setExtra(processor.getEnterNoticeExtra(userId, enterNoticeEntry));

                    return enterNoticeVO;
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }


    /**
     * 生成勋章图标
     *
     * @param context         上下文
     * @param enterMedalNodes 进房勋章构建节点列表
     * @return 勋章图标列表
     */
    private List<BadgeImageVO> generateMedalIcons(GenMedalInfoContext context, List<ICommentMedalNode> enterMedalNodes) {
        List<BadgeImageVO> imgIcons = new ArrayList<>();
        for (ICommentMedalNode medalNode : enterMedalNodes) {
            Optional<List<BadgeImageVO>> listOptional = medalNode.buildMedalImageInfo(context);
            listOptional.ifPresent(imgIcons::addAll);
        }
        return imgIcons;
    }

    /**
     * 获取用户信息
     *
     * @param enterNoticeEntry 进场通知
     * @return 用户信息
     */
    private UserVO getUserVO(EnterNoticeEntry enterNoticeEntry, Map<Long, UserAvatarWidget> userAvatarWidgetMap) {
        SimpleUser user = getUser(enterNoticeEntry.getUserId());
        if (user == null) {
            return null;
        }

        UserVO userVO = new UserVO();
        UserAvatarWidget avatarWidget = userAvatarWidgetMap.get(enterNoticeEntry.getUserId());
        // 处理用户信息
        userVO.setUserAvatarWidget(avatarWidget);
        userVO.setUserId(String.valueOf(user.getUserId()));
        userVO.setUserName(user.getNickName());
        userVO.setUserAvatar(user.getAvatar());
        // 用户等级 （西米独有 ）
        userVO.setUserRoomVipStatus(enterNoticeEntry.getUserRoomVipStatus());
        if (!StringUtils.isEmpty(enterNoticeEntry.getUserCover())) {
            userVO.setUserId(String.valueOf(enterNoticeEntry.getUserId()));
            userVO.setUserAvatar(enterNoticeEntry.getUserCover());
        }

        return userVO;
    }

    /**
     * 构建查询评论参数
     *
     * @param liveId        直播节目ID
     * @param performanceId performanceId
     * @param config        评论公共配置
     * @return 参数
     */
    public GetCommentWithServerTimeParam buildQueryParam(Long liveId, String performanceId, CommentCommonConfig config) {
        GetCommentWithServerTimeParam.GetCommentWithServerTimeParamBuilder builder = GetCommentWithServerTimeParam.builder();
        long endTime = System.currentTimeMillis();
        // 结束时间往前推1s
        endTime = endTime - commentConfig.getCommentRepetitionInterval();
        // 判断开始时间是否为null，如果为null，则是首次获取
        boolean isNjFirst = performanceId != null;
        //第一次进入直播间
        boolean firstEntry = performanceId == null;
        // 计算评论范围开始时间
        long startTime = getCommentScopeStartTime(performanceId, endTime, config);
        int getLiveCount = getQueryCount(startTime, endTime, liveId, config);
        builder.liveId(liveId)
                .count(getLiveCount)
                .startTime(startTime)
                .endTime(endTime)
                .firstEntry(firstEntry)
                .appId(ContextUtils.getContext().getHeader().getAppId())
                .commentCacheSwitch(config.isCommentCacheSwitch())
                .njFirst(isNjFirst);
        return builder.build();
    }

    /**
     * 从缓存中获取评论列表
     *
     * @param param 参数
     * @return 结果
     */
    public ResultVO<GetCommentWithServerTimeResult> getCommentListFromCache(GetCommentWithServerTimeParam param) {
        try {
            if (param.isCommentCacheSwitch()) {
                //如果开启了缓存，就需要取整秒，提高命中率
                param.setStartTime(param.getStartTime() / 1000 * 1000);
                param.setEndTime(param.getEndTime() / 1000 * 1000);
                //从redis中获取评论缓存
                List<TransientComment> comments = commentRedis.getCommentWithTime(param);

                //过滤标记的空评论数据
                Pair<List<TransientComment>, List<TransientComment>> filteredMarkComment = filterMarkComment(comments);
                //不存在标记位，或者标记位小于当前查询的结束时间，则走DC查询
                boolean onlyReadCache = isOnlyReadCache(param, filteredMarkComment.getLeft());
                if (!onlyReadCache) {
                    return ResultVO.failure(CommentMsgCodes.GET_LATEST_COMMENT_CACHE_FAIL.getCode(), CommentMsgCodes.GET_LATEST_COMMENT_CACHE_FAIL.getMsg());
                }
                log.info("getCommentListFromCache, liveId={},userId={}, startTime={},endTime={},resSize={}",
                        param.getLiveId(), ContextUtils.getContext().getUserId(),
                        DateUtil.formatDateToString(new Date(param.getStartTime()), DateUtil.datetime_2)
                        , DateUtil.formatDateToString(new Date(param.getEndTime()), DateUtil.datetime_2),
                        filteredMarkComment.getRight().size());

                //下一次请求的开始时间
                long commentServerTime = comments.get(comments.size() - 1).getTime();
                //监控打点
                commentMonitorManager.commentQueryState(1, true);
                GetCommentWithServerTimeResult result = GetCommentWithServerTimeResult.builder()
                        .transientComments(filteredMarkComment.getRight()).commentServerTime(commentServerTime).build();
                return ResultVO.success(result);
            }
        } catch (Exception e) {
            log.warn("getCommentListFromCache happen error:", e);
        }
        return ResultVO.failure(CommentMsgCodes.GET_LATEST_COMMENT_CACHE_FAIL.getCode(), CommentMsgCodes.GET_LATEST_COMMENT_CACHE_FAIL.getMsg());
    }

    /**
     * 获取评论范围开始时间
     *
     * @param performanceId 开始时间的字符串
     * @param endTime       结束时间
     * @param config        配置
     * @return 开始时间
     */
    private long getCommentScopeStartTime(String performanceId, long endTime, CommentCommonConfig config) {
        long startTime;
        if (Strings.isNullOrEmpty(performanceId)) {
            // performanceId中没有记录上次的时间区间上限时，拿当前时间往前推配置字段值的时间为开始时间
            startTime = endTime - config.getRequestInterval() * 1000L;
        } else {
            startTime = Long.parseLong(performanceId);
            // 从performanceId中获取的上次的时间区间上限值大等于endTime时，拿当前时间往前推配置字段值的时间为开始时间
            if (startTime >= endTime) {
                startTime = endTime - (config.getRequestInterval() * 1000L);
            }
        }
        return startTime;
    }

    /**
     * 过滤进房公告
     *
     * @param isFilter           是否过滤
     * @param userId             用户ID
     * @param appId              应用ID
     * @param enterNoticeEntries 进房公告消息
     * @return 结果
     */
    private List<EnterNoticeEntry> filterEnterNotice(boolean isFilter, long userId, int appId, List<EnterNoticeEntry> enterNoticeEntries) {
        if (isFilter) {
            // 进行过滤
            enterNoticeEntries =
                    commentFilterManager.filterEnterNoticeByUserSetting(userId, appId, enterNoticeEntries);
        }
        return enterNoticeEntries;
    }

    /**
     * 获取评论之后的操作
     *
     * @param param             参数
     * @param transientComments 评论列表
     */
    private void getCommentAfterProcessor(GetCommentWithServerTimeParam param, List<TransientComment> transientComments) {
        try {
            //没有开启开关，结束
            if (!param.isCommentCacheSwitch()) {
                return;
            }

            List<TransientComment> list = new ArrayList<>(transientComments);
            TransientComment commentEndMark = TransientComment.builder()
                    .commentMark(CommentMarkEnum.MARK_COMMENT.getMark())
                    .time(param.getEndTime()).build();
            list.add(commentEndMark);

            //如果查询成功，直接存数据到缓存中，不管缓存开关是否开启，都要保存，避免缓存开关在直播间开启后，直播间内的评论列表为空
            commentRedis.saveLatestCommentMsg(param.getAppId(), param.getLiveId(), list);
        } catch (Exception e) {
            log.warn("save Comment cache info fail:", e);
        }
    }

    /**
     * 过滤标记评论和正常评论
     *
     * @param comments 评论列表
     * @return left: 标记评论列表，right: 正常评论列表
     */
    private Pair<List<TransientComment>, List<TransientComment>> filterMarkComment(List<TransientComment> comments) {
        List<TransientComment> markComment = new ArrayList<>();
        List<TransientComment> normalComment = new ArrayList<>();
        CommentMarkEnum marKCommentEnum = CommentMarkEnum.MARK_COMMENT;
        for (TransientComment comment : comments) {
            //标记数据（空评论）
            if (comment.getCommentMark() == marKCommentEnum.getMark()) {
                markComment.add(comment);
                continue;
            }
            //正常评论
            normalComment.add(comment);
        }
        //对markComment集合按时间进行倒叙排序
        markComment.sort(Comparator.comparing(TransientComment::getTime).reversed());
        return Pair.of(markComment, normalComment);
    }

    /**
     * 判断是否仅读缓存数据
     *
     * @param param    参数
     * @param comments 评论列表结果
     * @return true: 读取缓存，false: 读dc
     */
    private boolean isOnlyReadCache(GetCommentWithServerTimeParam param, List<TransientComment> comments) {
        if (CollectionUtils.isEmpty(comments)) {
            return false;
        }

        //标记位时间戳最大的记录
        TransientComment comment = comments.get(0);
        //如果最后一条评论是标记评论，且时间小于等于当前请求的结束时间，就仅读评论
        return param.getEndTime() <= comment.getTime();
    }

    /**
     * 获取查询评论数量
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @param liveId    直播间ID
     * @param config    公共配置
     * @return 时间间隔
     */
    private int getQueryCount(long startTime, long endTime, long liveId, CommentCommonConfig config) {
        try {
            int timeDelay = (int) (endTime - startTime);
            if (timeDelay >= config.getUpdateCommentCountTimeInterval()) {
                log.info("updateCommentCountTimeInterval,liveId={}, userId={}, startTime={}, endTime={},count={}", liveId,
                        ContextUtils.getContext().getUserId(), startTime, endTime, config.getMaxLiveCommentsRespCount());
                return config.getMaxLiveCommentsRespCount();
            }
        } catch (Exception e) {
            log.warn("getQueryCount fail,liveId={}, userId={}", liveId, ContextUtils.getContext().getUserId());
        }
        return config.getLiveCommentsRespLimit();
    }


    /**
     * 获取所有的评论的样式
     *
     * @return
     */
    public ResultVO<List<CommentStyle>> getCommentStyleList() {
        Result<List<CommentStyle>> result = liveCommentServiceRemote.getCommentStyleList();
        if (result.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
            return ResultVO.failure("同步评论样式异常");
        }
        List<CommentStyle> commentStyles = result.target();
        return ResultVO.success(commentStyles);
    }


}
