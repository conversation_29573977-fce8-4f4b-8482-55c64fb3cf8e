package fm.lizhi.ocean.wave.comment.core.extension.medal.v2;

import com.google.common.collect.Lists;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.commons.service.client.rcode.GeneralRCode;
import fm.lizhi.ocean.wave.comment.core.extension.medal.bean.GenMedalInfoV2Context;
import fm.lizhi.ocean.wave.comment.core.model.vo.BadgeImageVO;
import fm.lizhi.ocean.wave.comment.core.remote.bean.FreshUser;
import fm.lizhi.ocean.wave.comment.core.remote.param.CheckUserIsIllegalParam;
import fm.lizhi.ocean.wave.comment.core.remote.result.CheckUserIsIllegalResult;
import fm.lizhi.ocean.wave.comment.core.remote.service.IFamilyServiceRemote;
import fm.lizhi.ocean.wave.user.api.UserService;
import fm.lizhi.ocean.wave.user.result.FreshUserResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Optional;

import static fm.lizhi.ocean.wave.comment.core.extension.medal.bean.GenMedalInfoV2Context.MedalShowArea.COMMENT_AREA;
import static fm.lizhi.ocean.wave.comment.core.extension.medal.bean.GenMedalInfoV2Context.MedalShowArea.ENTER_NOTICE_AREA;

/**
 * 新用户勋章节点-HY
 * <AUTHOR>
 */
@Slf4j
@Component
public class HyFreshUserMedalV2Node implements ICommentV2MedalNode {

    @Resource
    private IFamilyServiceRemote familyServiceRemote;

    @Autowired
    private UserService userService;

    @Override
    public Optional<List<BadgeImageVO>> buildMedalImageInfo(GenMedalInfoV2Context context) {
        int medalShowArea = context.getMedalShowArea();
        FreshUser freshUser = null;
        long userId = 0;
        if (medalShowArea == COMMENT_AREA.getArea()) {
            //评论的新用户信息是在发送评论时就有的
            freshUser = context.getComment().getFreshUser();
            userId = context.getComment().getUserId();
        } else if (medalShowArea == ENTER_NOTICE_AREA.getArea()) {
            //进房公告的新用户信息，需要在构建时查询
            freshUser = context.getEnterNoticeEntry().getFreshUserEntry();
            userId = context.getEnterNoticeEntry().getUserId();

            Result<FreshUserResult> freshUserResult = userService.isFreshUserByCache(userId);
            if (freshUserResult.rCode() == 0 && freshUserResult.target() != null) {
                FreshUserResult userResult = freshUserResult.target();
                freshUser = FreshUser.builder().is(userResult.isIs()).url(userResult.getUrl()).aspect(userResult.getAspect()).build();
            }
        }

        if (freshUser == null || StringUtils.isBlank(freshUser.getUrl())) {
            return Optional.empty();
        }

        // 判断用户是否违规
        Boolean checkUserIsIllegal = checkUserIsIllegal(userId);
        if (checkUserIsIllegal) {
            BadgeImageVO badgeImageVO = new BadgeImageVO();
            badgeImageVO.setBadgeUrl(freshUser.getUrl());
            badgeImageVO.setBadgeAspect(freshUser.getAspect());
            return Optional.of(Lists.newArrayList(badgeImageVO));
        }
        return Optional.empty();
    }

    /**
     * 非法用户检查
     *
     * @param userId 用户id
     * @return 是否非法，true：不是，false：是
     */
    private Boolean checkUserIsIllegal(Long userId) {
        CheckUserIsIllegalParam param = new CheckUserIsIllegalParam();
        param.setUserId(userId);
        Result<CheckUserIsIllegalResult> result = familyServiceRemote.checkUserIsIllegal(param);
        if (result.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
            log.warn("checkUserIsIllegal error, param:{}", param);
            return true;
        }
        return result.target().getNoRisk();
    }
}
