package fm.lizhi.ocean.wave.comment.core.manager.filter;

import fm.lizhi.ocean.wave.comment.core.model.dto.EnterNoticeDTO;

import java.util.ArrayList;
import java.util.List;

/**
 * 进场消息财富登记过滤器
 *
 * <AUTHOR>
 */
public class EnterNoticeWealthLevelV2Filter implements EnterNoticeV2Filter {

    private final Integer wealthLevel;

    public EnterNoticeWealthLevelV2Filter(Integer wealthLevel) {
        this.wealthLevel = wealthLevel;
    }

    @Override
    public List<EnterNoticeDTO> filter(List<EnterNoticeDTO> msg) {
        List<EnterNoticeDTO> result = new ArrayList<>();
        if (wealthLevel == null || wealthLevel <= 0) {
            return msg;
        }

        for (EnterNoticeDTO enterNoticeEntry : msg) {
            if (enterNoticeEntry.getWealthInfo() == null) {
                continue;
            }
            if (enterNoticeEntry.getWealthInfo().getLevel() >= wealthLevel) {
                result.add(enterNoticeEntry);
            }
        }
        return result;
    }
}
