package fm.lizhi.ocean.wave.comment.core.manager;

import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.commons.service.client.rcode.GeneralRCode;
import fm.lizhi.ocean.wave.comment.core.config.CommentCommonConfig;
import fm.lizhi.ocean.wave.comment.core.config.CommentConfig;
import fm.lizhi.ocean.wave.comment.core.constants.CommentMsgCodes;
import fm.lizhi.ocean.wave.comment.core.constants.WaveEnterNoticeType;
import fm.lizhi.ocean.wave.comment.core.convert.EnterNoticeMsgConvert;
import fm.lizhi.ocean.wave.comment.core.extension.comment.IEnterNoticeMsgProcessor;
import fm.lizhi.ocean.wave.comment.core.extension.medal.bean.GenMedalInfoV2Context;
import fm.lizhi.ocean.wave.comment.core.extension.medal.v2.ICommentV2MedalNode;
import fm.lizhi.ocean.wave.comment.core.model.dto.EnterNoticeDTO;
import fm.lizhi.ocean.wave.comment.core.model.result.LatestEnterMsgResult;
import fm.lizhi.ocean.wave.comment.core.model.vo.BadgeImageVO;
import fm.lizhi.ocean.wave.comment.core.model.vo.EnterMsgVO;
import fm.lizhi.ocean.wave.comment.core.model.vo.MsgUserVO;
import fm.lizhi.ocean.wave.comment.core.remote.bean.EnterNoticesParam;
import fm.lizhi.ocean.wave.comment.core.remote.param.GetEnterNoticesByRangeParam;
import fm.lizhi.ocean.wave.comment.core.remote.param.GetNewFanNoticesByRangeParam;
import fm.lizhi.ocean.wave.comment.core.remote.param.GetVehicleEnterNoticesByRangeParam;
import fm.lizhi.ocean.wave.comment.core.remote.result.*;
import fm.lizhi.ocean.wave.comment.core.remote.service.IEnterMsgQueryServiceRemote;
import fm.lizhi.ocean.wave.common.auto.route.common.remote.MyAutowired;
import fm.lizhi.ocean.wave.common.extension.ProcessorV2Factory;
import fm.lizhi.ocean.wave.common.util.RpcResult;
import fm.lizhi.ocean.wave.common.util.TimeUtil;
import fm.lizhi.ocean.wave.live.core.remote.bean.LiveBean;
import fm.lizhi.ocean.wave.live.core.remote.param.GetLiveRemoteParam;
import fm.lizhi.ocean.wave.live.core.remote.result.GetLiveRemoteResult;
import fm.lizhi.ocean.wave.live.core.remote.service.ILiveServiceRemote;
import fm.lizhi.ocean.wave.server.common.util.ContextUtils;
import fm.lizhi.ocean.wave.server.common.vo.ResultVO;
import fm.lizhi.ocean.wave.user.api.UserService;
import fm.lizhi.ocean.wave.user.bean.SimpleUser;
import fm.lizhi.ocean.wave.user.bean.UserAvatarWidget;
import fm.lizhi.ocean.wave.user.param.BatchGetUserParam;
import fm.lizhi.ocean.wave.user.result.BatchGetSimpleUserResult;
import hy.fm.lizhi.live.enternotice.meta.contants.EnterNoticeConst;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 进房公告管理器
 *
 * <AUTHOR>
 * @date 2025/5/06
 */
@Slf4j
@Component
public class EnterNoticeMsgManager {

    @MyAutowired
    private IEnterMsgQueryServiceRemote enterNoticeServiceRemote;

    @Autowired
    private ProcessorV2Factory factory;

    @Autowired
    private CommentConfig commentConfig;

    @Autowired
    private CommentFilterManager commentFilterManager;

    @MyAutowired
    private ILiveServiceRemote liveServiceRemote;

    @Autowired
    private UserService userService;

    public ResultVO<LatestEnterMsgResult> getEnterNoticeList(Long liveId, String performanceId, boolean isFilter) {
        LiveBean live = getLive(liveId);
        if (live == null) {
            log.warn("getEnterNoticeList live not exist, liveId:{}", liveId);
            return ResultVO.failure(CommentMsgCodes.GET_LATEST_COMMENT_LIVE_NOT_EXIST.getCode(), CommentMsgCodes.GET_LATEST_COMMENT_LIVE_NOT_EXIST.getMsg());
        }

        //从下游业务查询出进房消息
        ResultVO<GetEnterNoticeMsgResult> enterNoticeRes = latestEnterNotice(live, performanceId, isFilter);
        if (!enterNoticeRes.isOK()) {
            log.warn("getComments enterNoticeListRes fail, liveId:{}, performanceId:{}", liveId, performanceId);
            return ResultVO.failure(CommentMsgCodes.GET_LATEST_COMMENT_FAIL.getCode(), CommentMsgCodes.GET_LATEST_COMMENT_FAIL.getMsg());
        }

        //构建参数
        IEnterNoticeMsgProcessor processor = factory.getProcessor(IEnterNoticeMsgProcessor.class);
        List<EnterMsgVO> enterNoticeVOList = transformNoticeToVO(live, enterNoticeRes.getData().getEntries(), processor, ContextUtils.getContext().getUserId());
        //打印出userId和进房文案
        printMsgLog(enterNoticeVOList, processor);
        return ResultVO.success(new LatestEnterMsgResult()
                .setQueryInterval(commentConfig.getQueryEnterMsgInterval())
                .setEnterNotice(enterNoticeVOList)
                .setPerformanceId(String.valueOf(enterNoticeRes.getData().getEndTime()))
                .setLastPage(enterNoticeVOList.size() < commentConfig.getEnterNoticeCount()));
    }

    /**
     * 获取最新的进房公告
     *
     * @param live          直播间信息
     * @param performanceId 本次查询的标记，是上次最后一条数据的结束时间，作为本次查询的开始时间
     * @param isFilter      是否需要过滤
     * @return 结果
     */
    public ResultVO<GetEnterNoticeMsgResult> latestEnterNotice(LiveBean live, String performanceId, boolean isFilter) {
        long userId = ContextUtils.getContext().getUserId();

        //构建参数
        IEnterNoticeMsgProcessor processor = factory.getProcessor(IEnterNoticeMsgProcessor.class);

        //如果不可以获取进房公告，直接退出
        if (!processor.isCanGetEnterNotice(live.getId())) {
            return ResultVO.success(GetEnterNoticeMsgResult.builder().entries(new ArrayList<>()).endTime(System.currentTimeMillis()).build());
        }

        // 生成参数
        long endTime = System.currentTimeMillis();
        EnterNoticesParam enterNoticesParam = generateEnterNoticesParam(live, userId, performanceId, endTime, processor.getCommentConfig());

        // 请求获取进房公告
        List<EnterNoticeDTO> enterNotices = getEnterNoticesList(enterNoticesParam, processor);

        //对进房公告进行过滤
        List<EnterNoticeDTO> enterNoticeEntries = filterEnterNotice(isFilter, userId, enterNotices, processor);
        return ResultVO.success(GetEnterNoticeMsgResult.builder().entries(enterNoticeEntries).endTime(endTime).build());
    }

    /**
     * 转换成VO
     *
     * @param live               直播节目ID
     * @param enterNoticeEntries 进场通知列表
     * @param processor          差异化处理器
     * @return 进场通知VO列表
     */
    private List<EnterMsgVO> transformNoticeToVO(LiveBean live,
                                                 List<EnterNoticeDTO> enterNoticeEntries,
                                                 IEnterNoticeMsgProcessor processor, long userId) {
        List<ICommentV2MedalNode> enterMedalNodes = processor.getBuildEnterNoticeMedalNodes();
        CommentCommonConfig commentConfig = processor.getCommentConfig();
        List<Long> userIds = enterNoticeEntries.stream().map(EnterNoticeDTO::getUserId).collect(Collectors.toList());
        Map<Long, UserAvatarWidget> userAvatarWidgetMap = userService.batchGetUserAvatarWidget(userIds).target();
        Result<BatchGetSimpleUserResult> userRes = userService.batchGetSimpleUserByCache(BatchGetUserParam.builder().userIdList(userIds).build());
        if (RpcResult.isFail(userRes)) {
            log.warn("transformNoticeToVO batchGetSimpleUserByCache fail, userIds={}", userIds);
            return new ArrayList<>();
        }

        Map<Long, SimpleUser> simpleUserMap = userRes.target().getUserList().stream().collect(Collectors.toMap(SimpleUser::getUserId, simpleUser -> simpleUser));
        return enterNoticeEntries.stream()
                .map(enterNoticeEntry -> {
                    //生成进房公告勋章图标
                    List<BadgeImageVO> imgIcons = generateMedalIcons(live, enterNoticeEntry, commentConfig, enterMedalNodes);
                    MsgUserVO<?> msgUserVO = processor.buildUserVoAndAdapterExtra(enterNoticeEntry);
                    msgUserVO = EnterNoticeMsgConvert.I.buildUserVO(enterNoticeEntry, userAvatarWidgetMap, simpleUserMap, msgUserVO);
                    if (msgUserVO == null) {
                        log.warn("transformNoticeToVO user not exist,liveId={}, userId:{}, content={}",
                                live.getId(), enterNoticeEntry.getUserId(), enterNoticeEntry.getNotice());
                        return null;
                    }
                    msgUserVO.setUserIcons(imgIcons);
                    EnterMsgVO enterNoticeVO = EnterNoticeMsgConvert.I.convertNoticeVO(enterNoticeEntry, msgUserVO);
                    processor.setShowButton(live.getId(), userId, enterNoticeEntry, enterNoticeVO);
                    return enterNoticeVO;
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    /**
     * 过滤进房公告
     *
     * @param isFilter           是否过滤
     * @param userId             用户ID
     * @param enterNoticeEntries 进房公告消息
     * @param processor          差异化流程处理器
     * @return 结果
     */
    private List<EnterNoticeDTO> filterEnterNotice(boolean isFilter, long userId, List<EnterNoticeDTO> enterNoticeEntries, IEnterNoticeMsgProcessor processor) {
        Integer appId = ContextUtils.getContext().getHeader().getAppId();
        if (isFilter) {
            // 进行过滤
            enterNoticeEntries =
                    commentFilterManager.filterEnterMsgByUserSetting(userId, appId, enterNoticeEntries);
        }

        //过滤掉隐藏的进房公告
        return processor.filterHideEnterNotice(enterNoticeEntries);
    }

    /**
     * 获取进房公告列表
     *
     * @param enterNoticesParam 进房公告参数
     * @return 进房公告列表
     */
    private List<EnterNoticeDTO> getEnterNoticesList(EnterNoticesParam enterNoticesParam, IEnterNoticeMsgProcessor processor) {
        Long liveId = enterNoticesParam.getLiveId();
        long startTime = enterNoticesParam.getStartTime();
        long endTime = enterNoticesParam.getNoticeEndTime();
        boolean isNj = enterNoticesParam.getIsNj();
        Long userId = enterNoticesParam.getUserId();

        List<EnterNoticeDTO> enterNoticeEntries = new ArrayList<>(8);
        //大主播，不显示座驾
        if (!processor.isBigStarNjHideMount(enterNoticesParam.getNjId())) {
            //先取座驾,再取普通
            enterNoticeEntries =
                    this.getEnterNoticesByType(WaveEnterNoticeType.VEHICLE, liveId, startTime, endTime, commentConfig.getEnterNoticeCount(), userId);
        }

        if (enterNoticeEntries.size() >= commentConfig.getEnterNoticeCount()) {
            return enterNoticeEntries;
        }

        //取普通的
        int left = commentConfig.getEnterNoticeCount() - enterNoticeEntries.size();
        List<EnterNoticeDTO> enterNoticesCommon =
                this.getEnterNoticesByType(WaveEnterNoticeType.NORMAL, liveId, startTime, endTime, left, userId);
        if (!enterNoticesCommon.isEmpty()) {
            enterNoticeEntries.addAll(enterNoticesCommon);
        }

        if (isNj) {
            //  主播才有关注消息
            List<EnterNoticeDTO> enterNoticesNewFan =
                    this.getEnterNoticesByType(WaveEnterNoticeType.NEW_FANS, liveId, startTime, endTime, left, userId);
            if (!enterNoticesNewFan.isEmpty()) {
                enterNoticeEntries.addAll(enterNoticesNewFan);
            }
        }
        return enterNoticeEntries;
    }

    /**
     * 获取进房公告列表
     *
     * @param type      进房公告类型，详细枚举如：@link{fm.lizhi.ocean.wave.comment.core.constants.WaveEnterNoticeType}
     * @param liveId    liveId
     * @param startTime 公告开始时间
     * @param endTime   公告结束时间
     * @param left      剩余数量
     * @param userId    当前请求的用户id
     * @return 进房公告列表
     */
    private List<EnterNoticeDTO> getEnterNoticesByType(int type, long liveId, long startTime, long endTime, int left, long userId) {
        List<EnterNoticeDTO> enterNoticeEntries = new ArrayList<>(30);
        //先取座驾,再取普通
        int anonymousCount = 0;
        List<EnterNoticeDTO> listVehicleTemp = null;
        if (type == EnterNoticeConst.EnterNoticeType.VEHICLE) {
            listVehicleTemp = getVehicleEnterNotices(liveId, startTime, endTime, userId);
        } else if (type == EnterNoticeConst.EnterNoticeType.NEW_FANS) {
            listVehicleTemp = getNewFanEnterNotices(liveId, startTime, endTime, userId);
        } else {
            listVehicleTemp = getEnterNotices(liveId, startTime, endTime, userId);
            for (EnterNoticeDTO item : listVehicleTemp) {
                if (item.getAnonymous() == 1) {
                    //匿名算一个
                    anonymousCount++;
                }
            }
        }
        if (listVehicleTemp != null && !listVehicleTemp.isEmpty()) {
            enterNoticeEntries.addAll(listVehicleTemp);
        }
        //最多取指定条
        if (enterNoticeEntries.size() - anonymousCount >= left) {
            return enterNoticeEntries;
        }
        return enterNoticeEntries;
    }


    /**
     * 获取座驾进房公告
     *
     * @param liveId      直播id
     * @param startTime   开始时间
     * @param rangEndTime 结束时间
     * @return 结果
     */
    private List<EnterNoticeDTO> getVehicleEnterNotices(long liveId, long startTime, long rangEndTime, long userId) {
        GetVehicleEnterNoticesByRangeParam param = new GetVehicleEnterNoticesByRangeParam();
        param.setLiveId(liveId);
        param.setStartTimeStamp(startTime);
        param.setEndTimeStamp(rangEndTime);
        param.setUserId(userId);
        Result<GetVehicleEnterNoticesResult> result = enterNoticeServiceRemote.getVehicleEnterNoticesByRange(param);
        if (result.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
            log.error("getVehicleEnterNotices error:liveId={},rCode={}", liveId, result.rCode());
            return Lists.newArrayList();
        }

        return result.target().getEnterNotices();
    }

    /**
     * 获取粉丝进房消息
     *
     * @param liveId    直播id
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 粉丝进房消息列表
     */
    private List<EnterNoticeDTO> getNewFanEnterNotices(long liveId, long startTime, long endTime, long userId) {
        // 构建获取粉丝进房参数
        GetNewFanNoticesByRangeParam param = new GetNewFanNoticesByRangeParam();
        param.setLiveId(liveId);
        param.setStartTimeStamp(startTime);
        param.setEndTimeStamp(endTime);
        param.setUserId(userId);

        // 获取粉丝进房消息
        Result<GetNewFanNoticesResult> result = enterNoticeServiceRemote.getNewFanNoticesByRange(param);
        if (result.rCode() != 0) {
            log.error("getNewfanEnterNotices error:liveId={}`rCode={}", liveId, result.rCode());
            return Lists.newArrayList();
        }

        return result.target().getEnterNotices();
    }

    /**
     * 获取普通进场消息
     *
     * @param liveId    直播id
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 普通进场消息列表
     */
    private List<EnterNoticeDTO> getEnterNotices(long liveId, long startTime, long endTime, long userId) {
        // 构建获取进场消息参数
        GetEnterNoticesByRangeParam param = new GetEnterNoticesByRangeParam();
        param.setLiveId(liveId);
        param.setStartTimeStamp(startTime);
        param.setEndTimeStamp(endTime);
        param.setUserId(userId);

        // 获取进场消息
        Result<GetEnterNoticesResult> result = enterNoticeServiceRemote.getEnterNoticesByRange(param);
        if (result.rCode() != 0) {
            log.error("getEnterNotices error:liveId={}`rCode={}", liveId, result.rCode());
            return Lists.newArrayList();
        }

        return result.target().getEnterNotices();
    }

    /**
     * 获取直播信息
     *
     * @param liveId 直播id
     * @return 直播信息
     */
    private LiveBean getLive(Long liveId) {
        GetLiveRemoteParam getLiveRemoteParam = new GetLiveRemoteParam();
        getLiveRemoteParam.setLiveId(liveId);
        //getLiveWithCache底层也是加了本地缓存，如果能上游控制，就不在下游控制，同时减少网络IO
        Result<GetLiveRemoteResult> liveResult = liveServiceRemote.getLiveByCache(getLiveRemoteParam);
        if (liveResult.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
            log.error("getLive error, param:{}", getLiveRemoteParam);
            return null;
        }

        return liveResult.target().getLiveBean();
    }

    /**
     * 生成勋章图标
     *
     * @param liveBean         直播间信息
     * @param enterNoticeEntry 进房消息
     * @param commentConfig    评论配置
     * @param enterMedalNodes  进房勋章构建节点列表
     * @return 勋章图标列表
     */
    private List<BadgeImageVO> generateMedalIcons(LiveBean liveBean, EnterNoticeDTO enterNoticeEntry,
                                                  CommentCommonConfig commentConfig,
                                                  List<ICommentV2MedalNode> enterMedalNodes) {
        //构建勋章图标上下文
        GenMedalInfoV2Context context = GenMedalInfoV2Context.builder()
                .appId(ContextUtils.getContext().getHeader().getAppId())
                .commentConfig(commentConfig)
                .enterNoticeEntry(enterNoticeEntry)
                .liveBean(liveBean)
                .medalShowArea(GenMedalInfoV2Context.MedalShowArea.ENTER_NOTICE_AREA.getArea())
                .build();

        List<BadgeImageVO> imgIcons = new ArrayList<>();
        for (ICommentV2MedalNode medalNode : enterMedalNodes) {
            Optional<List<BadgeImageVO>> listOptional = medalNode.buildMedalImageInfo(context);
            listOptional.ifPresent(imgIcons::addAll);
        }
        return imgIcons;
    }

    /**
     * 生成查询进房消息参数
     *
     * @param live          直播节目
     * @param userId        用户ID
     * @param performanceId 本次查询的标记，是上次最后一条数据的结束时间，作为本次查询的开始时间
     * @param config        apollo 配置
     * @return 结果
     */
    public EnterNoticesParam generateEnterNoticesParam(LiveBean live, Long userId, String performanceId, long endTime, CommentCommonConfig config) {
        // 结束时间往后推1s
        long noticeEndTime = TimeUtil.ceilTimestamp(endTime, TimeUnit.SECONDS, commentConfig.getEnterEndTimeDelaySec());
        int enterNoticeSecond = config.getEnterNoticeSecond();
        long startTime = getNoticeStartTime(performanceId, endTime);
        if (startTime == 0) {
            //首次进入，获取最近5轮
            startTime = endTime - enterNoticeSecond * 5L;
        }

        // 是否主播
        boolean isNj = live.getUserId().equals(userId);
        log.info("generateEnterNoticesParam:liveId={}`userId={}`startTime={}`endTime={},originalEndTime={}", live.getId(), userId, startTime, noticeEndTime,  endTime);
        EnterNoticesParam param = new EnterNoticesParam();
        param.setNoticeEndTime(noticeEndTime);
        param.setStartTime(startTime);
        param.setIsNj(isNj);
        param.setLiveId(live.getId());
        param.setUserId(userId);
        param.setAppId(ContextUtils.getContext().getHeader().getAppId());
        param.setNjId(live.getUserId());
        param.setIsFilter(param.getIsFilter());
        param.setCommentCacheSwitch(config.isEnterNoticeCacheSwitch());
        return param;
    }

    private long getNoticeStartTime(String performanceId, long noticeEndTime) {
        long startTime;
        if (Strings.isNullOrEmpty(performanceId)) {
            return 0;
        } else {
            startTime = Long.parseLong(performanceId);
            return startTime > noticeEndTime ? 0 : startTime;
        }
    }

    private void printMsgLog(List<EnterMsgVO> enterNoticeEntries, IEnterNoticeMsgProcessor processor) {
        try {
            CommentCommonConfig config = processor.getCommentConfig();
            if (config.isEnterMsgLogSwitch() && CollectionUtils.isNotEmpty(enterNoticeEntries)) {
                enterNoticeEntries.forEach(enterNoticeVO -> {
                    log.info("enterNoticeMsg:  userId={}, content={}", enterNoticeVO.getUserId(), enterNoticeVO.getContent());
                });
            }
        } catch (Exception e) {
            log.error("printMsgLog error:", e);
        }
    }
}
