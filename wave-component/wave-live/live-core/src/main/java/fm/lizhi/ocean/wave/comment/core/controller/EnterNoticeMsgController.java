package fm.lizhi.ocean.wave.comment.core.controller;

import fm.lizhi.ocean.wave.comment.core.manager.EnterNoticeMsgManager;
import fm.lizhi.ocean.wave.comment.core.model.param.GetLatestEnterMsgParam;
import fm.lizhi.ocean.wave.comment.core.model.result.LatestEnterMsgResult;
import fm.lizhi.ocean.wave.server.common.auth.annotation.VerifyUserToken;
import fm.lizhi.ocean.wave.server.common.vo.ResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
 * 进房消息控制器
 * <AUTHOR>
 */
@RestController
@RequestMapping("/enter")
public class EnterNoticeMsgController {

    @Autowired
    private EnterNoticeMsgManager enterNoticeMsgManager;

    @RequestMapping("/getLatestEnterMsg")
    @VerifyUserToken
    public ResultVO<LatestEnterMsgResult> getLatestEnterMsg(@Valid GetLatestEnterMsgParam param) {
        return enterNoticeMsgManager.getEnterNoticeList(param.getLiveId(), param.getPerformanceId(), false);
    }

    @RequestMapping("/getFilterLatestEnterMsg")
    @VerifyUserToken
    public ResultVO<LatestEnterMsgResult> getFilterLatestEnterMsg(@Valid GetLatestEnterMsgParam param) {
        return enterNoticeMsgManager.getEnterNoticeList(param.getLiveId(), param.getPerformanceId(), true);
    }
}
