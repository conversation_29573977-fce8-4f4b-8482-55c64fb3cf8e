package fm.lizhi.ocean.wave.comment.core.remote.service.pp;

import com.alibaba.dubbo.common.utils.StringUtils;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.commons.service.client.rcode.GeneralRCode;
import fm.lizhi.ocean.wave.comment.core.config.CommentConfig;
import fm.lizhi.ocean.wave.comment.core.constants.CommentTypeMapping;
import fm.lizhi.ocean.wave.comment.core.constants.WaveCommentType;
import fm.lizhi.ocean.wave.comment.core.model.dto.PpCommentExtraDTO;
import fm.lizhi.ocean.wave.comment.core.model.dto.TransientCommentDTO;
import fm.lizhi.ocean.wave.comment.core.remote.adapter.pp.PpCommentQueryAdapter;
import fm.lizhi.ocean.wave.comment.core.remote.bean.GrowRelationLevel;
import fm.lizhi.ocean.wave.comment.core.remote.bean.GuardMedal;
import fm.lizhi.ocean.wave.comment.core.remote.bean.SimpleMedal;
import fm.lizhi.ocean.wave.comment.core.remote.bean.VipLevel;
import fm.lizhi.ocean.wave.comment.core.remote.param.GetCommentWithServerTimeParam;
import fm.lizhi.ocean.wave.comment.core.remote.result.GetLiveCommentResult;
import fm.lizhi.ocean.wave.comment.core.remote.service.ILiveCommentQueryServiceRemote;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wave.server.common.util.ContextUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import pp.fm.lizhi.datacenter.comment.pp.api.TransientCommentService;
import pp.fm.lizhi.datacenter.comment.pp.protocol.CommentProto;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 直播评论查询服务
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class PpLiveCommentQueryServiceRemote implements ILiveCommentQueryServiceRemote {

    @Resource
    private TransientCommentService transientCommentService;

    @Resource
    private CommentConfig commentConfig;


    @Override
    public boolean support(BusinessEvnEnum evnEnum) {
        return BusinessEvnEnum.PP.equals(evnEnum);
    }

    /**
     * 获取评论列表
     *
     * @param param 获取评论列表参数
     * @return 评论列表
     */
    @Override
    public Result<GetLiveCommentResult> getCommentWithServerTime(GetCommentWithServerTimeParam param) {
        Result<CommentProto.ResponseGetTransientComment> result = transientCommentService.getCommentWithServerTime(
                param.getLiveId(),
                param.getStartTime(), param.getEndTime(),
                param.getCount(), param.isNjFirst(),
                param.isFirstEntry());

        if (result.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
            log.error("getCommentWithServerTime failed, rCode={}`param={}", result.rCode(), param);
            return new Result<>(result.rCode(), null);
        }

        CommentProto.ResponseGetTransientComment responseGetTransientComment = result.target();
        List<CommentProto.TransientComment> transientCommentList = responseGetTransientComment.getTransientCommentList();
        List<TransientCommentDTO> transientComments = new ArrayList<>();
        for (CommentProto.TransientComment transientComment : transientCommentList) {
            if (isSpecialActivityComment(transientComment.getCommentType())) {
                continue;
            }
            //贵族等级
            CommentProto.PpVipLevel ppVipLevel = transientComment.hasPpVipLevel() ? transientComment.getPpVipLevel() : null;
            // 发送的用户
            List<CommentProto.ToUser> toUserList = transientComment.getToUserList();

            //数据转换
            TransientCommentDTO transientCommentDTO = PpCommentQueryAdapter.I.convertComment(transientComment);
            //贵族等级
            List<GrowRelationLevel> growRelationLevels = PpCommentQueryAdapter.I.convertGrowRelationLevelBeans(transientComment.getGrowRelationLevelList());
            //贵族等级
            VipLevel vipLevel = PpCommentQueryAdapter.I.convertVipLevel(ppVipLevel);
            //守护勋章
            GuardMedal guardMedal = PpCommentQueryAdapter.I.generateGuardMedal(transientComment.getGuardMedalUrl(), commentConfig.getPp().getDefaultLevelMedalAspect());
            //接受用户
            List<Long> toUserIds = generateToUserIdList(toUserList);
            //勋章列表
            List<SimpleMedal> simpleMedalList = PpCommentQueryAdapter.I.convertSimpleMedals(transientComment.getSimpleMedalList());

            // 评论类型转换
            int commentType = CommentTypeMapping.getWaveCommentType(transientComment.getCommentType(),
                    BusinessEvnEnum.from(ContextUtils.getContext().getHeader().getAppId()),
                    transientComment.getCommentTypeExtension());
            //如果评论类型是系统评论，且触发系统评论的用户ID存在，则使用触发者的头像
            long userId = commentType == WaveCommentType.SYSTEM_NOTICE_COMMENT && transientComment.getOperatorUserId() > 0 ?
                    transientComment.getOperatorUserId() : transientComment.getUserId();

            PpCommentExtraDTO ppCommentExtraDTO = new PpCommentExtraDTO()
                    .setVipLevel(vipLevel)
                    .setGrowRelationLevelList(growRelationLevels)
                    .setSimpleMedals(simpleMedalList)
                    .setMaskStatus(transientComment.getMaskStatus())
                    .setGuardMedal(guardMedal);
            transientCommentDTO.setPpCommentExtra(ppCommentExtraDTO);
            transientCommentDTO.setCommentType(commentType);
            transientCommentDTO.setUserId(userId);
            transientCommentDTO.setToUser(toUserIds);
            transientComments.add(transientCommentDTO);
        }

        GetLiveCommentResult commentResult = GetLiveCommentResult.builder()
                .transientComments(transientComments)
                .commentServerTime(Math.min(responseGetTransientComment.getCommentServerTime(), param.getEndTime()))
                .lastPage(param.getCount() > transientComments.size())
                .build();
        return new Result<>(result.rCode(), commentResult);
    }

    private List<Long> generateToUserIdList(List<CommentProto.ToUser> toUserList) {
        List<Long> toUserIds = new ArrayList<>();
        for (CommentProto.ToUser toUser : toUserList) {
            toUserIds.add(toUser.getUserId());
        }
        return toUserIds;
    }

    /**
     * 是否是互动玩法评论
     *
     * @param commentType 评论类型
     * @return true：是，false：否
     */
    private boolean isSpecialActivityComment(int commentType) {
        List<Integer> specialActivityCommentTypes = commentConfig.getPp().getSpecialActivityCommentTypes();
        if (specialActivityCommentTypes.isEmpty()) {
            return false;
        }

        return specialActivityCommentTypes.contains(commentType);
    }
}
