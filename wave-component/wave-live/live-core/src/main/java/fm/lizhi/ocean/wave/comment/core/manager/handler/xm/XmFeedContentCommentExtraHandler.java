package fm.lizhi.ocean.wave.comment.core.manager.handler.xm;

import fm.lizhi.ocean.wave.comment.core.constants.WaveCommentType;
import fm.lizhi.ocean.wave.comment.core.convert.XmSpecialActivityCommentConvert;
import fm.lizhi.ocean.wave.comment.core.manager.handler.SpecialActivityCommentExtraHandler;
import fm.lizhi.ocean.wave.comment.core.model.bean.xm.XmFeedContentCommentExtra;
import fm.lizhi.ocean.wave.comment.core.model.vo.SpecialActivityCommentVO;
import fm.lizhi.ocean.wave.comment.core.remote.bean.FeedContentInfoBean;
import fm.lizhi.ocean.wave.common.util.JsonUtils;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import xm.fm.lizhi.datacenter.comment.pp.bean.SpecialActivityCommentKafkaBean;

/**
 * 作品内容评论
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class XmFeedContentCommentExtraHandler implements SpecialActivityCommentExtraHandler<FeedContentInfoBean> {

    @Override
    public FeedContentInfoBean convertExtraDTO(String json) {
        SpecialActivityCommentKafkaBean specialActivityCommentBean = JsonUtils.fromJsonString(json, SpecialActivityCommentKafkaBean.class);
        xm.fm.lizhi.datacenter.comment.pp.bean.FeedContentInfoBean feedContentInfoBean = specialActivityCommentBean.getCommentInfo().getFeedContentInfoBean();
        if (feedContentInfoBean == null) {
            return null;
        }
        return XmSpecialActivityCommentConvert.I.convertFeedContentInfoToBean(feedContentInfoBean);
    }

    @Override
    public void processActivityCommentExtra(FeedContentInfoBean feedContentInfoBean, SpecialActivityCommentVO comment) {
        if (feedContentInfoBean == null || comment.getCommentType() != this.getCommentType()) {
            return;
        }
        //构建处理点唱玩法的评论信息
        XmFeedContentCommentExtra activityExtra = XmSpecialActivityCommentConvert.I.convertFeedContentCommentToExtra(feedContentInfoBean);
        comment.setActivityExtra(activityExtra);
    }

    @Override
    public int getCommentType() {
        return WaveCommentType.FEED_CONTENT;
    }

    @Override
    public int getAppId() {
        return BusinessEvnEnum.XIMI.getAppId();
    }

}
