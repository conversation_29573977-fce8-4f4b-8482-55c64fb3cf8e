package fm.lizhi.ocean.wave.comment.core.manager.handler.xm;

import fm.lizhi.ocean.wave.comment.core.config.CommentConfig;
import fm.lizhi.ocean.wave.comment.core.constants.WaveCommentType;
import fm.lizhi.ocean.wave.comment.core.convert.XmSpecialActivityCommentConvert;
import fm.lizhi.ocean.wave.comment.core.manager.handler.SpecialActivityCommentExtraHandler;
import fm.lizhi.ocean.wave.comment.core.model.bean.xm.XmFreshUserInterestCommentExtra;
import fm.lizhi.ocean.wave.comment.core.model.vo.SpecialActivityCommentVO;
import fm.lizhi.ocean.wave.comment.core.remote.bean.FreshUserInterestBean;
import fm.lizhi.ocean.wave.common.util.JsonUtils;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import xm.fm.lizhi.datacenter.comment.pp.bean.FreshUserInterestInfoBean;
import xm.fm.lizhi.datacenter.comment.pp.bean.SpecialActivityCommentKafkaBean;

/**
 * 新用户偏好评论处理器
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class XmFreshUserInterestCommentExtraHandler implements SpecialActivityCommentExtraHandler<FreshUserInterestBean> {

    @Autowired
    private CommentConfig commentConfig;

    @Override
    public FreshUserInterestBean convertExtraDTO(String json) {
        SpecialActivityCommentKafkaBean specialActivityCommentBean = JsonUtils.fromJsonString(json, SpecialActivityCommentKafkaBean.class);
        FreshUserInterestInfoBean interestInfoBean = specialActivityCommentBean.getCommentInfo().getFreshUserInterestInfoBean();
        if (interestInfoBean == null) {
            return null;
        }
        return XmSpecialActivityCommentConvert.I.convertFreshUserInterestCommentBean(interestInfoBean);
    }

    @Override
    public void processActivityCommentExtra(FreshUserInterestBean freshUserInterestBean, SpecialActivityCommentVO comment) {
        if (freshUserInterestBean == null || comment.getCommentType() != this.getCommentType()) {
            return;
        }
        //构建处理点唱玩法的评论信息
        XmFreshUserInterestCommentExtra activityExtra = XmSpecialActivityCommentConvert.I.convertFreshUserInterestCommentBizExtra(freshUserInterestBean);
        comment.setActivityExtra(activityExtra);
        comment.setContent(freshUserInterestBean.getTitle() == null ? commentConfig.getXm().getFreshUserInterestCommentTitle() : freshUserInterestBean.getTitle());
    }

    @Override
    public int getCommentType() {
        return WaveCommentType.FRESH_USER_INTEREST;
    }

    @Override
    public int getAppId() {
        return BusinessEvnEnum.XIMI.getAppId();
    }

}
