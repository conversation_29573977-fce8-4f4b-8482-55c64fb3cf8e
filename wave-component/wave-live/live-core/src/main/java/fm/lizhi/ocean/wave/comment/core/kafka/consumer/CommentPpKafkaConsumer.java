package fm.lizhi.ocean.wave.comment.core.kafka.consumer;

import fm.lizhi.common.kafka.common.strategy.AutoOffsetReset;
import fm.lizhi.common.kafka.ioc.api.annotation.KafkaHandler;
import fm.lizhi.common.kafka.ioc.api.annotation.KafkaListener;
import fm.lizhi.common.kafka.ioc.api.annotation.SubscriptionOn;
import fm.lizhi.common.kafka.ioc.api.strategy.annotation.AutoOffsetResetStrategy;
import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.ocean.wave.comment.core.config.CommentConfig;
import fm.lizhi.ocean.wave.comment.core.constants.LiveRoomMsgConstants;
import fm.lizhi.ocean.wave.comment.core.convert.PpSpecialActivityCommentConvert;
import fm.lizhi.ocean.wave.comment.core.manager.CommentPushTaskManger;
import fm.lizhi.ocean.wave.comment.core.manager.SpecialActivityCommentManager;
import fm.lizhi.ocean.wave.comment.core.model.dto.TransientCommentDTO;
import fm.lizhi.ocean.wave.comment.core.model.vo.CommentMsgVo;
import fm.lizhi.ocean.wave.common.util.JsonUtils;
import fm.lizhi.ocean.wave.common.util.KafkaMsgUtils;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wave.server.common.util.ContextUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import pp.fm.lizhi.datacenter.comment.pp.bean.SpecialActivityCommentKafkaBean;

import java.util.List;

/**
 * Description:
 *
 * <AUTHOR>
 * Created in  2023/6/6
 */
@Slf4j
@Component
@KafkaListener(clusterNamespace = "pp-kafka250-bootstrap-server")
@SubscriptionOn(enable = "${pp.kafka.consumer.enable}")
public class CommentPpKafkaConsumer {

    @Autowired
    private CommentPushTaskManger commentPushTaskManger;

    @Autowired
    private CommentConfig commentConfig;

    @Autowired
    private SpecialActivityCommentManager specialActivityCommentManager;

    /**
     * pp评论消息推送
     *
     * @param body
     */
    @KafkaHandler(topic = "lz_topic_pp_comment", group = "lz_ocean_wave_comment_group")
    @AutoOffsetResetStrategy(AutoOffsetReset.Latest)
    public void handleCommentMsg(String body) {
        CommentMsgVo commentMsgVo;
        String msg = null;
        try {
            msg = KafkaMsgUtils.removeKafkaMsgCICDHead(body);
            commentMsgVo = JsonUtil.loads(msg, CommentMsgVo.class);
        } catch (Exception e) {
            log.error("pp.commentMsgVo json parse error, msg:{}, orgMsg:{}", msg, body, e);
            return;
        }

        ContextUtils.setBusinessEvnEnum(BusinessEvnEnum.PP);
        long liveId = commentMsgVo.getLiveId();
        //是否打开评论罗马推送
        if (commentConfig.isCommentRomaPushSwitch()) {
            //推送评论
            boolean res = commentPushTaskManger.saveLiveCommentInfo(BusinessEvnEnum.PP.getAppId(), liveId, LiveRoomMsgConstants.COMMENT_MSG, commentMsgVo.getTime());
            if (!res) {
                // 保存失败，则不提交offset
                throw new RuntimeException("save live comment info fail");
            }
        }
    }

    /**
     * pp评论消息推送
     *
     * @param body 消息体
     */
    @KafkaHandler(topic = "pp_topic_special_activity_comment", group = "lz_ocean_wave_comment_group")
    @AutoOffsetResetStrategy(AutoOffsetReset.Latest)
    public void handleSpecialActivityCommentMsg(String body) {
        String msg = null;
        try {
            msg = KafkaMsgUtils.removeKafkaMsgCICDHead(body);
        } catch (Exception e) {
            log.error("pp.handleSpecialActivityCommentMsg json parse error, msg:{}, orgMsg:{}", msg, body, e);
            return;
        }

        ContextUtils.setBusinessEvnEnum(BusinessEvnEnum.PP);
        //转换为特殊活动评论
        SpecialActivityCommentKafkaBean specialActivityCommentBean = JsonUtils.fromJsonString(msg, SpecialActivityCommentKafkaBean.class);
        if (!isSpecialActivityComment(specialActivityCommentBean.getCommentType())) {
            return;
        }
        TransientCommentDTO commentDTO = PpSpecialActivityCommentConvert.I.convert(specialActivityCommentBean.getCommentInfo(), commentConfig);
        boolean res = specialActivityCommentManager.handleSpecialActivityCommentMsg(specialActivityCommentBean.getLiveId(), msg, commentDTO);
        if (!res) {
            // 保存失败，则不提交offset
            throw new RuntimeException("save live comment info fail");
        }
    }

    /**
     * 是否是互动玩法评论
     *
     * @param commentType 评论类型
     * @return true：是，false：否
     */
    private boolean isSpecialActivityComment(int commentType) {
        List<Integer> specialActivityCommentTypes = commentConfig.getPp().getSpecialActivityCommentTypes();
        if (specialActivityCommentTypes.isEmpty()) {
            return false;
        }

        return specialActivityCommentTypes.contains(commentType);
    }

}
