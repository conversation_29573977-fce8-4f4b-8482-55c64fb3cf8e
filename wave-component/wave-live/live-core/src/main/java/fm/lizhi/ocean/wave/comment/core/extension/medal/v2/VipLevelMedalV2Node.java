package fm.lizhi.ocean.wave.comment.core.extension.medal.v2;

import com.google.common.collect.Lists;
import fm.lizhi.ocean.wave.comment.core.extension.comment.ICommentMedalNodeProcessor;
import fm.lizhi.ocean.wave.comment.core.extension.medal.bean.GenMedalInfoV2Context;
import fm.lizhi.ocean.wave.comment.core.model.vo.BadgeImageVO;
import fm.lizhi.ocean.wave.comment.core.remote.bean.VipInfoEntry;
import fm.lizhi.ocean.wave.comment.core.remote.bean.VipLevel;
import fm.lizhi.ocean.wave.common.extension.ProcessorV2Factory;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;

/**
 * vip等级勋章节点
 *
 * <AUTHOR>
 */
@Component
public class VipLevelMedalV2Node implements ICommentV2MedalNode {

    @Autowired
    private ProcessorV2Factory factory;

    @Override
    public Optional<List<BadgeImageVO>> buildMedalImageInfo(GenMedalInfoV2Context context) {
        int medalShowArea = context.getMedalShowArea();
        String badgeIcon;
        float badgeAspect;
        ICommentMedalNodeProcessor processor = factory.getProcessor(ICommentMedalNodeProcessor.class);
        if (medalShowArea == GenMedalInfoV2Context.MedalShowArea.COMMENT_AREA.getArea()) {
            VipLevel vipLevel = processor.getVipLevel(context.getComment());
            if (vipLevel == null || StringUtils.isBlank(vipLevel.getCover())) {
                return Optional.empty();
            }

            badgeIcon = vipLevel.getCover();
            badgeAspect = vipLevel.getAspect();
        } else {
            VipInfoEntry vipInfoEntry = processor.getVipInfoEntry(context.getEnterNoticeEntry());
            if (vipInfoEntry == null) {
                return Optional.empty();
            }

            badgeIcon = vipInfoEntry.getBadgeIcon();
            badgeAspect = vipInfoEntry.getBadgeAspect();
        }
        BadgeImageVO badgeImageVO = new BadgeImageVO();
        badgeImageVO.setBadgeUrl(badgeIcon);
        badgeImageVO.setBadgeAspect(badgeAspect);
        return Optional.of(Lists.newArrayList(badgeImageVO));
    }
}
