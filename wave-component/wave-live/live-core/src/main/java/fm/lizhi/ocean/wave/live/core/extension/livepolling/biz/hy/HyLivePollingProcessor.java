package fm.lizhi.ocean.wave.live.core.extension.livepolling.biz.hy;

import com.google.common.base.Objects;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.commons.service.client.rcode.GeneralRCode;
import fm.lizhi.live.call.hy.api.CallService;
import fm.lizhi.live.call.hy.protocol.LiveCallProto;
import fm.lizhi.live.cmpt.behavior.msg.anchor.AnchorBehaviorType;
import fm.lizhi.live.cmpt.behavior.msg.device.DeviceBehaviorType;
import fm.lizhi.live.cmpt.behavior.msg.device.RoomType;
import fm.lizhi.live.data.bean.UserBehaviorType;
import fm.lizhi.ocean.wave.api.live.constants.LiveRoomStatusEnum;
import fm.lizhi.ocean.wave.common.extension.consts.BizRouterEnum;
import fm.lizhi.ocean.wave.common.util.ThreadUtils;
import fm.lizhi.ocean.wave.server.common.vo.ResultVO;
import fm.lizhi.ocean.wave.live.core.config.CommonLiveConfig;
import fm.lizhi.ocean.wave.live.core.config.LiveConfig;
import fm.lizhi.ocean.wave.live.core.extension.livepolling.LivePollingProcessor;
import fm.lizhi.ocean.wave.live.core.extension.livepolling.bean.LivePollingPostBean;
import fm.lizhi.ocean.wave.live.core.kafka.producer.biz.hy.HyBehaviorKafkaProducer;
import fm.lizhi.ocean.wave.live.core.kafka.producer.biz.hy.bean.HyAnchorBehaviorBean;
import fm.lizhi.ocean.wave.live.core.kafka.producer.biz.hy.bean.HyDeviceBehaviorBean;
import fm.lizhi.ocean.wave.live.core.kafka.producer.biz.hy.bean.HyUserBehaviorBean;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;

/**
 * <AUTHOR>
 * @date 2023/5/17
 */
@Slf4j
@Component
public class HyLivePollingProcessor implements LivePollingProcessor {

    @Autowired
    private HyBehaviorKafkaProducer hyBehaviorKafkaProducer;
    @Autowired
    private LiveConfig liveConfig;
    @Autowired
    private CallService callService;

    private static final ExecutorService THREAD_POOL_EXECUTOR = ThreadUtils.getTtlExecutors(
            "hyLivePollingBehaviorMsgSender", 4, 8, new LinkedBlockingQueue<Runnable>(1000));


    @Override
    public ResultVO<Void> postprocessor(LivePollingPostBean param) {

        // comeSource=8不成立,先默认处理为false, 如需判断liveLock可通过LiveBean->extraJson->password.
        // boolean slideLockLive = comeSource  == 8 && liveServiceFacade.liveLock(newLive.getId());
        boolean slideLockLive = false;
        if (Objects.equal(LiveRoomStatusEnum.NORMAL.getValue(), param.getLiveRoomBean().getStatus().getValue()) && !slideLockLive) {
            THREAD_POOL_EXECUTOR.submit(() -> {
                //发送设备行为消息
                sendDeviceBehaviorMsg(param);
                //发送主播行为消息
                sendAnchorBehaviroMsg(param);
                //发送用户行为消息
                sendUserBehaviorMsg(param);
            });
        }

        return ResultVO.success();
    }

    private void sendUserBehaviorMsg(LivePollingPostBean param) {
        hyBehaviorKafkaProducer.sendUserBehavior(HyUserBehaviorBean.builder().userId(param.getCurrentUserId())
                .liveId(param.getLiveBean().getId()).ip(param.getIp()).clientVersion(param.getClientVersion())
                .njId(param.getLiveRoomBean().getUserId()).appId(param.getAppId()).deviceId(param.getDeviceId())
                .userBehaviorType(UserBehaviorType.LIVE_LISTEN.getCode())
                .build());
    }

    private void sendAnchorBehaviroMsg(LivePollingPostBean param) {
        //仅房主才发送主播行为消息
        hyBehaviorKafkaProducer.sendAnchorBehavior(HyAnchorBehaviorBean.builder().liveBean(param.getLiveBean())
                .anchorBehaviorType(AnchorBehaviorType.LIVE.getValue()).appId(param.getAppId())
                .userId(param.getCurrentUserId()).njId(param.getLiveRoomBean().getUserId()).build());
    }

    private void sendDeviceBehaviorMsg(LivePollingPostBean param) {
        hyBehaviorKafkaProducer.sendDeviceBehavior(HyDeviceBehaviorBean.builder().userId(param.getCurrentUserId())
                .deviceBehaviorType(DeviceBehaviorType.IN_ROOM.getValue()).deviceId(param.getDeviceId())
                .liveRoomId(param.getLiveRoomBean().getId()).roomType(RoomType.LIVE_ROOM.getValue())
                .liveId(param.getLiveBean().getId()).appId(param.getAppId())
                .njId(param.getLiveRoomBean().getUserId()).build());
    }

    @Override
    public BizRouterEnum getBizRouter() {
        return BizRouterEnum.HY;
    }

    @Override
    public CommonLiveConfig getLiveCommonConfig() {
        return liveConfig.getHy();
    }

    @Override
    public String getActualChannelId(long liveId, String currentChannelId) {
        Result<LiveCallProto.ResponseGetLineCode> getLineCodeResult = callService.getLineCode(liveId);
        if(getLineCodeResult.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
            //获取失败直接置null，抛弃这一次的数据
            log.warn("hy getActualChannelId error, liveId={}`currentChannelId={}`rCode={}", liveId, currentChannelId, getLineCodeResult.rCode());
            return null;
        }
        return getLineCodeResult.target().getLineCode();
    }
}
