package fm.lizhi.ocean.wave.comment.core.remote.adapter.hy;

import fm.lizhi.ocean.wave.comment.core.model.dto.EnterNoticeDTO;
import fm.lizhi.ocean.wave.comment.core.model.dto.HyEnterNoticeExtraDTO;
import fm.lizhi.ocean.wave.comment.core.model.dto.WealthLevelDTO;
import fm.lizhi.ocean.wave.comment.core.remote.bean.*;
import hy.fm.lizhi.live.enternotice.protocol.EnternoticeProto;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * Hy进房公告适配器
 * <AUTHOR>
 */

@Component
public class HyEnterNoticesByRangeAdapter {


    /**
     * 创建进房公告列表
     *
     * @param result 进房公告列表
     * @return 进房公告列表
     */
    public List<EnterNoticeEntry> convertResult(List<EnternoticeProto.EnterNoticeEntry> result) {
        List<EnterNoticeEntry> enterNoticeEntryBeanList = new ArrayList<>();
        for (EnternoticeProto.EnterNoticeEntry enterNoticeEntry : result) {
            // 1. 解析进房公告信息
            EnternoticeProto.WealthInfoEntry wealthInfo = enterNoticeEntry.getWealthInfo();
            EnternoticeProto.UserMountEntry userMountEntry = enterNoticeEntry.getUserMountEntry();
            EnternoticeProto.VipInfoEntry vipInfoEntry = enterNoticeEntry.getVipInfoEntry();
            //是否新用户 1是 0否
            int freshUser = enterNoticeEntry.getFreshUserEntry().getIs() ? 1 : 0;

            // 2.1 创建财富等级信息
            WealthLevelDTO wealthLevelBean = createWealthLevel(wealthInfo);
            // 2.2 创建坐骑信息
            UserMountEntry userMountEntryBean = createUserMountEntry(userMountEntry);
            // 2.3 创建贵族信息
            VipInfoEntry vipInfoEntryBean = createVipInfoEntry(vipInfoEntry);
            // 2.4 创建新用户信息
            FreshUser freshUserBean = createFreshUser(freshUser);

            // 3. 创建进房公告
            EnterNoticeEntry enterNoticeEntryBean = createEnterNoticeEntry(enterNoticeEntry, wealthLevelBean,
                    userMountEntryBean, vipInfoEntryBean, freshUserBean);

            // 4. 添加进房公告
            enterNoticeEntryBeanList.add(enterNoticeEntryBean);
        }

        return enterNoticeEntryBeanList;
    }

    /**
     * 创建进房公告列表
     *
     * @param result 进房公告列表
     * @return 进房公告列表
     */
    public List<EnterNoticeDTO> convertV2Result(List<EnternoticeProto.EnterNoticeEntry> result) {
        List<EnterNoticeDTO> enterNoticeEntryBeanList = new ArrayList<>();
        for (EnternoticeProto.EnterNoticeEntry enterNoticeEntry : result) {
            // 1. 解析进房公告信息
            EnternoticeProto.WealthInfoEntry wealthInfo = enterNoticeEntry.getWealthInfo();
            EnternoticeProto.UserMountEntry userMountEntry = enterNoticeEntry.getUserMountEntry();
            EnternoticeProto.VipInfoEntry vipInfoEntry = enterNoticeEntry.getVipInfoEntry();
            //是否新用户 1是 0否
            int freshUser = enterNoticeEntry.getFreshUserEntry().getIs() ? 1 : 0;

            // 2.1 创建财富等级信息
            WealthLevelDTO wealthLevelBean = createWealthLevel(wealthInfo);
            // 2.2 创建坐骑信息
            UserMountEntry userMountEntryBean = createUserMountEntry(userMountEntry);
            // 2.3 创建贵族信息
            VipInfoEntry vipInfoEntryBean = createVipInfoEntry(vipInfoEntry);
            // 2.4 创建新用户信息
            FreshUser freshUserBean = createFreshUser(freshUser);

            // 3. 创建进房公告
            EnterNoticeDTO enterNoticeEntryBean = createEnterNoticeDTO(enterNoticeEntry, wealthLevelBean,
                    userMountEntryBean, vipInfoEntryBean, freshUserBean);

            // 4. 添加进房公告
            enterNoticeEntryBeanList.add(enterNoticeEntryBean);
        }

        return enterNoticeEntryBeanList;
    }


    /**
     * 创建财富等级信息
     *
     * @param wealthInfo 财富等级信息
     * @return 财富等级
     */
    private WealthLevelDTO createWealthLevel(EnternoticeProto.WealthInfoEntry wealthInfo) {
        WealthLevelDTO wealthLevelBean = new WealthLevelDTO();
        wealthLevelBean.setLevel(wealthInfo.getLevel());
        wealthLevelBean.setCover(wealthInfo.getBageIcon());
        wealthLevelBean.setAspect(wealthInfo.getAspect());
        return wealthLevelBean;
    }

    /**
     * 创建用户座驾信息
     *
     * @param userMountEntry 用户座驾信息
     * @return 用户座驾
     */
    private UserMountEntry createUserMountEntry(EnternoticeProto.UserMountEntry userMountEntry) {
        UserMountEntry userMountEntryBean = new UserMountEntry();
        userMountEntryBean.setLevel(userMountEntry.getLevel());
        userMountEntryBean.setSvgaAniURL(userMountEntry.getSvgaAniURL());
        userMountEntryBean.setText(userMountEntry.getText());
        return userMountEntryBean;
    }

    /**
     * 创建贵族信息
     *
     * @param vipInfoEntry 贵族信息
     * @return 贵族信息
     */
    private VipInfoEntry createVipInfoEntry(EnternoticeProto.VipInfoEntry vipInfoEntry) {
        VipInfoEntry vipInfoEntryBean = new VipInfoEntry();
        vipInfoEntryBean.setVipBackgroundImage(vipInfoEntry.getVipBackgroundImage());
        vipInfoEntryBean.setVipLevel(vipInfoEntry.getVipLevel());
        vipInfoEntryBean.setVipName(vipInfoEntry.getVipName());
        vipInfoEntryBean.setBadgeIcon(vipInfoEntry.getBadgeIcon());
        vipInfoEntryBean.setBadgeAspect(vipInfoEntry.getBadgeAspect());
        return vipInfoEntryBean;
    }

    /**
     * 创建新用户信息
     *
     * @param freshUser 是否新用户 1是 0否
     * @return 新用户信息
     */
    private FreshUser createFreshUser(int freshUser) {
        FreshUser freshUserBean = new FreshUser();
        freshUserBean.setIs(freshUser == 1);
        return freshUserBean;
    }


    /**
     * 创建进房公告信息
     *
     * @param enterNoticeEntry   进房公告信息
     * @param wealthLevelBean    财富等级信息
     * @param userMountEntryBean 用户座驾信息
     * @param vipInfoEntryBean   贵族信息
     * @param freshUserBean      新用户信息
     * @return 进房公告信息
     */
    private EnterNoticeEntry createEnterNoticeEntry(EnternoticeProto.EnterNoticeEntry enterNoticeEntry,
                                                    WealthLevelDTO wealthLevelBean, UserMountEntry userMountEntryBean,
                                                    VipInfoEntry vipInfoEntryBean, FreshUser freshUserBean) {
        EnterNoticeEntry enterNoticeEntryBean = new EnterNoticeEntry();
        enterNoticeEntryBean.setUserId(enterNoticeEntry.getUserId());
        enterNoticeEntryBean.setTimeStamp(enterNoticeEntry.getTimeStamp());
        enterNoticeEntryBean.setAnonymous(enterNoticeEntry.getAnonymous());
        enterNoticeEntryBean.setNotice(enterNoticeEntry.getNotice());
        enterNoticeEntryBean.setCount(enterNoticeEntry.getCount());
        enterNoticeEntryBean.setWealthInfo(wealthLevelBean);
        enterNoticeEntryBean.setUserMountEntry(userMountEntryBean);
        enterNoticeEntryBean.setUserCover(enterNoticeEntry.getUserCover());
        enterNoticeEntryBean.setVipInfoEntry(vipInfoEntryBean);
        enterNoticeEntryBean.setRelationEffectJson(enterNoticeEntry.getRelationEffectJson());
        enterNoticeEntryBean.setFreshUserEntry(freshUserBean);
        enterNoticeEntryBean.setIsShowButton(enterNoticeEntry.getIsShowButton());
        return enterNoticeEntryBean;
    }

    /**
     * 创建进房公告信息
     *
     * @param enterNoticeEntry   进房公告信息
     * @param wealthLevelBean    财富等级信息
     * @param userMountEntryBean 用户座驾信息
     * @param vipInfoEntryBean   贵族信息
     * @param freshUserBean      新用户信息
     * @return 进房公告信息
     */
    private EnterNoticeDTO createEnterNoticeDTO(EnternoticeProto.EnterNoticeEntry enterNoticeEntry,
                                                WealthLevelDTO wealthLevelBean, UserMountEntry userMountEntryBean,
                                                VipInfoEntry vipInfoEntryBean, FreshUser freshUserBean) {
        EnterNoticeDTO noticeDTO = new EnterNoticeDTO();
        noticeDTO.setUserId(enterNoticeEntry.getUserId());
        noticeDTO.setTimeStamp(enterNoticeEntry.getTimeStamp());
        noticeDTO.setAnonymous(enterNoticeEntry.getAnonymous());
        noticeDTO.setNotice(enterNoticeEntry.getNotice());
        noticeDTO.setCount(enterNoticeEntry.getCount());
        noticeDTO.setWealthInfo(wealthLevelBean);
        noticeDTO.setUserMountEntry(userMountEntryBean);
        noticeDTO.setUserCover(enterNoticeEntry.getUserCover());
        noticeDTO.setFreshUserEntry(freshUserBean);

        HyEnterNoticeExtraDTO hyEnterNoticeExtraDTO = new HyEnterNoticeExtraDTO();
        hyEnterNoticeExtraDTO.setVipInfoEntry(vipInfoEntryBean);
        hyEnterNoticeExtraDTO.setShowButton(enterNoticeEntry.getIsShowButton());
        noticeDTO.setHyEnterNoticeExtraDTO(hyEnterNoticeExtraDTO);
        return noticeDTO;
    }
}
