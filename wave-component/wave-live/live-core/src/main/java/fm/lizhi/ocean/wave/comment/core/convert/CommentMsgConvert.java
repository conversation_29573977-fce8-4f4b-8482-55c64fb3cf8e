package fm.lizhi.ocean.wave.comment.core.convert;

import fm.lizhi.ocean.wave.comment.core.model.dto.TransientCommentDTO;
import fm.lizhi.ocean.wave.comment.core.model.dto.WealthLevelDTO;
import fm.lizhi.ocean.wave.comment.core.model.vo.*;
import fm.lizhi.ocean.wave.comment.core.remote.bean.*;
import fm.lizhi.ocean.wave.user.result.*;
import org.apache.commons.collections.CollectionUtils;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Mapper(collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        /* 未映射目标属性的报告策略 */
        unmappedTargetPolicy = ReportingPolicy.ERROR,
        /* 确定何时对bean映射的源属性值包含空检查 */
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface CommentMsgConvert {

    CommentMsgConvert I = Mappers.getMapper(CommentMsgConvert.class);

    /**
     * 转换特殊活动评论
     *
     * @param commentVO 评论结果
     * @return 特殊活动评论结果
     */

    List<SpecialActivityCommentVO> convertCommentList(List<CommentVO> commentVO);

    @Mapping(target = "activityExtra", ignore = true)
    SpecialActivityCommentVO convert(CommentVO commentVO);

    /**
     * 转换勋章列表
     *
     * @param medalResultList 勋章结果列表
     * @return 转换结果
     */
    List<Medal> convertMedalList(List<MedalResult> medalResultList);

    /**
     * 转换财富等级
     *
     * @param wealth 财富等级
     * @return 转换结果
     */
    WealthLevelDTO convertWealthLevel(WealthLevelResult wealth);

    /**
     * 转换新用户信息
     *
     * @param freshUser 新用户信息
     * @return 结果
     */
    FreshUser convertFreshUser(FreshUserResult freshUser);

    /**
     * 转换成长等级
     *
     * @param showMealRelationResult 成长等级
     * @return 转换结果
     */
    @Mapping(target = "levelId", source = "level")
    GrowRelationLevel convertGrowRelationLevel(ShowMealRelationResult showMealRelationResult);

    /**
     * 转换贵族勋章
     *
     * @param vipMedalResult 贵族勋章
     * @return 转换结果
     */
    @Mapping(target = "image", source = "icon")
    @Mapping(target = "priority", ignore = true)
    VipMedal convertVipMedal(VipPrivilegeMedalResult vipMedalResult);

    /**
     * 构建贵族等级
     *
     * @param vipLevelResult 贵族等级
     * @return 贵族等级VO
     */
    @Mapping(target = "cover", source = "icon")
    VipLevel convertVipLevel(VipPrivilegeMedalResult vipLevelResult);

    /**
     * 构建简单勋章列表
     *
     * @param simpleMedalList 简单勋章列表
     * @return 转换结果
     */
    List<SimpleMedal> convertSimpleMedalList(List<MedalGroupResult> simpleMedalList);

    /**
     * 构建简单勋章
     *
     * @param simpleMedal 简单勋章
     * @return 结果
     */
    @Mapping(target = "longImage", source = "longMedalImageUrl")
    @Mapping(target = "image", source = "medalImageUrl")
    @Mapping(target = "priority", ignore = true)
    SimpleMedal convertSimpleMedal(MedalGroupResult simpleMedal);

    /**
     * 构建主播等级
     *
     * @param levelMarkVO 主播等级
     * @return 转换结果
     */
    AnthorLevel convertAnthorLevel(LevelMarkVO levelMarkVO);

    /**
     * 构建图片信息
     *
     * @param transientComment 原始评论信息
     * @param url              图片地址
     * @return 图片VO
     */
    @Mapping(target = "imageUrl", source = "url")
    ImageVO buildImageVO(TransientCommentDTO transientComment, String url);

    /**
     * 构建基础评论
     *
     * @param transientComment 原始评论信息
     * @return 评论VO
     */
    default CommentVO builfBaseCommentVO(TransientCommentDTO transientComment) {
        CommentVO commentVO = new CommentVO();
        commentVO.setContent(transientComment.getContent());
        commentVO.setBubbleEffectId(transientComment.getStyleId());
        commentVO.setCommentId(String.valueOf(transientComment.getCommentId()));
        commentVO.setCommentType(transientComment.getCommentType());
        commentVO.setTimeStamp(transientComment.getTime());
        commentVO.setAndroidColor(transientComment.getAndroidColor());
        commentVO.setIosColor(transientComment.getIosColor());
        if (CollectionUtils.isNotEmpty(transientComment.getToUser())) {
            List<String> toUserIds = transientComment.getToUser().stream().map(String::valueOf).collect(Collectors.toList());
            commentVO.setToUserIds(toUserIds);
        }
        return commentVO;
    }
}
