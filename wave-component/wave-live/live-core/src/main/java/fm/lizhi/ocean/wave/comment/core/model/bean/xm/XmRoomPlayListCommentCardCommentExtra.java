package fm.lizhi.ocean.wave.comment.core.model.bean.xm;

import fm.lizhi.ocean.wave.comment.core.model.bean.RoomPlayListCommentCardCommentExtra;
import fm.lizhi.ocean.wave.comment.core.remote.bean.RoomPlaylistCommentCardBean;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 西米直播间精选评论
 *
 * <AUTHOR>
 */
@Data
public class XmRoomPlayListCommentCardCommentExtra implements RoomPlayListCommentCardCommentExtra {
    /**
     * 消息卡片标题
     */
    private String title;

    /**
     * 消息卡片副标题
     */
    private String subTitle;

    /**
     * 消息卡片按钮文案
     */
    private String buttonTitle;

    /**
     * 推荐歌曲列表
     */
    private List<RoomPlaylistCommentCardBean.RoomSongInfo> songList;

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class RoomSongInfo {
        /**
         * 歌曲名字
         */
        private String songName;

        /**
         * 歌曲封面
         */
        private String poster;
    }
}
