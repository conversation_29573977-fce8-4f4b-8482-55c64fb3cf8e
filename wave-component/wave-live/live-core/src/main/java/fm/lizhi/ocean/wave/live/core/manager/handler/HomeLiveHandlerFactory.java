package fm.lizhi.ocean.wave.live.core.manager.handler;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 评论类型处理器工厂
 *
 * <AUTHOR>
 * @date 2023/7/21 3:34 下午
 */
@Component
public class HomeLiveHandlerFactory {

    @Autowired
    private List<IHomeLiveHandler> handlers;

    /**
     * 获取处理类
     *
     * @param tabType 首页tab类型
     * @return 处理器
     */
    public IHomeLiveHandler getHandler(int tabType) {
        for (IHomeLiveHandler handler : handlers) {
            if (handler.getTabType() == tabType) {
                return handler;
            }
        }
        throw new RuntimeException("首页TAB类型未找到，请联系管理员！");
    }
}
