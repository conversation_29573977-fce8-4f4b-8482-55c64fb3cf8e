package fm.lizhi.ocean.wave.comment.core.model.vo;

import fm.lizhi.ocean.wave.comment.core.model.bean.MsgUserExtraInfo;
import lombok.Data;

import java.util.List;

/**
 * 西米评论用户额外信息
 *
 * <AUTHOR>
 */
@Data
public class XmMsgUserExtraInfo implements MsgUserExtraInfo {

    /**
     * 贵宾卡标识（西米独有）
     */
    private List<String> roomVipUrls;

    /**
     * 贵宾 昵称的颜色 （西米独有）
     */
    private List<Long> nameColorsList;

    /**
     * ximi独有
     * MONTH_ROOM_VIP(1, "月贵宾"),
     * YEAR_ROOM_VIP(2, "年贵宾"),
     */
    private Integer userRoomVipStatus;
}
