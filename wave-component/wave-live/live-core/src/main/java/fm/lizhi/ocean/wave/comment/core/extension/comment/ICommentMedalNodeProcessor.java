package fm.lizhi.ocean.wave.comment.core.extension.comment;

import fm.lizhi.ocean.wave.comment.core.model.dto.EnterNoticeDTO;
import fm.lizhi.ocean.wave.comment.core.model.dto.TransientCommentDTO;
import fm.lizhi.ocean.wave.comment.core.remote.bean.SimpleMedal;
import fm.lizhi.ocean.wave.comment.core.remote.bean.VipInfoEntry;
import fm.lizhi.ocean.wave.comment.core.remote.bean.VipLevel;
import fm.lizhi.ocean.wave.common.extension.BusinessEnvAwareProcessor;

import java.util.List;

/**
 * 评论勋章处理器
 *
 * <AUTHOR>
 */
public interface ICommentMedalNodeProcessor extends BusinessEnvAwareProcessor {

    @Override
    default Class<? extends BusinessEnvAwareProcessor> getBaseBusinessProcessor() {
        return ICommentMedalNodeProcessor.class;
    }

    /**
     * 获取穿戴的勋章列表
     *
     * @param transientCommentDTO 评论信息
     * @return 结果
     */
    List<SimpleMedal> getWearMedalList(TransientCommentDTO transientCommentDTO);

    /**
     * 获取vip等级
     *
     * @param transientCommentDTO 评论信息
     * @return 等级信息
     */
    VipLevel getVipLevel(TransientCommentDTO transientCommentDTO);

    /**
     * 获取vip等级信息
     *
     * @param enterNoticeEntry 进房消息
     * @return vip等级信息
     */
    VipInfoEntry getVipInfoEntry(EnterNoticeDTO enterNoticeEntry);

}
