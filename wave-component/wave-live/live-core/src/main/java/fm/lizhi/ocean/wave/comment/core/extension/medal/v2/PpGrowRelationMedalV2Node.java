package fm.lizhi.ocean.wave.comment.core.extension.medal.v2;

import com.google.common.collect.Lists;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wave.comment.core.extension.medal.bean.GenMedalInfoV2Context;
import fm.lizhi.ocean.wave.comment.core.model.dto.PpCommentExtraDTO;
import fm.lizhi.ocean.wave.comment.core.model.dto.TransientCommentDTO;
import fm.lizhi.ocean.wave.comment.core.model.vo.BadgeImageVO;
import fm.lizhi.ocean.wave.comment.core.remote.bean.GrowRelationLevel;
import fm.lizhi.ocean.wave.server.common.util.ContextUtils;
import fm.lizhi.ocean.wave.user.api.GrowRelationLevelService;
import fm.lizhi.ocean.wave.user.result.GrowRelationLevelResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 成长关系等级勋章节点
 * <AUTHOR>
 */
@Component
public class PpGrowRelationMedalV2Node implements ICommentV2MedalNode {

    @Autowired
    private GrowRelationLevelService growRelationLevelService;

    @Override
    public Optional<List<BadgeImageVO>> buildMedalImageInfo(GenMedalInfoV2Context context) {
        //只有评论才有成长关系等级勋章
        TransientCommentDTO comment = context.getComment();
        if (comment == null) {
            return Optional.empty();
        }

        PpCommentExtraDTO ppCommentExtra = comment.getPpCommentExtra();
        //选出成长关系信息
        GrowRelationLevel relationLevel = genGrowRelationLevel(ppCommentExtra.getGrowRelationLevelList(), ContextUtils.getContext().getUserId());
        if (relationLevel == null) {
            return Optional.empty();
        }

        BadgeImageVO badgeImageVO = new BadgeImageVO();
        // 尝试获取
        GrowRelationLevel growRelationLevel = ppCommentExtra.getGrowRelationLevel();
        relationLevel = growRelationLevel == null ? relationLevel : growRelationLevel;
        Result<GrowRelationLevelResult> result = growRelationLevelService.getUserGrowRelationByCache(relationLevel.getLevelId());
        if (result.rCode() != 0) {
            return Optional.empty();
        }

        //将关系列表置空
        ppCommentExtra.setGrowRelationLevelList(null);
        ppCommentExtra.setGrowRelationLevel(relationLevel);

        // 获取成长等级配置
        GrowRelationLevelResult levelInfo = result.target();
        badgeImageVO.setBadgeUrl(levelInfo.getMedalUrl());
        badgeImageVO.setBadgeAspect(Double.valueOf(levelInfo.getMedalAspect()).floatValue());
        return Optional.of(Lists.newArrayList(badgeImageVO));
    }

    /**
     * 生成成长关系等级信息
     *
     * @param growRelationLevelList 成长关系等级信息列表
     * @param userId                当前用户ID
     * @return 生成成长关系等级信息
     */
    private GrowRelationLevel genGrowRelationLevel(List<GrowRelationLevel> growRelationLevelList, long userId) {
        Map<Long, GrowRelationLevel> growRelationLevelMap =
                growRelationLevelList.stream()
                        .collect(Collectors.toMap(GrowRelationLevel::getRecUserId, item -> item));
        // 尝试获取
        GrowRelationLevel growRelationLevel = growRelationLevelMap.get(userId);
        if (growRelationLevel == null) {
            return null;
        }

        GrowRelationLevel growRelationLevelBean = new GrowRelationLevel();
        growRelationLevelBean.setLevelId(growRelationLevel.getLevelId());
        growRelationLevelBean.setRecUserId(growRelationLevel.getRecUserId());
        return growRelationLevelBean;
    }
}
