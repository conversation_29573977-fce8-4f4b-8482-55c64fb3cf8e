package fm.lizhi.ocean.wave.comment.core.manager;

import fm.lizhi.ocean.wave.comment.core.constants.WaveCommentType;
import fm.lizhi.ocean.wave.comment.core.manager.filter.CommentV2Filter;
import fm.lizhi.ocean.wave.comment.core.model.dto.TransientCommentDTO;

import java.util.ArrayList;
import java.util.List;

/**
 * 评论财富登记过滤器
 *
 * <AUTHOR>
 */
public class CommentWealthLevelFilter implements CommentV2Filter {

    private final Integer wealthLevel;

    public CommentWealthLevelFilter(Integer wealthLevel) {
        this.wealthLevel = wealthLevel;
    }


    @Override
    public List<TransientCommentDTO> filter(List<TransientCommentDTO> msg) {
        List<TransientCommentDTO> result = new ArrayList<>();
        if (wealthLevel == null || wealthLevel <= 0) {
            return msg;
        }

        for (TransientCommentDTO comment : msg) {
            if (comment.getCommentType() != WaveCommentType.CHANNEL_ENTER_COMMENT) {
                //不是渠道进房评论，不参与过滤
                result.add(comment);
            }
            if (comment.getWealthLevel() == null) {
                continue;
            }
            if (comment.getWealthLevel().getLevel() >= wealthLevel) {
                result.add(comment);
            }
        }
        return result;
    }
}
