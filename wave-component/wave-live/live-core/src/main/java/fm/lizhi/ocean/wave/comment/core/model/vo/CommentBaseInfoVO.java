package fm.lizhi.ocean.wave.comment.core.model.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import fm.lizhi.ocean.wave.comment.core.model.bean.ICommentBean;
import fm.lizhi.ocean.wave.comment.core.model.bean.MsgUserExtraInfo;
import lombok.Data;

import java.util.List;

/**
 * 直播间评论VO
 *
 * <AUTHOR>
 */
@Data
public class CommentBaseInfoVO implements ICommentBean {
    /**
     * 评论Id
     */
    private String commentId;
    /**
     * 用户信息
     */
    private MsgUserVO<? extends MsgUserExtraInfo> user;
    /**
     * 评论内容
     */
    private String content;
    /**
     * 评论类型
     */
    private Integer commentType;
    /**
     * 图片信息
     */
    private ImageVO image;
    /**
     * 表情信息
     */
    private EmotionVO emotion;

    /**
     * 评论时间戳
     */
    private Long timeStamp;

    /**
     * 气泡样式id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private long bubbleEffectId;

    /**
     * ios颜色
     */
    private String iosColor;
    /**
     * android颜色
     */
    private String androidColor;

    /**
     * 可见用户ID
     */
    private List<String> toUserIds;
}
