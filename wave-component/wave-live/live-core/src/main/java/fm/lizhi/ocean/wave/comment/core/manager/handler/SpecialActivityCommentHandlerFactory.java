package fm.lizhi.ocean.wave.comment.core.manager.handler;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 互动玩法评论处理器工厂
 *
 * <AUTHOR>
 */
@Component
public class SpecialActivityCommentHandlerFactory {

    @Autowired
    private List<SpecialActivityCommentExtraHandler<?>> handlers;

    /**
     * 获取处理类
     *
     * @param commentType 评论类型
     * @param <T>         返回的处理器类型
     * @return 处理器
     */
    public <T extends SpecialActivityCommentExtraHandler<?>> T getHandler(int commentType, int appId) {
        for (SpecialActivityCommentExtraHandler<?> handler : handlers) {
            if (handler.getCommentType() == commentType && handler.getAppId() == appId) {
                //noinspection unchecked
                return (T) handler;
            }
        }
        return null;
    }
}
