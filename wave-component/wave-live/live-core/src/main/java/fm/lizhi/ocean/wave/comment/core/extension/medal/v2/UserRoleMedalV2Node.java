package fm.lizhi.ocean.wave.comment.core.extension.medal.v2;

import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.commons.service.client.rcode.GeneralRCode;
import fm.lizhi.ocean.wave.api.live.api.LiveRoomRoleService;
import fm.lizhi.ocean.wave.api.live.bean.LiveRoomUserRole;
import fm.lizhi.ocean.wave.comment.core.config.CommentCommonConfig;
import fm.lizhi.ocean.wave.comment.core.extension.medal.ICommentMedalNode;
import fm.lizhi.ocean.wave.comment.core.extension.medal.bean.GenMedalInfoContext;
import fm.lizhi.ocean.wave.comment.core.extension.medal.bean.GenMedalInfoV2Context;
import fm.lizhi.ocean.wave.comment.core.extension.role.IUserRoleMedalProcessor;
import fm.lizhi.ocean.wave.comment.core.model.dto.TransientCommentDTO;
import fm.lizhi.ocean.wave.comment.core.model.vo.BadgeImageVO;
import fm.lizhi.ocean.wave.comment.core.remote.bean.TransientComment;
import fm.lizhi.ocean.wave.common.extension.ProcessorV2Factory;
import fm.lizhi.ocean.wave.live.core.remote.bean.LiveBean;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 用户角色勋章节点
 * <AUTHOR>
 */
@Slf4j
@Component
public class UserRoleMedalV2Node implements ICommentV2MedalNode {

    @Autowired
    private LiveRoomRoleService liveRoomRoleService;

    @Autowired
    private ProcessorV2Factory factory;

    // 房主
    private static final int USER_ROLE_TYPE_ROOM = 1;

    // 管理员
    private static final int USER_ROLE_TYPE_MANAGER = 2;

    private static final int USER_ROLE_TYPE_SUPER_MANAGER = 10;

    @Override
    public Optional<List<BadgeImageVO>> buildMedalImageInfo(GenMedalInfoV2Context context) {
        LiveBean live = context.getLiveBean();
        TransientCommentDTO comment = context.getComment();
        CommentCommonConfig commonConfig = context.getCommentConfig();

        // 角色勋章列表
        List<BadgeImageVO> medalList = new ArrayList<>();
        List<LiveRoomUserRole> liveRoomRoles = getLiveRoomUserAllRole(live, comment);

        IUserRoleMedalProcessor processor = factory.getProcessor(IUserRoleMedalProcessor.class);
        // 过滤不需要展示的角色
        liveRoomRoles = processor.filterLiveRoomRoles(liveRoomRoles);

        for (LiveRoomUserRole liveRoomRole : liveRoomRoles) {

            if (liveRoomRole.getRole() == USER_ROLE_TYPE_SUPER_MANAGER) {
                BadgeImageVO badgeImageVO = new BadgeImageVO();
                badgeImageVO.setBadgeUrl(commonConfig.getRoomSuperManagerIconUrl());
                badgeImageVO.setBadgeAspect(commonConfig.getUserSuperManagerRoleImageBadgeAspect());
                medalList.add(badgeImageVO);
            }

            if (liveRoomRole.getRole() == USER_ROLE_TYPE_MANAGER) {
                BadgeImageVO badgeImageVO = new BadgeImageVO();
                badgeImageVO.setBadgeUrl(commonConfig.getRoomManagerIconUrl());
                badgeImageVO.setBadgeAspect(commonConfig.getUserRoleImageBadgeAspect());
                medalList.add(badgeImageVO);
            }

            if (liveRoomRole.getRole() == USER_ROLE_TYPE_ROOM) {
                BadgeImageVO badgeImageVO = new BadgeImageVO();
                badgeImageVO.setBadgeUrl(commonConfig.getRoomOwnerIconUrl());
                badgeImageVO.setBadgeAspect(commonConfig.getUserRoleImageBadgeAspect());
                medalList.add(badgeImageVO);
            }
        }
        return Optional.of(medalList);
    }

    /**
     * 获取用户在直播间的所有角色
     *
     * @param live             直播间信息
     * @param transientComment 评论信息
     * @return 用户在直播间的所有角色
     */
    private List<LiveRoomUserRole> getLiveRoomUserAllRole(LiveBean live, TransientCommentDTO transientComment) {
        Result<List<LiveRoomUserRole>> result = liveRoomRoleService
                .getLiveRoomUserAllRoleFromCache(live.getLiveRoomId(), transientComment.getUserId());
        if (result.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
            log.warn("getLiveRoomUserAllRole error, rCode:{},liveRoomId:{},userId:{}", result.rCode(),
                    live.getLiveRoomId(), transientComment.getUserId());
            return new ArrayList<>();
        }
        return result.target();
    }
}
