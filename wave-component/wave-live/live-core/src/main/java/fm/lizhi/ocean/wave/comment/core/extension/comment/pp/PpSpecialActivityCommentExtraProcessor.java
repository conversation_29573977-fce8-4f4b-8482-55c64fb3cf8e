package fm.lizhi.ocean.wave.comment.core.extension.comment.pp;

import fm.lizhi.ocean.wave.comment.core.extension.comment.ISpecialActivityCommentExtraProcessor;
import fm.lizhi.ocean.wave.comment.core.model.bean.FreshUserInterestCommentExtra;
import fm.lizhi.ocean.wave.comment.core.model.bean.RoomPlayListCommentCardCommentExtra;
import fm.lizhi.ocean.wave.comment.core.model.bean.RoomRouletteWheelCommentExtra;
import fm.lizhi.ocean.wave.comment.core.model.bean.xm.XmSongSpecialActivityCommentExtra;
import fm.lizhi.ocean.wave.comment.core.remote.bean.FreshUserInterestBean;
import fm.lizhi.ocean.wave.comment.core.remote.bean.RoomPlaylistCommentCardBean;
import fm.lizhi.ocean.wave.comment.core.remote.bean.RoomRouletteWheelCommentCardBean;
import fm.lizhi.ocean.wave.comment.core.remote.bean.SongInfo;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import org.springframework.stereotype.Component;

/**
 * 互动活动评论差异化信息处理器
 *
 * <AUTHOR>
 */
@Component
public class PpSpecialActivityCommentExtraProcessor implements ISpecialActivityCommentExtraProcessor {

    @Override
    public SongInfo convertSongInfo(String json) {
        return null;
    }

    @Override
    public RoomPlaylistCommentCardBean convertRoomPlaylistCommentCardBean(String json) {
        return null;
    }

    @Override
    public XmSongSpecialActivityCommentExtra buildSongCommentBizExtra(SongInfo songCommentBean) {
        return null;
    }

    @Override
    public RoomPlayListCommentCardCommentExtra buildRoomPlayListCommentBizExtra(RoomPlaylistCommentCardBean cardBean) {
        return null;
    }

    @Override
    public RoomRouletteWheelCommentCardBean convertRoomRouletteWheelCommentBean(String json) {
        return null;
    }

    @Override
    public RoomRouletteWheelCommentExtra buildRoomRouletteWheelCommentExtra(RoomRouletteWheelCommentCardBean cardBean) {
        return null;
    }

    @Override
    public FreshUserInterestBean convertFreshUserInterestCommentBean(String json) {
        return null;
    }

    @Override
    public FreshUserInterestCommentExtra buildFreshUserInterestCommentExtra(FreshUserInterestBean cardBean) {
        return null;
    }

    @Override
    public BusinessEvnEnum getBusinessEnv() {
        return BusinessEvnEnum.PP;
    }
}
