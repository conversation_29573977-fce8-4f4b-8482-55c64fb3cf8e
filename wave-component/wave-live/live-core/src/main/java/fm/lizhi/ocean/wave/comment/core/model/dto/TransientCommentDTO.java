package fm.lizhi.ocean.wave.comment.core.model.dto;

import java.util.List;

import fm.lizhi.ocean.wave.comment.core.remote.bean.FreshUser;
import fm.lizhi.ocean.wave.comment.core.remote.bean.Medal;
import fm.lizhi.ocean.wave.comment.core.remote.bean.SimpleUser;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 评论基本字段
 *
 * <AUTHOR>
 */
@Data
public class TransientCommentDTO {
    /**
     * 评论id
     */
    private long commentId;
    /**
     * 评论用户id
     */
    private long userId;

    /**
     * 触发评论场景的用户ID
     */
    private long operatorUserId;

    /**
     * 评论内容
     */
    private String content;
    /**
     * 评论时间
     */
    private long time;
    /**
     * ios颜色
     */
    private String iosColor;
    /**
     * android颜色
     */
    private String androidColor;

    /**
     * 评论类型
     */
    private int commentType;
    /**
     * 评论类型扩展
     */
    private int commentTypeExtension;
    /**
     * 图片高度
     */
    private int imageHeight;
    /**
     * 图片宽度
     */
    private int imageWidth;
    /**
     * 图片大小
     */
    private int imageSize;
    /**
     * 图片url
     */
    private String imageUrl;
    /**
     * 图片宽高比
     */
    private float aspectRatio;
    /**
     * 是否原图
     */
    private boolean imageOriginal;

    /**
     * 表情id
     */
    private long emotionId;
    /**
     * 停止图片序号
     */
    private int repeatStopImageIndex;
    /**
     * 指定发送用户
     */
    private List<Long> toUser;
    /**
     * 评论样式ID
     */
    private long styleId;
    /**
     * 子评论类型
     */
    private int bizType;

    /**
     * 财富等级
     */
    private WealthLevelDTO wealthLevel;

    /**
     * 新用户
     */
    private FreshUser freshUser;

    /**
     * 用户信息
     */
    private SimpleUser simpleUser;

    /**
     * 用户勋章列表（拥有的）
     */
    private List<Medal> medal;

    /**
     * PP专属评论属性
     */
    private PpCommentExtraDTO ppCommentExtra;

    /**
     * 西米专属评论属性
     */
    private XmCommentExtraDTO xmCommentExtra;

    /**
     * 黑夜专属评论属性
     */
    private HyCommentExtraDTO hyCommentExtra;


}
