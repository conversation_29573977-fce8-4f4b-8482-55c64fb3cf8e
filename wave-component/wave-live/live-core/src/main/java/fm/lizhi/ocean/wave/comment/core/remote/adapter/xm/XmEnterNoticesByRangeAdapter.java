package fm.lizhi.ocean.wave.comment.core.remote.adapter.xm;

import fm.lizhi.ocean.wave.comment.core.model.dto.EnterNoticeDTO;
import fm.lizhi.ocean.wave.comment.core.model.dto.WealthLevelDTO;
import fm.lizhi.ocean.wave.comment.core.model.dto.XmEnterNoticeExtraDTO;
import fm.lizhi.ocean.wave.comment.core.remote.bean.EnterNoticeEntry;
import fm.lizhi.ocean.wave.comment.core.remote.bean.FreshUser;
import fm.lizhi.ocean.wave.comment.core.remote.bean.UserMountEntry;
import fm.lizhi.ocean.wave.comment.core.remote.bean.VipInfoEntry;
import org.springframework.stereotype.Component;
import xm.fm.lizhi.live.enternotice.protocol.EnternoticeProto;

import java.util.ArrayList;
import java.util.List;

/**
 * xm进房公告适配器
 * <AUTHOR>
 */
@Component
public class XmEnterNoticesByRangeAdapter {

    /**
     * 创建进房公告列表
     *
     * @param result 进房公告列表
     * @return 进房公告列表
     */
    public List<EnterNoticeEntry> convertResult(List<EnternoticeProto.EnterNoticeEntry> result,long userId) {
        List<EnterNoticeEntry> enterNoticeEntryBeanList = new ArrayList<>();
        for (EnternoticeProto.EnterNoticeEntry enterNoticeEntry : result) {
            // 1. 解析进房公告信息
            EnternoticeProto.WealthInfoEntry wealthInfo = enterNoticeEntry.getWealthInfo();
            EnternoticeProto.UserMountEntry userMountEntry = enterNoticeEntry.getUserMountEntry();
            EnternoticeProto.VipInfoEntry vipInfoEntry = enterNoticeEntry.getVipInfoEntry();
            //是否新用户 1是 0否
            int freshUser = enterNoticeEntry.getFreshUSer();

            // 2.1 创建财富等级信息
            WealthLevelDTO wealthLevelBean = createWealthLevel(wealthInfo);
            // 2.2 创建坐骑信息
            UserMountEntry userMountEntryBean = createUserMountEntry(userMountEntry);
            // 2.3 创建贵族信息
            VipInfoEntry vipInfoEntryBean = createVipInfoEntry(vipInfoEntry);
            // 2.4 创建新用户信息
            FreshUser freshUserBean = createFreshUser(freshUser);

            // 3. 创建进房公告
            EnterNoticeEntry enterNoticeEntryBean = createEnterNoticeEntry(enterNoticeEntry, wealthLevelBean,
                    userMountEntryBean, vipInfoEntryBean, freshUserBean);

            if (enterNoticeEntry.getUserId() != userId && enterNoticeEntry.getPlayerIdListCount() > 0) {
                boolean showGreetButton = enterNoticeEntry.getPlayerIdListList().contains(userId);
                enterNoticeEntryBean.setIsShowButton(showGreetButton);
            }

            if (enterNoticeEntry.getAnonymous() == 0) {
                enterNoticeEntryBean.setUserRoomVipStatus(enterNoticeEntry.getUserRoomVipStatus());
            }
            // 4. 添加进房公告
            enterNoticeEntryBeanList.add(enterNoticeEntryBean);
        }

        return enterNoticeEntryBeanList;
    }

    public List<EnterNoticeDTO> convertV2Result(List<EnternoticeProto.EnterNoticeEntry> result, long userId) {
        List<EnterNoticeDTO> enterNoticeEntryBeanList = new ArrayList<>();
        for (EnternoticeProto.EnterNoticeEntry enterNoticeEntry : result) {
            // 1. 解析进房公告信息
            EnternoticeProto.WealthInfoEntry wealthInfo = enterNoticeEntry.getWealthInfo();
            EnternoticeProto.UserMountEntry userMountEntry = enterNoticeEntry.getUserMountEntry();
            EnternoticeProto.VipInfoEntry vipInfoEntry = enterNoticeEntry.getVipInfoEntry();
            //是否新用户 1是 0否
            int freshUser = enterNoticeEntry.getFreshUSer();

            // 2.1 创建财富等级信息
            WealthLevelDTO wealthLevelBean = createWealthLevel(wealthInfo);
            // 2.2 创建坐骑信息
            UserMountEntry userMountEntryBean = createUserMountEntry(userMountEntry);
            // 2.3 创建贵族信息
            VipInfoEntry vipInfoEntryBean = createVipInfoEntry(vipInfoEntry);
            // 2.4 创建新用户信息
            FreshUser freshUserBean = createFreshUser(freshUser);

            // 3. 创建进房公告
            EnterNoticeDTO enterNoticeEntryBean = createEnterNoticeDTO(enterNoticeEntry, wealthLevelBean,
                    userMountEntryBean, vipInfoEntryBean, freshUserBean);

            XmEnterNoticeExtraDTO extraDTO = new XmEnterNoticeExtraDTO();
            if (enterNoticeEntry.getAnonymous() == 0) {
                extraDTO.setUserRoomVipStatus(enterNoticeEntry.getUserRoomVipStatus());
            }
            enterNoticeEntryBean.setXmEnterNoticeExtraDTO(extraDTO);
            // 4. 添加进房公告
            enterNoticeEntryBeanList.add(enterNoticeEntryBean);
        }

        return enterNoticeEntryBeanList;
    }


    /**
     * 创建财富等级信息
     *
     * @param wealthInfo 财富等级信息
     * @return 财富等级
     */
    private static WealthLevelDTO createWealthLevel(EnternoticeProto.WealthInfoEntry wealthInfo) {
        WealthLevelDTO wealthLevelBean = new WealthLevelDTO();
        wealthLevelBean.setLevel(wealthInfo.getLevel());
        wealthLevelBean.setCover(wealthInfo.getBageIcon());
        wealthLevelBean.setAspect(wealthInfo.getAspect());
        return wealthLevelBean;
    }

    /**
     * 创建用户座驾信息
     *
     * @param userMountEntry 用户座驾信息
     * @return 用户座驾
     */
    private static UserMountEntry createUserMountEntry(EnternoticeProto.UserMountEntry userMountEntry) {
        UserMountEntry userMountEntryBean = new UserMountEntry();
        userMountEntryBean.setLevel(userMountEntry.getLevel());
        userMountEntryBean.setSvgaAniURL(userMountEntry.getSvgaAniURL());
        userMountEntryBean.setText(userMountEntry.getText());
        return userMountEntryBean;
    }

    /**
     * 创建贵族信息
     *
     * @param vipInfoEntry 贵族信息
     * @return 贵族信息
     */
    private static VipInfoEntry createVipInfoEntry(EnternoticeProto.VipInfoEntry vipInfoEntry) {
        VipInfoEntry vipInfoEntryBean = new VipInfoEntry();
        vipInfoEntryBean.setVipBackgroundImage(vipInfoEntry.getVipBackgroundImage());
        vipInfoEntryBean.setVipLevel(vipInfoEntry.getVipLevel());
        vipInfoEntryBean.setVipName(vipInfoEntry.getVipName());
        vipInfoEntryBean.setBadgeIcon(vipInfoEntry.getBadgeIcon());
        vipInfoEntryBean.setBadgeAspect(vipInfoEntry.getBadgeAspect());
        return vipInfoEntryBean;
    }

    /**
     * 创建新用户信息
     *
     * @param freshUser 是否新用户 1是 0否
     * @return 新用户信息
     */
    private static FreshUser createFreshUser(int freshUser) {
        FreshUser freshUserBean = new FreshUser();
        freshUserBean.setIs(freshUser == 1);
        return freshUserBean;
    }

    /**
     * 创建进房公告信息
     *
     * @param enterNoticeEntry   进房公告信息
     * @param wealthLevelBean    财富等级信息
     * @param userMountEntryBean 用户座驾信息
     * @param vipInfoEntryBean   贵族信息
     * @param freshUserBean      新用户信息
     * @return 进房公告信息
     */
    private static EnterNoticeEntry createEnterNoticeEntry(EnternoticeProto.EnterNoticeEntry enterNoticeEntry,
                                                    WealthLevelDTO wealthLevelBean, UserMountEntry userMountEntryBean,
                                                    VipInfoEntry vipInfoEntryBean, FreshUser freshUserBean) {
        EnterNoticeEntry enterNoticeEntryBean = new EnterNoticeEntry();
        enterNoticeEntryBean.setUserId(enterNoticeEntry.getUserId());
        enterNoticeEntryBean.setTimeStamp(enterNoticeEntry.getTimeStamp());
        enterNoticeEntryBean.setAnonymous(enterNoticeEntry.getAnonymous());
        enterNoticeEntryBean.setNotice(enterNoticeEntry.getNotice());
        enterNoticeEntryBean.setCount(enterNoticeEntry.getCount());
        enterNoticeEntryBean.setWealthInfo(wealthLevelBean);
        enterNoticeEntryBean.setUserMountEntry(userMountEntryBean);
        enterNoticeEntryBean.setUserCover(enterNoticeEntry.getUserCover());
        enterNoticeEntryBean.setVipInfoEntry(vipInfoEntryBean);
        enterNoticeEntryBean.setRelationEffectJson(enterNoticeEntry.getRelationEffectJson());
        enterNoticeEntryBean.setFreshUserEntry(freshUserBean);
        return enterNoticeEntryBean;
    }

    /**
     * 创建进房公告信息
     *
     * @param enterNoticeEntry   进房公告信息
     * @param wealthLevelBean    财富等级信息
     * @param userMountEntryBean 用户座驾信息
     * @param vipInfoEntryBean   贵族信息
     * @param freshUserBean      新用户信息
     * @return 进房公告信息
     */
    private static EnterNoticeDTO createEnterNoticeDTO(EnternoticeProto.EnterNoticeEntry enterNoticeEntry,
                                                           WealthLevelDTO wealthLevelBean, UserMountEntry userMountEntryBean,
                                                           VipInfoEntry vipInfoEntryBean, FreshUser freshUserBean) {
        EnterNoticeDTO enterNoticeEntryBean = new EnterNoticeDTO();
        enterNoticeEntryBean.setUserId(enterNoticeEntry.getUserId());
        enterNoticeEntryBean.setTimeStamp(enterNoticeEntry.getTimeStamp());
        enterNoticeEntryBean.setAnonymous(enterNoticeEntry.getAnonymous());
        enterNoticeEntryBean.setNotice(enterNoticeEntry.getNotice());
        enterNoticeEntryBean.setCount(enterNoticeEntry.getCount());
        enterNoticeEntryBean.setWealthInfo(wealthLevelBean);
        enterNoticeEntryBean.setUserMountEntry(userMountEntryBean);
        enterNoticeEntryBean.setUserCover(enterNoticeEntry.getUserCover());
        enterNoticeEntryBean.setFreshUserEntry(freshUserBean);
        return enterNoticeEntryBean;
    }
}
