package fm.lizhi.ocean.wave.comment.core.model.vo;

import fm.lizhi.ocean.wave.comment.core.model.bean.CommentExtraInfo;
import fm.lizhi.ocean.wave.comment.core.remote.bean.PpCommentComBizExtra;
import fm.lizhi.ocean.wave.comment.core.remote.bean.XmCommentComBizExtra;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 直播间评论VO
 *
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class CommentVO extends CommentBaseInfoVO {

    /**
     * pp通用业务扩展字段
     * 即会影响所有评论类型，PP业务独有的字段
     */
    private PpCommentComBizExtra ppCommonBizExtra;

    /**
     * xm通用业务扩展字段
     * 即会影响所有评论类型，xm业务独有的字段
     */
    private XmCommentComBizExtra xmCommonBizExtra;

    /**
     * 评论额外属性，只要是非评论通用属性，就放在这里
     */
    private CommentExtraInfoVO<? extends CommentExtraInfo> extra;

}
