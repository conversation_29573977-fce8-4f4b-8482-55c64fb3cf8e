package fm.lizhi.ocean.wave.comment.core.manager;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.google.common.base.Strings;
import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.commons.service.client.rcode.GeneralRCode;
import fm.lizhi.ocean.wave.comment.core.config.CommentCommonConfig;
import fm.lizhi.ocean.wave.comment.core.config.CommentConfig;
import fm.lizhi.ocean.wave.comment.core.constants.CommentMsgCodes;
import fm.lizhi.ocean.wave.comment.core.constants.WaveCommentType;
import fm.lizhi.ocean.wave.comment.core.convert.CommentMsgConvert;
import fm.lizhi.ocean.wave.comment.core.dao.redis.CommentRedis;
import fm.lizhi.ocean.wave.comment.core.extension.comment.IGetCommentMsgProcessor;
import fm.lizhi.ocean.wave.comment.core.extension.medal.bean.GenMedalInfoV2Context;
import fm.lizhi.ocean.wave.comment.core.extension.medal.v2.ICommentV2MedalNode;
import fm.lizhi.ocean.wave.comment.core.manager.filter.CommentMsgGiftTypeFilter;
import fm.lizhi.ocean.wave.comment.core.manager.filter.CommentMsgToUserFilter;
import fm.lizhi.ocean.wave.comment.core.manager.filter.CommentV2Filter;
import fm.lizhi.ocean.wave.comment.core.model.bean.ICommentBean;
import fm.lizhi.ocean.wave.comment.core.model.dto.TransientCommentDTO;
import fm.lizhi.ocean.wave.comment.core.model.mapper.CommentMapper;
import fm.lizhi.ocean.wave.comment.core.model.result.LatestCommentsV2Result;
import fm.lizhi.ocean.wave.comment.core.model.vo.*;
import fm.lizhi.ocean.wave.comment.core.remote.bean.FreshUser;
import fm.lizhi.ocean.wave.comment.core.remote.param.GetCommentWithServerTimeParam;
import fm.lizhi.ocean.wave.comment.core.remote.result.GetLiveCommentResult;
import fm.lizhi.ocean.wave.comment.core.remote.service.ILiveCommentQueryServiceRemote;
import fm.lizhi.ocean.wave.common.auto.route.common.remote.MyAutowired;
import fm.lizhi.ocean.wave.common.config.CommonProviderConfig;
import fm.lizhi.ocean.wave.common.extension.ProcessorV2Factory;
import fm.lizhi.ocean.wave.common.model.BusinessConfig;
import fm.lizhi.ocean.wave.common.util.RpcResult;
import fm.lizhi.ocean.wave.common.util.UrlUtils;
import fm.lizhi.ocean.wave.live.core.remote.bean.LiveBean;
import fm.lizhi.ocean.wave.live.core.remote.param.GetLiveRemoteParam;
import fm.lizhi.ocean.wave.live.core.remote.result.GetLiveRemoteResult;
import fm.lizhi.ocean.wave.live.core.remote.service.ILiveServiceRemote;
import fm.lizhi.ocean.wave.server.common.util.ContextUtils;
import fm.lizhi.ocean.wave.server.common.vo.ResultVO;
import fm.lizhi.ocean.wave.user.api.UserService;
import fm.lizhi.ocean.wave.user.api.UserWealthLevelService;
import fm.lizhi.ocean.wave.user.bean.SimpleUser;
import fm.lizhi.ocean.wave.user.bean.UserAvatarWidget;
import fm.lizhi.ocean.wave.user.param.BatchGetUserParam;
import fm.lizhi.ocean.wave.user.result.BatchGetSimpleUserResult;
import fm.lizhi.ocean.wave.user.result.FreshUserResult;
import fm.lizhi.ocean.wave.user.result.WealthLevelResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 评论
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class CommentQueryManager {

    @Autowired
    private UserService userService;
    @MyAutowired
    private ILiveServiceRemote liveServiceRemote;
    @MyAutowired
    private ILiveCommentQueryServiceRemote liveCommentServiceRemote;

    @Autowired
    private CommentConfig commentConfig;
    @Autowired
    private CommonProviderConfig commonConfig;
    @Autowired
    private CommentFilterManager commentFilterManager;

    @Autowired
    private UserWealthLevelService userWealthLevelService;

    @Autowired
    private ProcessorV2Factory processorV2Factory;

    @Autowired
    private CommentRedis commentRedis;

    /**
     * 获取最近评论列表
     *
     * @param liveId        直播id
     * @param performanceId performanceId
     * @return 最近消息结果
     */
    public ResultVO<LatestCommentsV2Result> latestComments(Long liveId, String performanceId) {
        return getComments(liveId, performanceId, false);
    }


    /**
     * 获取最近过滤评论列表
     *
     * @param liveId        直播id
     * @param performanceId performanceId
     * @return 过滤后的评论列表
     */
    public ResultVO<LatestCommentsV2Result> latestFilterComments(Long liveId, String performanceId) {
        return getComments(liveId, performanceId, true);
    }

    /**
     * 查询特殊活动评论
     *
     * @param liveId        直播节目ID
     * @param performanceId 分页查询的起始时间
     * @return 结果
     */
    public ResultVO<LatestCommentsV2Result> getSpecialActivityComments(Long liveId, String performanceId) {
        IGetCommentMsgProcessor processor = processorV2Factory.getProcessor(IGetCommentMsgProcessor.class);
        GetCommentWithServerTimeParam queryParam = buildQueryParam(liveId, performanceId, processor.getCommentConfig());
        // 从redis中获取数据，数据已经按升序排好序了
        List<SpecialActivityCommentVO> specialActivityCommentMsg = commentRedis.getSpecialActivityCommentMsg(queryParam.getAppId(), queryParam.getLiveId(), queryParam.getStartTime(), queryParam.getEndTime());
        List<SpecialActivityCommentVO> res = specialActivityCommentMsg.subList(0, Math.min(specialActivityCommentMsg.size(), queryParam.getCount()));
        log.info("getSpecialActivityComments liveId:{}, startTime:{}, endTime:{}, count:{}, size:{}", queryParam.getLiveId(), queryParam.getStartTime(), queryParam.getEndTime(), queryParam.getCount(), specialActivityCommentMsg.size());
        return ResultVO.success(new LatestCommentsV2Result()
                .setLastPage(specialActivityCommentMsg.size() < queryParam.getCount())
                .setPerformanceId(String.valueOf(res.isEmpty() ? System.currentTimeMillis() : res.get(res.size() - 1)))
                .setQueryInterval(commentConfig.getQueryCommentMsgInterval())
                .setLiveComment(new ArrayList<>(res)));
    }

    /**
     * 获取评论列表
     *
     * @param liveId        直播id
     * @param performanceId performanceId
     * @return 评论列表
     */
    public ResultVO<LatestCommentsV2Result> getComments(Long liveId, String performanceId, boolean isFilter) {
        IGetCommentMsgProcessor processor = processorV2Factory.getProcessor(IGetCommentMsgProcessor.class);
        // 1. 平台自身前置检验
        LiveBean live = getLive(liveId);
        if (live == null) {
            log.warn("getComments live not exist, liveId:{}", liveId);
            return ResultVO.failure(CommentMsgCodes.GET_LATEST_COMMENT_LIVE_NOT_EXIST.getCode(),
                    CommentMsgCodes.GET_LATEST_COMMENT_LIVE_NOT_EXIST.getMsg());
        }

        // 2. 查询出评论列表
        ResultVO<GetLiveCommentResult> commentListRes = getCommentList(liveId, performanceId, processor.getCommentConfig());
        if (!commentListRes.isOK()) {
            log.warn("getComments commentListRes fail, liveId:{}, performanceId:{}", liveId, performanceId);
            return ResultVO.failure(CommentMsgCodes.GET_LATEST_COMMENT_FAIL.getCode(), CommentMsgCodes.GET_LATEST_COMMENT_FAIL.getMsg());
        }

        //3. 过滤处理进房评论消息
        List<TransientCommentDTO> comments = processEnterComment(commentListRes.getData().getTransientComments(), processor, isFilter);
        //4. 过滤并对评论排序
        List<TransientCommentDTO> filterComments = filterAndSortComments(comments, ContextUtils.getContext().getUserId(), isFilter);
        //5. 转换评论列表
        List<CommentVO> liveCommentVOList = transformCommentsToLiveComment(live, filterComments, processor);
        //6. 特殊处理进房卡片
        List<ICommentBean> commentResList = processor.processSpecialEnterMsg(filterComments, liveCommentVOList);
        return ResultVO.success(new LatestCommentsV2Result()
                .setLastPage(commentListRes.getData().isLastPage())
                .setPerformanceId(String.valueOf(commentListRes.getData().getCommentServerTime()))
                .setQueryInterval(commentConfig.getQueryCommentMsgInterval())
                .setLiveComment(commentResList));
    }

    /**
     * 过滤并对评论排序
     *
     * @param transientComments 评论列表
     * @param userId            用户id
     * @param isFilter          是否过滤
     * @return 过滤后的评论列表
     */
    public List<TransientCommentDTO> filterAndSortComments(List<TransientCommentDTO> transientComments, long userId, boolean isFilter) {
        // 过滤评论
        CommentCompositeFilter commentCompositeFilter = new CommentCompositeFilter();
        if (userId > 0) {
            CommentV2Filter commentToUserFilter = new CommentMsgToUserFilter(userId);
            commentCompositeFilter.addFilter(commentToUserFilter);
        }

        // 增加过滤器
        if (isFilter) {
            CommentV2Filter msgGiftTypeFilter = new CommentMsgGiftTypeFilter();
            commentCompositeFilter.addFilter(msgGiftTypeFilter);
        }

        List<TransientCommentDTO> filterComment = commentCompositeFilter.filter(transientComments);

        // 按时间排序
        filterComment.sort(Comparator.comparing(TransientCommentDTO::getTime));
        return filterComment;
    }

    /**
     * 转换评论信息列表
     *
     * @param live              直播信息
     * @param transientComments 评论信息列表
     * @return 转换后的评论信息列表
     */
    public List<CommentVO> transformCommentsToLiveComment(LiveBean live,
                                                          List<TransientCommentDTO> transientComments,
                                                          IGetCommentMsgProcessor processor) {
        CommentCommonConfig commentConfig = processor.getCommentConfig();
        List<ICommentV2MedalNode> commentMedalNodes = processor.getBuildCommentMedalNodes();

        List<CommentVO> commentVOList = new ArrayList<>();

        List<Long> userIds = transientComments.stream().map(TransientCommentDTO::getUserId).collect(Collectors.toList());
        //批量查询用户头像
        Map<Long, UserAvatarWidget> userAvatarWidgetMap = userService.batchGetUserAvatarWidget(userIds).target();

        //批量查询用户
        Map<Long, SimpleUser> simpleUserMap = batchGetUser(userIds);
        for (TransientCommentDTO transientComment : transientComments) {
            SimpleUser user = simpleUserMap.getOrDefault(transientComment.getUserId(), null);
            if (user == null) {
                log.warn("transformCommentsToLiveComment user not exist,liveId={}, userId:{}, content={}",
                        live.getId(), transientComment.getUserId(), transientComment.getContent());
                continue;
            }

            // 如果是系统公告评论，并且存在操作人ID，则需要重置用户属性信息（此时的用户属性信息都是主播的）
            ResultVO<TransientCommentDTO> resetRes = processor.resetCommentUserProperty(transientComment);
            if (!resetRes.isOK()) {
                log.warn("transformCommentsToLiveComment resetCommentUserProperty fail,liveId={}, userId:{}, content={}",
                        live.getId(), transientComment.getUserId(), transientComment.getContent());
                continue;
            }

            // 处理用户勋章
            List<BadgeImageVO> imgIcons = handelBadgeImageVO(live, transientComment, commentConfig, commentMedalNodes);

            //构建基本评论信息
            CommentVO commentVO = CommentMsgConvert.I.builfBaseCommentVO(transientComment);
            //补全图片评论
            fillImageComment(commentVO, transientComment, commonConfig);
            //补全用户信息
            fillUserInfo(commentVO, transientComment, processor, imgIcons, user, userAvatarWidgetMap);
            //补全其他额外信息
            processor.fillCommentExtraInfo(commentVO, transientComment);
            commentVOList.add(commentVO);
        }

        return commentVOList;
    }

    /**
     * 获取评论列表
     *
     * @param liveId        直播id
     * @param performanceId performanceId
     * @return 结果
     */
    private ResultVO<GetLiveCommentResult> getCommentList(Long liveId, String performanceId, CommentCommonConfig config) {
        //构建查询参数
        GetCommentWithServerTimeParam param = buildQueryParam(liveId, performanceId, config);

        //查询评论列表
        Result<GetLiveCommentResult> result = liveCommentServiceRemote.getCommentWithServerTime(param);
        if (result.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
            log.warn("getCommentList error, param:{}", param);
            return ResultVO.failure("获取评论列表失败");
        }

        //差别比较大的，需要调整条数
        if ((param.getEndTime() - param.getStartTime()) >= config.getUpdateCommentCountTimeInterval()) {
            log.info("getCommentList, liveId={},userId={},performanceId={} startTime={},endTime={},resSize={}",
                    liveId, ContextUtils.getContext().getUserId(), performanceId,
                    param.getStartTime(), param.getEndTime(), result.target().getTransientComments().size());
        }

        return ResultVO.success(result.target());
    }


    /**
     * 获取直播信息
     *
     * @param liveId 直播id
     * @return 直播信息
     */
    private LiveBean getLive(Long liveId) {
        GetLiveRemoteParam getLiveRemoteParam = new GetLiveRemoteParam();
        getLiveRemoteParam.setLiveId(liveId);
        //getLiveWithCache底层也是加了本地缓存，如果能上游控制，就不在下游控制，同时减少网络IO
        Result<GetLiveRemoteResult> liveResult = liveServiceRemote.getLiveByCache(getLiveRemoteParam);
        if (liveResult.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
            log.error("getLive error, param:{}", getLiveRemoteParam);
            return null;
        }

        return liveResult.target().getLiveBean();
    }

    /**
     * 获取用户信息
     *
     * @param userIdList 用户ID列表
     * @return 用户信息
     */
    private Map<Long, SimpleUser> batchGetUser(List<Long> userIdList) {
        if (CollectionUtils.isEmpty(userIdList)) {
            return new HashMap<>(2);
        }
        BatchGetUserParam userParam = BatchGetUserParam.builder().userIdList(userIdList).build();
        Result<BatchGetSimpleUserResult> result = userService.batchGetSimpleUserByCache(userParam);
        if (result.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
            log.warn("batchGetUser error, userIdList:{}", userIdList);
            return new HashMap<>();
        }
        List<SimpleUser> simpleUsers = result.target().getUserList();
        if (CollectionUtils.isEmpty(simpleUsers)) {
            return new HashMap<>();
        }
        //simpleUsers转成map
        // 获取用户信息
        return simpleUsers.stream().collect(Collectors.toMap(SimpleUser::getUserId, Function.identity()));
    }


    /**
     * 处理渠道进房评论消息，例如：xxxx从xxxx来了
     * 补全缺失的勋章
     *
     * @param comments 评论消息列表
     * @param isFilter 是否过滤，true: 过滤
     * @return left: 评论列表，right: 进房公告列表
     */
    public List<TransientCommentDTO> processEnterComment(List<TransientCommentDTO> comments, IGetCommentMsgProcessor processor, boolean isFilter) {
        long userId = ContextUtils.getContext().getUserId();
        //判断是否存在渠道进房评论消息
        boolean isExistChannelEnterComment = comments.stream().anyMatch(comment -> comment.getCommentType() == WaveCommentType.CHANNEL_ENTER_COMMENT);
        if (!isExistChannelEnterComment) {
            //不存在就直接结束，因为这类评论很少，解约时间
            return comments;
        }

        for (TransientCommentDTO comment : comments) {
            if (comment.getCommentType() != WaveCommentType.CHANNEL_ENTER_COMMENT) {
                //不是渠道进房评论，跳过
                continue;
            }

            if (comment.getFreshUser() == null) {
                //补充新人信息
                Result<FreshUserResult> freshUser = userService.isFreshUserByCache(comment.getUserId());
                if (freshUser.rCode() == 0 && freshUser.target() != null) {
                    FreshUserResult userResult = freshUser.target();
                    FreshUser user = FreshUser.builder().is(userResult.isIs()).url(userResult.getUrl()).aspect(userResult.getAspect()).build();
                    comment.setFreshUser(user);
                }
            }

            if (comment.getWealthLevel() == null) {
                //一般一批评论的去到进房消息数量不多，所以遍历查询没有问题
                Result<WealthLevelResult> wealthLevelFromCache = userWealthLevelService.getWealthLevelFromCache(comment.getUserId());
                //补充财富等级
                WealthLevelResult wealthLevelResult = RpcResult.isSuccess(wealthLevelFromCache) ? wealthLevelFromCache.target() : null;
                comment.setWealthLevel(CommentMapper.I.toWealthLevelDTO(wealthLevelResult));
            }

            //补充其他的勋章，目前只有PP补全贵族勋章
            processor.fillEnterCommentMedal(comment);
            log.debug("processEnterComment, comment:{}", JsonUtil.dumps(comment));
        }

        //如果存在渠道进房评论，且需要过滤，则根据用户设置做评论消息过滤
        if (isFilter) {
            comments = commentFilterManager.filterEnterCommentByUserSetting(userId, ContextUtils.getContext().getHeader().getAppId(), comments);
        }
        return comments;
    }

    /**
     * 生成处理用户图标
     *
     * @param live              直播间信息
     * @param transientComment  评论信息
     * @param commentConfig     评论配置
     * @param commentMedalNodes 勋章节点
     * @return 勋章列表
     */
    private List<BadgeImageVO> handelBadgeImageVO(LiveBean live, TransientCommentDTO transientComment, CommentCommonConfig commentConfig, List<ICommentV2MedalNode> commentMedalNodes) {
        GenMedalInfoV2Context context = GenMedalInfoV2Context.builder()
                .comment(transientComment)
                .medalShowArea(GenMedalInfoV2Context.MedalShowArea.COMMENT_AREA.getArea())
                .liveBean(live).appId(ContextUtils.getContext().getHeader().getAppId())
                .commentConfig(commentConfig).build();

        List<BadgeImageVO> imgIcons = new ArrayList<>();
        if (CollectionUtils.isEmpty(commentMedalNodes)) {
            return imgIcons;
        }

        //按顺序构建勋章图标
        for (ICommentV2MedalNode medalNode : commentMedalNodes) {
            try {
                Optional<List<BadgeImageVO>> listOptional = medalNode.buildMedalImageInfo(context);
                listOptional.ifPresent(imgIcons::addAll);
            } catch (Exception e) {
                log.error("buildMedalImageInfo error, userId={}, comment={}", context.getComment().getUserId(), context.getComment(), e);
            }
        }
        return imgIcons;
    }

    /**
     * 构建查询评论参数
     *
     * @param liveId        直播节目ID
     * @param performanceId performanceId
     * @param config        评论公共配置
     * @return 参数
     */
    public GetCommentWithServerTimeParam buildQueryParam(Long liveId, String performanceId, CommentCommonConfig config) {
        GetCommentWithServerTimeParam.GetCommentWithServerTimeParamBuilder builder = GetCommentWithServerTimeParam.builder();
        long endTime = System.currentTimeMillis();
        // 判断开始时间是否为null，如果为null，则是首次获取
        boolean isNjFirst = performanceId != null;
        //第一次进入直播间
        boolean firstEntry = performanceId == null;
        // 计算评论范围开始时间
        long startTime = getCommentScopeStartTime(performanceId, endTime, config);
        int getLiveCount = getQueryCount(startTime, endTime, liveId, config);
        builder.liveId(liveId)
                .count(getLiveCount)
                .startTime(startTime)
                .endTime(endTime)
                .firstEntry(firstEntry)
                .appId(ContextUtils.getContext().getHeader().getAppId())
                .commentCacheSwitch(config.isCommentCacheSwitch())
                .njFirst(isNjFirst);
        return builder.build();
    }

    /**
     * 获取评论范围开始时间
     *
     * @param performanceId 开始时间的字符串
     * @param endTime       结束时间
     * @param config        配置
     * @return 开始时间
     */
    private long getCommentScopeStartTime(String performanceId, long endTime, CommentCommonConfig config) {
        long startTime;
        if (Strings.isNullOrEmpty(performanceId)) {
            // performanceId中没有记录上次的时间区间上限时，拿当前时间往前推配置字段值的时间为开始时间
            startTime = endTime - config.getRequestInterval() * 1000L;
        } else {
            startTime = Long.parseLong(performanceId);
            // 从performanceId中获取的上次的时间区间上限值大等于endTime时，拿当前时间往前推配置字段值的时间为开始时间
            if (startTime >= endTime) {
                startTime = endTime - (config.getRequestInterval() * 1000L);
            }
        }
        return startTime;
    }


    /**
     * 获取查询评论数量
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @param liveId    直播间ID
     * @param config    公共配置
     * @return 时间间隔
     */
    private int getQueryCount(long startTime, long endTime, long liveId, CommentCommonConfig config) {
        try {
            int timeDelay = (int) (endTime - startTime);
            if (timeDelay >= config.getUpdateCommentCountTimeInterval()) {
                log.info("updateCommentCountTimeInterval,liveId={}, userId={}, startTime={}, endTime={},count={}", liveId,
                        ContextUtils.getContext().getUserId(), startTime, endTime, config.getMaxLiveCommentsRespCount());
                return config.getMaxLiveCommentsRespCount();
            }
        } catch (Exception e) {
            log.warn("getQueryCount fail,liveId={}, userId={}", liveId, ContextUtils.getContext().getUserId());
        }
        return config.getLiveCommentsRespLimit();
    }

    /**
     * 补全用户信息
     *
     * @param commentVO           评论信息结果
     * @param transientComment    评论信息
     * @param processor           差异化处理器
     * @param imgIcons            勋章图片列表
     * @param user                用户信息
     * @param userAvatarWidgetMap 头像框map
     */
    private void fillUserInfo(CommentVO commentVO, TransientCommentDTO transientComment, IGetCommentMsgProcessor processor,
                              List<BadgeImageVO> imgIcons, SimpleUser user, Map<Long, UserAvatarWidget> userAvatarWidgetMap) {
        if (isShowUserInfo(transientComment.getCommentType())) {
            UserAvatarWidget avatarWidget = userAvatarWidgetMap.get(transientComment.getUserId());
            // 适配差异化用户信息
            MsgUserVO<?> userVO = processor.createUserAndFillBizExtra(transientComment);
            userVO.setUserAvatarWidget(avatarWidget);
            userVO.setUserId(String.valueOf(user.getUserId()));
            userVO.setUserName(user.getNickName());
            userVO.setUserAvatar(user.getAvatar());
            userVO.setUserIcons(imgIcons);
            processor.adapterUserExtra(transientComment, userVO);
            commentVO.setUser(userVO);
        }
    }

    /**
     * 补全图片评论
     *
     * @param commentVO        评论对象结果
     * @param transientComment 评论对象元数据
     */
    private void fillImageComment(CommentVO commentVO, TransientCommentDTO transientComment, CommonProviderConfig commonConfig) {
        BusinessConfig businessConfig = commonConfig.getBusinessConfig(ContextUtils.getContext().getHeader().getAppId());
        // 处理图片评论
        if (StringUtils.isNotBlank(transientComment.getImageUrl())) {
            String imgUrl = UrlUtils.addCdnHost(businessConfig.getCdnHost(), transientComment.getImageUrl());
            ImageVO imageVO = CommentMsgConvert.I.buildImageVO(transientComment, imgUrl);
            commentVO.setImage(imageVO);
        }

        // 处理表情评论
        if (transientComment.getEmotionId() > 0) {
            EmotionVO emotionVO = new EmotionVO();
            emotionVO.setEmotionId(String.valueOf(transientComment.getEmotionId()));
            emotionVO.setRepeatStopImageIndex(transientComment.getRepeatStopImageIndex());
            commentVO.setEmotion(emotionVO);
        }
    }

    /**
     * 是否展示用户信息
     *
     * @param commentType 评论类型
     * @param commentType 评论类型扩展
     * @return true: 展示，false: 不展示
     */
    private boolean isShowUserInfo(int commentType) {
        //活动评论不展示头像
        if (commentType == WaveCommentType.ACTIVITY_COMMENT) {
            return false;
        }
        //进房评论不展示头像
        return commentType != WaveCommentType.LIVE_INTRO;
    }

}
