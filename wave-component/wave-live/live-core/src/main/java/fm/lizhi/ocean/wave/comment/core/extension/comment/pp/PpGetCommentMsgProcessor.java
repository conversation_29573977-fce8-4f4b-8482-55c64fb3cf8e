package fm.lizhi.ocean.wave.comment.core.extension.comment.pp;

import com.google.common.collect.Lists;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wave.comment.core.config.CommentCommonConfig;
import fm.lizhi.ocean.wave.comment.core.config.CommentConfig;
import fm.lizhi.ocean.wave.comment.core.constants.WaveCommentType;
import fm.lizhi.ocean.wave.comment.core.convert.CommentMsgConvert;
import fm.lizhi.ocean.wave.comment.core.extension.comment.IGetCommentMsgProcessor;
import fm.lizhi.ocean.wave.comment.core.extension.medal.v2.*;
import fm.lizhi.ocean.wave.comment.core.model.bean.ICommentBean;
import fm.lizhi.ocean.wave.comment.core.model.dto.PpCommentExtraDTO;
import fm.lizhi.ocean.wave.comment.core.model.dto.TransientCommentDTO;
import fm.lizhi.ocean.wave.comment.core.model.dto.WealthLevelDTO;
import fm.lizhi.ocean.wave.comment.core.model.vo.MsgUserVO;
import fm.lizhi.ocean.wave.comment.core.model.vo.CommentVO;
import fm.lizhi.ocean.wave.comment.core.model.vo.PpMsgUserExtraInfo;
import fm.lizhi.ocean.wave.comment.core.remote.bean.*;
import fm.lizhi.ocean.wave.common.config.CommonProviderConfig;
import fm.lizhi.ocean.wave.common.model.BusinessConfig;
import fm.lizhi.ocean.wave.common.util.ModelMapperUtils;
import fm.lizhi.ocean.wave.common.util.RpcResult;
import fm.lizhi.ocean.wave.common.util.UrlUtils;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wave.server.common.util.ContextUtils;
import fm.lizhi.ocean.wave.server.common.vo.ResultVO;
import fm.lizhi.ocean.wave.user.api.*;
import fm.lizhi.ocean.wave.user.result.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * Description: 获取评论差异化处理类
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class PpGetCommentMsgProcessor implements IGetCommentMsgProcessor {

    @Autowired
    private CommentConfig commentConfig;

    @Autowired
    private WealthMedalV2Node wealthMedalNode;

    @Autowired
    private UserMedalListV2Node userMedalListNode;

    @Autowired
    private UserRoleMedalV2Node userRoleMedalNode;

    @Autowired
    private VipLevelMedalV2Node vipLevelMedalNode;

    @Autowired
    private FreshUserMedalV2Node freshUserMedalNode;

    @Autowired
    private PpGrowRelationMedalV2Node growRelationMedalNode;

    @Autowired
    private UserWealthLevelService userWealthLevelService;

    @Autowired
    private MedalService medalService;

    @Autowired
    private GrowRelationLevelService growRelationLevelService;

    @Autowired
    private CommonProviderConfig commonProviderConfig;

    @Autowired
    private UserService userService;

    @Autowired
    private VipPrivilegeService vipPrivilegeService;

    @Autowired
    private PpGuardMedalV2Node ppGuardMedalV2Node;


    @Override
    public BusinessEvnEnum getBusinessEnv() {
        return BusinessEvnEnum.PP;
    }

    @Override
    public MsgUserVO<PpMsgUserExtraInfo> createUserAndFillBizExtra(TransientCommentDTO transientComment) {
        return new MsgUserVO<>();
    }

    @Override
    public void adapterUserExtra(TransientCommentDTO transientComment, MsgUserVO<?> userVO) {
        // 蒙面评论，transientComment的用户信息中的头像和昵称已经在dc被替换过的了，直接使用
        if (transientComment.getPpCommentExtra().getMaskStatus() == 1) {
            userVO.setUserAvatar(transientComment.getSimpleUser().getPortrait());
            userVO.setUserName(transientComment.getSimpleUser().getName());
            // 头像框、勋章不显示
            userVO.setUserAvatarWidget(null);
            userVO.setUserIcons(null);
        }
    }

    @Override
    public CommentCommonConfig getCommentConfig() {
        return commentConfig.getPp();
    }

    @Override
    public List<ICommentV2MedalNode> getBuildCommentMedalNodes() {
        List<ICommentV2MedalNode> nodes = new ArrayList<>(8);
        //不同的业务需要的勋章节点不同，且顺序不同
        //- 守护勋章
        //- 贵族勋章
        //- 角色勋章
        //- 财富等级信息
        //- 成长关系
        //- 新用户勋章
        //- 勋章列表
        nodes.add(ppGuardMedalV2Node);
        nodes.add(vipLevelMedalNode);
        nodes.add(userRoleMedalNode);
        nodes.add(wealthMedalNode);
        nodes.add(growRelationMedalNode);
        nodes.add(freshUserMedalNode);
        nodes.add(userMedalListNode);
        return nodes;
    }

    @Override
    public ResultVO<TransientCommentDTO> resetCommentUserProperty(TransientCommentDTO transientComment) {
        //- 贵族勋章
        //- 财富等级信息
        //- 成长关系
        //- 新用户勋章
        //- 勋章列表
        //只针对系统公告评论做用户属性数据重置
        boolean needReset = transientComment.getCommentType() == WaveCommentType.SYSTEM_NOTICE_COMMENT && transientComment.getOperatorUserId() > 0;
        if (!needReset) {
            return ResultVO.success(transientComment);
        }

        //拷贝数据
        TransientCommentDTO targetComment = ModelMapperUtils.MODEL_MAPPER.map(transientComment, TransientCommentDTO.class);
        PpCommentExtraDTO ppCommentExtra = targetComment.getPpCommentExtra();
        //查询勋章列表信息
        Result<List<MedalResult>> medalListRes = medalService.getMedalListFromCache(targetComment.getUserId());
        //查询财富等级信息
        Result<WealthLevelResult> wealthRes = userWealthLevelService.getWealthLevelFromCache(targetComment.getUserId());
        //是否是新用户查询
        Result<FreshUserResult> freshUserResult = userService.isFreshUserByCache(targetComment.getUserId());
        //重置用户成长关系勋章
        Result<List<ShowMealRelationResult>> relationResult = growRelationLevelService.getShowMealRelationFromCache(targetComment.getUserId());
        //补充贵族勋章
        Result<VipPrivilegeMedalResult> userVipInfo = vipPrivilegeService.getVipInfoFromCache(targetComment.getUserId());

        List<Medal> medals = CommentMsgConvert.I.convertMedalList(medalListRes.target());
        WealthLevelDTO wealthLevelDTO = CommentMsgConvert.I.convertWealthLevel(wealthRes.target());
        FreshUser freshUser = CommentMsgConvert.I.convertFreshUser(freshUserResult.target());
        VipMedal vipMedal = CommentMsgConvert.I.convertVipMedal(userVipInfo.target());

        if (wealthLevelDTO != null) {
            wealthLevelDTO.setCover(buildWealthUrl(wealthLevelDTO));
        }
        targetComment.setMedal(medals);
        targetComment.setWealthLevel(wealthLevelDTO);
        targetComment.setFreshUser(freshUser);
        ppCommentExtra.setVipMedal(vipMedal);

        if (RpcResult.isSuccess(relationResult)) {
            List<ShowMealRelationResult> showMealRelationResults = relationResult.target();
            Optional<ShowMealRelationResult> relationOption = showMealRelationResults.stream()
                    .filter(relation -> relation.getRecUserId() == ContextUtils.getContext().getUserId()).findFirst();
            if (relationOption.isPresent()) {
                GrowRelationLevel growRelationLevel = CommentMsgConvert.I.convertGrowRelationLevel(relationOption.get());
                ppCommentExtra.setGrowRelationLevelList(Lists.newArrayList(growRelationLevel));
            }
        }

        targetComment.setPpCommentExtra(ppCommentExtra);
        return ResultVO.success(targetComment);
    }

    @Override
    public void fillEnterCommentMedal(TransientCommentDTO transientComment) {
        PpCommentExtraDTO ppCommentExtra = transientComment.getPpCommentExtra();
        //补充贵族勋章
        Result<VipPrivilegeMedalResult> userVipInfo = vipPrivilegeService.getVipInfoFromCache(transientComment.getUserId());
        if (userVipInfo != null && userVipInfo.target() != null) {
            BusinessConfig businessConfig = commonProviderConfig.getBusinessConfig(BusinessEvnEnum.PP.getAppId());
            VipPrivilegeMedalResult vipInfo = userVipInfo.target();
            VipLevel vipMedal = new VipLevel();
            vipMedal.setLevel(vipInfo.getLevel());
            vipMedal.setCover(UrlUtils.addCdnHost(businessConfig.getCdnHost(), vipInfo.getIcon()));
            vipMedal.setAspect(vipInfo.getAspect());
            ppCommentExtra.setVipLevel(vipMedal);
        }
        transientComment.setPpCommentExtra(ppCommentExtra);
    }

    @Override
    public void fillCommentExtraInfo(CommentVO commentVO, TransientCommentDTO transientComment) {
        commentVO.setPpCommonBizExtra(
                PpCommentComBizExtra.builder().masked(transientComment.getPpCommentExtra().getMaskStatus() == 1).build());
    }

    @Override
    public List<ICommentBean> processSpecialEnterMsg(List<TransientCommentDTO> transientCommentDTOList, List<CommentVO> commentVOList) {
        return new ArrayList<>(commentVOList);
    }

    /**
     * 构建财富等级图片地址
     *
     * @param wealthInfo 财富等级信息
     * @return 图片地址
     */
    private String buildWealthUrl(WealthLevelDTO wealthInfo) {
        if (wealthInfo.getCover().startsWith("http")) {
            return wealthInfo.getCover();
        }

        String cdn = commonProviderConfig.getBusinessConfig(BusinessEvnEnum.PP.getAppId()).getCdnHost();
        return UrlUtils.addCdnHost(cdn, wealthInfo.getCover());
    }
}
