package fm.lizhi.ocean.wave.comment.core.config;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface CommentCommonConfig {

    boolean isOverseaLimitSwitch();

    String getOverseaIpTestWhitelist();

    List<Long> getBigStarLiveRoomIds();

    int getSocialOperationLimitWithoutPhone();

    String getOverseaUserIdWhiteList();

    String getOverseaPhoneNumAppWhiteList();

    boolean isOverseaPhoneNumSwitch();

    String getCheckRiskNotice();

    /**
     * 老用户欢迎文案
     *
     * @return 文案
     */
    String getPayRateWelcomeOldUserText();

    /**
     * 新用户欢迎文案
     *
     * @return 文案
     */
    String getPayRateWelcomeNewUserText();

    /**
     * 新用户欢迎 - 送礼特效 - 爱心泡泡
     *
     * @return svga路径
     */
    String getPayRateWelcomeNewUserSvga();

    /**
     * 老用户欢迎 - 送礼特效 - 飘带
     *
     * @return svga路径
     */
    String getPayRateWelcomeOldUserSvga();

    /**
     * 是否开启欢迎功能
     *
     * @return true：开，false: 关
     */
    boolean isOpenWelcomeSwitch();


    /**
     * 超级管理员的
     *
     * @return
     */
    String getRoomSuperManagerIconUrl();

    /**
     * 房间管理图标
     */
    String getRoomManagerIconUrl();

    /**
     * 房主图片
     */
    String getRoomOwnerIconUrl();

    /**
     * 房主、管理图标高宽比
     */
    float getUserRoleImageBadgeAspect();

    /**
     * 超级管理员图标高宽比
     */
    default float getUserSuperManagerRoleImageBadgeAspect() {
        return 0.62f;
    }

    /**
     * 风控功能提示
     */
    String getCheckRiskFunctionNotice();

    /**
     * 获取勋章图标高宽比默认值
     */
    float getDefaultMedalIconAspect();

    /**
     * 进房公告获取数据间隔
     */
    int getEnterNoticeSecond();

    /**
     * 获取财富等级CDN
     */
    String getWealthLevelCdn();

    /**
     * 获取新用户icon 高度比
     *
     * @return 结果
     */
    float getFreshUserIconAspect();

    /**
     * 获取新用户icon url
     *
     * @return url
     */
    String getFreshUserIconUrl();

    /**
     * 表情默认发送评论
     */
    String getEmotionDefaultComment();

    /**
     * 互动表情 旧版本表情提示文案
     */
    String getInteractiveMotionImproveText();

    /**
     * 获取直播间评论列表最大长度
     */
    int getLiveCommentsRespLimit();

    /**
     * 获取直播间评论列表最大长度
     */
    int getRequestInterval();

    /**
     * 评论缓存开关
     */
    boolean isCommentCacheSwitch();

    /**
     * 进房公告缓存开关
     */
    boolean isEnterNoticeCacheSwitch();

    /**
     * 获取调整评论查询数量的时间间隔，单位ms
     */
    int getUpdateCommentCountTimeInterval();

    /**
     * 获取查询评论的最大条数
     */
    int getMaxLiveCommentsRespCount();

    /**
     * 自定义表情包分组icon
     *
     * @return
     */
    String getActionEmotionGroupIconUrl();

    /**
     * 未过审表情包图片地址
     *
     * @return
     */
    String getReviewEmotionUrl();

    /**
     * 评论日志开关
     *
     * @return 结果
     */
    boolean isCommentMsgLogSwitch();


    /**
     * 座驾黑名单ID列表，存在的话就不显示座驾
     */
    String getMountBlackListIds();

    /**
     * 评论接口打印日志请求用户iD
     *
     * @return
     */
    List<Long> getPrintCommentLogUserIds();

    /**
     * 互动玩法评论类型
     */
    List<Integer> getSpecialActivityCommentTypes();

    /**
     * 进房消息日志开关
     *
     * @return true: 开启
     */
    boolean isEnterMsgLogSwitch();

}
