package fm.lizhi.ocean.wave.comment.core.extension.medal.v2;

import com.google.common.collect.Lists;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wave.comment.core.extension.medal.ICommentMedalNode;
import fm.lizhi.ocean.wave.comment.core.extension.medal.bean.GenMedalInfoContext;
import fm.lizhi.ocean.wave.comment.core.extension.medal.bean.GenMedalInfoV2Context;
import fm.lizhi.ocean.wave.comment.core.model.dto.WealthLevelDTO;
import fm.lizhi.ocean.wave.comment.core.model.vo.BadgeImageVO;
import fm.lizhi.ocean.wave.common.config.CommonProviderConfig;
import fm.lizhi.ocean.wave.common.model.BusinessConfig;
import fm.lizhi.ocean.wave.common.util.UrlUtils;
import fm.lizhi.ocean.wave.user.api.OfficialCertifiedService;
import fm.lizhi.ocean.wave.user.bean.OfficialCertified;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;

/**
 * 财富等级勋章节点
 * <AUTHOR>
 */
@Component
public class HyWealthMedalV2Node implements ICommentV2MedalNode {

    @Autowired
    private CommonProviderConfig commonProviderConfig;

    @Autowired
    private OfficialCertifiedService officialCertifiedService;

    @Override
    public Optional<List<BadgeImageVO>> buildMedalImageInfo(GenMedalInfoV2Context context) {
        WealthLevelDTO wealthLevel = null;
        Long userId = null;

        if (context.getMedalShowArea() == GenMedalInfoV2Context.MedalShowArea.COMMENT_AREA.getArea()) {
            wealthLevel = context.getComment().getWealthLevel();
            userId = context.getComment().getUserId();
        } else if (context.getMedalShowArea() == GenMedalInfoV2Context.MedalShowArea.ENTER_NOTICE_AREA.getArea()) {
            wealthLevel = context.getEnterNoticeEntry().getWealthInfo();
        }

        // 财富等级为0或者财富等级信息不存在，不展示财富等级勋章
        if (wealthLevel == null || wealthLevel.getLevel() <= 0 || StringUtils.isBlank(wealthLevel.getCover())) {
            return Optional.empty();
        }

        // 存在官方认证标签，则评论不返回财富等级勋章
        if (userId != null && userId > 0L) {
            Result<List<OfficialCertified>> officialCertifiedResult = officialCertifiedService.getUserOfficialCertified(userId);

            if (officialCertifiedResult.rCode() != 0 || CollectionUtils.isNotEmpty(officialCertifiedResult.target())) {
                return Optional.empty();
            }
        }

        String wealthLevelCdn = context.getCommentConfig().getWealthLevelCdn();
        if (StringUtils.isBlank(wealthLevelCdn)) {
            BusinessConfig businessConfig = commonProviderConfig.getBusinessConfig(context.getAppId());
            wealthLevelCdn = businessConfig.getCdnHost();
        }

        // 财富等级
        BadgeImageVO badgeImageVO = new BadgeImageVO();
        badgeImageVO.setBadgeUrl(UrlUtils.addCdnHost(wealthLevelCdn, wealthLevel.getCover()));
        badgeImageVO.setBadgeAspect(wealthLevel.getAspect());
        return Optional.of(Lists.newArrayList(badgeImageVO));
    }
}
