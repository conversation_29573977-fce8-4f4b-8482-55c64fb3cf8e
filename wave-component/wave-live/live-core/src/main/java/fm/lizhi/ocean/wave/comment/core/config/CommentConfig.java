package fm.lizhi.ocean.wave.comment.core.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;


/**
 * 评论配置
 *
 * <AUTHOR>
 */
@ConfigurationProperties(prefix = "wave-comment")
@Data
public class CommentConfig {

    /**
     * 评论延迟开关
     */
    public boolean commentDelaySwitch = true;

    /**
     * 评论延迟阈值秒数
     */
    public int commentDelayThreshold = 2000;

    /**
     * 评论推送开关
     */
    public boolean commentRomaPushSwitch = true;

    /**
     * 进房公告条数
     */
    public int enterNoticeCount = 30;

    /**
     * 一次性生成评论id的数量
     */
    private int genCommentIdLimit = 100;

    /**
     * 清理评论缓存任务间隔周期，重启后才生效，单位分钟
     */
    private int cleanCommentCacheTaskCycleMin = 5;

    /**
     * 清理开始时间，距离当前时间N分钟之前
     */
    private int cleanStartTimeMinBefore = 30;

    /**
     * 清理结束时间，距离当前时间N分钟之前
     */
    private int cleanEndTimeMinBefore = 3;

    /**
     * 清理评论缓存任务开关
     */
    private boolean cleanCommentTaskSwitch = false;

    /**
     * 扫描数量
     */
    private int scanCount = 500;

    /**
     * 自定义表情包数量限制
     */
    private int diyEmotionLimit = 200;

    /**
     * 评论重复间隔时间间隔，往前推N毫秒，单位毫秒
     */
    private int commentRepetitionInterval = 1000;

    /**
     * 评论监控开关
     */
    private boolean commentMonitorSwitch = false;

    /**
     * 是否检查踢出
     */
    private boolean checkKickOut = false;

    private int printCommentCostLogLimit = 200;

    /**
     * 进房公告分页查询间隔，单位毫秒
     */
    private int queryEnterMsgInterval = 200;

    /**
     * 评论分页查询间隔，单位毫秒
     */
    private int queryCommentMsgInterval = 200;

    /**
     * 房间消息超时推送时间，单位秒
     */
    private int roomMsgTimeoutPushMillSec = 1;

    /**
     * 房间消息超时扫描时间，单位毫秒
     */
    private int roomMsgTimeoutScanMillSec = 1000;

    /**
     * 进房消息提前时间，单位秒
     */
    private int enterEndTimeDelaySec = 1;

    /**
     * 黑叶的配置
     */
    private HyCommentConfig hy = new HyCommentConfig();

    /**
     * pp评论配置
     */
    private PpCommentConfig pp = new PpCommentConfig();

    /**
     * 西米评论配置
     */
    private XmCommentConfig xm = new XmCommentConfig();

}
