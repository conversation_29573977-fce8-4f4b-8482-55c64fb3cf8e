package fm.lizhi.ocean.wave.comment.core.convert;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import fm.lizhi.ocean.wave.comment.core.config.CommentConfig;
import fm.lizhi.ocean.wave.comment.core.constants.CommentTypeMapping;
import fm.lizhi.ocean.wave.comment.core.model.dto.HyCommentExtraDTO;
import fm.lizhi.ocean.wave.comment.core.model.dto.TransientCommentDTO;
import fm.lizhi.ocean.wave.comment.core.model.dto.WealthLevelDTO;
import fm.lizhi.ocean.wave.comment.core.model.vo.ComeSourceVO;
import fm.lizhi.ocean.wave.comment.core.remote.bean.*;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wave.server.common.util.ContextUtils;
import hy.fm.lizhi.datacenter.comment.pp.bean.AnthorLevelBean;
import hy.fm.lizhi.datacenter.comment.pp.bean.FreshUserBean;
import hy.fm.lizhi.datacenter.comment.pp.bean.PpWealthLevelBean;
import hy.fm.lizhi.datacenter.comment.pp.bean.TransientComment;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.ArrayList;
import java.util.List;

/**
 * PP评论查询适配器
 *
 * <AUTHOR>
 */
@Mapper(
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        /* 未映射目标属性的报告策略 */
        unmappedTargetPolicy = ReportingPolicy.ERROR,
        /* 确定何时对bean映射的源属性值包含空检查 */
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface HySpecialActivityCommentConvert {

    HySpecialActivityCommentConvert I = Mappers.getMapper(HySpecialActivityCommentConvert.class);

    /**
     * 转换评论
     *
     * @param transientComment rpc相应体
     * @return 评论中间数据
     */
    @Mapping(target = "ppCommentExtra", ignore = true)
    @Mapping(target = "xmCommentExtra", ignore = true)
    @Mapping(target = "hyCommentExtra", ignore = true)
    @Mapping(target = "aspectRatio", ignore = true)
    @Mapping(target = "bizType", source = "biz")
    @Mapping(target = "simpleUser", expression =  "java(convertSimpleUser(transientComment.getSimpleUser()))")
    @Mapping(target = "wealthLevel", expression =  "java(convertWealthLevel(transientComment.getPpWealthLevelBean()))")
    @Mapping(target = "freshUser", expression =  "java(convertFreshUser(transientComment.getFreshUserBean()))")
    @Mapping(target = "medal", ignore = true)
    TransientCommentDTO convertComment(TransientComment transientComment);

    /**
     * 转换为黑叶特殊活动评论DTO
     *
     * @param transientComment 评论消息
     * @param commentConfig 评论配置
     * @return 黑叶特殊活动评论DTO
     */
    default TransientCommentDTO convert(TransientComment transientComment, CommentConfig commentConfig) {
        TransientCommentDTO transientCommentDTO = convertComment(transientComment);

        // 评论类型转换
        int commentType = CommentTypeMapping.getWaveCommentType(transientComment.getCommentType(),
                ContextUtils.getContext().getBusinessEvnEnum(),
                transientComment.getCommentTypeExtension());

        //构建简单勋章
        List<SimpleMedal> simpleMedals = convertSimpleMedals(transientComment.getSimpleMedals());
        //贵族等级
        ComeSourceVO comeSourceVO = convertComeSource(transientComment);
        List<MultiContentItem> multiContentItems = generateMultiContentItem(transientComment.getContentItemList());
        AnthorLevel anthorLevel = convertAnthorLevel(transientComment.getAnthorLevel());

        HyCommentExtraDTO commentExtraDTO = new HyCommentExtraDTO();
        commentExtraDTO.setSimpleMedals(simpleMedals==null ? new ArrayList<>() : simpleMedals);
        commentExtraDTO.setAnthorLevel(anthorLevel);
        commentExtraDTO.setContentItems(multiContentItems);
        commentExtraDTO.setComeSource(comeSourceVO);

        transientCommentDTO.setCommentType(commentType);
        transientCommentDTO.setHyCommentExtra(commentExtraDTO);
        return transientCommentDTO;
    }

    /**
     * 转换为简单用户
     *
     * @param simpleUser 简单用户
     * @return 结果
     */
    @Mapping(target = "nameColorsList", ignore = true)
    @Mapping(target = "roomVipUrls", ignore = true)
    @Mapping(target = "userRoomVipStatus", ignore = true)
    SimpleUser convertSimpleUser(hy.fm.lizhi.datacenter.comment.pp.bean.SimpleUser simpleUser);

    List<SimpleMedal> convertSimpleMedals(List<hy.fm.lizhi.datacenter.comment.pp.bean.SimpleMedal> simpleMedalList);

    /**
     * 多元素评论信息转换
     *
     * @param multiContentItemsList 多元素评论信息
     * @return 结果
     */
    default List<MultiContentItem> generateMultiContentItem(List<hy.fm.lizhi.datacenter.comment.pp.bean.MultiContentItem> multiContentItemsList) {
        List<MultiContentItem> contentItems = new ArrayList<>();
        if (CollectionUtils.isEmpty(multiContentItemsList)) {
            return contentItems;
        }
        for (hy.fm.lizhi.datacenter.comment.pp.bean.MultiContentItem contentItem : multiContentItemsList) {
            MultiContentItem item = new MultiContentItem();
            item.setAction(contentItem.getAction());
            item.setContentType(contentItem.getContentType());
            item.setTextColor(contentItem.getTextColor());
            if (contentItem.getContentType() == 1) {
                item.setText(contentItem.getContent());
            } else {
                item.setImg(contentItem.getContent());
            }
            contentItems.add(item);
        }
        return contentItems;
    }


    /**
     * 转换为主播等级
     *
     * @param anchorLevel 主播等级
     * @return 转换结果
     */
    AnthorLevel convertAnthorLevel(AnthorLevelBean anchorLevel);

    /**
     * 转换为评论来源
     *
     * @param transientComment 评论
     * @return 转换为评论来源
     */
    default ComeSourceVO convertComeSource(TransientComment transientComment) {
        ComeSourceVO comeSource = new ComeSourceVO();
        comeSource.setComeSource(Math.max(transientComment.getComeSource(), 0));
        comeSource.setComeSourceTgtUid(transientComment.getComeSourceTgtUid() <= 0 ? 0 : transientComment.getComeSourceTgtUid());
        return comeSource;
    }

    /**
     * 转换为新用户
     *
     * @param freshUser 新用户
     * @return 结果
     */
    @Mapping(target = "aspect", ignore = true)
    @Mapping(target = "url", ignore = true)
    FreshUser convertFreshUser(FreshUserBean freshUser);

    /**
     * 转换为财富等级
     *
     * @param wealthLevel 财富等级
     * @return 结果
     */
    WealthLevelDTO convertWealthLevel(PpWealthLevelBean wealthLevel);
}
