package fm.lizhi.ocean.wave.comment.core.extension.comment.xm;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wave.comment.core.config.CommentCommonConfig;
import fm.lizhi.ocean.wave.comment.core.config.CommentConfig;
import fm.lizhi.ocean.wave.comment.core.constants.WaveCommentType;
import fm.lizhi.ocean.wave.comment.core.convert.CommentMsgConvert;
import fm.lizhi.ocean.wave.comment.core.extension.comment.IGetCommentMsgProcessor;
import fm.lizhi.ocean.wave.comment.core.extension.medal.v2.*;
import fm.lizhi.ocean.wave.comment.core.model.bean.ICommentBean;
import fm.lizhi.ocean.wave.comment.core.model.bean.MsgUserExtraInfo;
import fm.lizhi.ocean.wave.comment.core.model.dto.TransientCommentDTO;
import fm.lizhi.ocean.wave.comment.core.model.dto.WealthLevelDTO;
import fm.lizhi.ocean.wave.comment.core.model.dto.XmCommentExtraDTO;
import fm.lizhi.ocean.wave.comment.core.model.vo.*;
import fm.lizhi.ocean.wave.comment.core.remote.bean.FreshUser;
import fm.lizhi.ocean.wave.comment.core.remote.bean.Medal;
import fm.lizhi.ocean.wave.comment.core.remote.bean.SimpleUser;
import fm.lizhi.ocean.wave.comment.core.remote.bean.XmCommentComBizExtra;
import fm.lizhi.ocean.wave.common.config.CommonProviderConfig;
import fm.lizhi.ocean.wave.common.util.ModelMapperUtils;
import fm.lizhi.ocean.wave.common.util.UrlUtils;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wave.server.common.vo.ResultVO;
import fm.lizhi.ocean.wave.user.api.MedalService;
import fm.lizhi.ocean.wave.user.api.UserService;
import fm.lizhi.ocean.wave.user.api.UserWealthLevelService;
import fm.lizhi.ocean.wave.user.result.FreshUserResult;
import fm.lizhi.ocean.wave.user.result.MedalResult;
import fm.lizhi.ocean.wave.user.result.WealthLevelResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Description: 获取评论差异化处理类
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class XmGetCommentMsgProcessor implements IGetCommentMsgProcessor {

    @Autowired
    private CommentConfig commentConfig;

    @Autowired
    private WealthMedalV2Node wealthMedalNode;

    @Autowired
    private UserMedalListV2Node userMedalListNode;

    @Autowired
    private FreshUserMedalV2Node freshUserMedalNode;

    @Autowired
    private UserWealthLevelService userWealthLevelService;

    @Autowired
    private MedalService medalService;

    @Autowired
    private CommonProviderConfig commonProviderConfig;

    @Autowired
    private UserRoleMedalV2Node userRoleMedalNode;

    @Autowired
    private UserService userService;

    @Override
    public BusinessEvnEnum getBusinessEnv() {
        return BusinessEvnEnum.XIMI;
    }

    @Override
    public CommentCommonConfig getCommentConfig() {
        return commentConfig.getXm();
    }


    @Override
    public MsgUserVO<XmMsgUserExtraInfo> createUserAndFillBizExtra(TransientCommentDTO transientComment) {
        MsgUserVO<XmMsgUserExtraInfo> userVO = new MsgUserVO<>();
        SimpleUser simpleUser = transientComment.getSimpleUser();
        if (simpleUser.getUserId() == transientComment.getUserId()) {
            XmMsgUserExtraInfo extraInfo = new XmMsgUserExtraInfo();
            // 昵称颜色
            extraInfo.setNameColorsList(simpleUser.getNameColorsList());
            // 用户的勋章
            extraInfo.setRoomVipUrls(simpleUser.getRoomVipUrls());
            extraInfo.setUserRoomVipStatus(simpleUser.getUserRoomVipStatus());
            userVO.setBizOtherExtra(extraInfo);
        }
        return userVO;
    }

    @Override
    public void adapterUserExtra(TransientCommentDTO transientComment, MsgUserVO<?> userVO) {
    }

    @Override
    public List<ICommentV2MedalNode> getBuildCommentMedalNodes() {
        List<ICommentV2MedalNode> nodes = new ArrayList<>(8);
        //不同的业务需要的勋章节点不同，且顺序不同
        //- 角色勋章
        //- 财富等级信息
        //- 新用户勋章
        //- 勋章列表
        nodes.add(userRoleMedalNode);
        nodes.add(wealthMedalNode);
        nodes.add(freshUserMedalNode);
        nodes.add(userMedalListNode);
        return nodes;
    }

    @Override
    public ResultVO<TransientCommentDTO> resetCommentUserProperty(TransientCommentDTO transientComment) {
        //- 财富等级信息
        //- 新用户勋章
        //- 勋章列表
        //只针对系统公告评论做用户属性数据重置
        boolean needReset = transientComment.getCommentType() == WaveCommentType.SYSTEM_NOTICE_COMMENT && transientComment.getOperatorUserId() > 0;
        if (!needReset) {
            return ResultVO.success(transientComment);
        }

        //拷贝数据
        TransientCommentDTO targetComment = ModelMapperUtils.MODEL_MAPPER.map(transientComment, TransientCommentDTO.class);
        XmCommentExtraDTO xmCommentExtra = targetComment.getXmCommentExtra();

        //查询勋章列表信息
        Result<List<MedalResult>> medalListRes = medalService.getMedalListFromCache(targetComment.getUserId());
        //查询财富等级信息
        Result<WealthLevelResult> wealthRes = userWealthLevelService.getWealthLevelFromCache(targetComment.getUserId());
        //是否是新用户查询
        Result<FreshUserResult> freshUserResult = userService.isFreshUserByCache(targetComment.getUserId());

        List<Medal> medals = CommentMsgConvert.I.convertMedalList(medalListRes.target());
        WealthLevelDTO wealthLevelDTO = CommentMsgConvert.I.convertWealthLevel(wealthRes.target());
        FreshUser freshUser = CommentMsgConvert.I.convertFreshUser(freshUserResult.target());
        targetComment.setMedal(medals);
        targetComment.setWealthLevel(wealthLevelDTO);
        targetComment.setFreshUser(freshUser);
        targetComment.setXmCommentExtra(xmCommentExtra);
        return ResultVO.success(targetComment);
    }

    @Override
    public void fillEnterCommentMedal(TransientCommentDTO transientComment) {
    }

    @Override
    public void fillCommentExtraInfo(CommentVO commentVO, TransientCommentDTO transientComment) {
        XmCommentExtraDTO xmCommentExtra = transientComment.getXmCommentExtra();
        @SuppressWarnings("unchecked")
        CommentExtraInfoVO<XmCommentExtraInfoVO> extra = (CommentExtraInfoVO<XmCommentExtraInfoVO>) commentVO.getExtra();
        if (extra == null) {
            extra = new CommentExtraInfoVO<>();
        }

        XmCommentExtraInfoVO bizExtra = extra.getBizOtherExtra();
        if (bizExtra == null) {
            bizExtra = new XmCommentExtraInfoVO();
        }

        //构建设置黑叶特有的评论扩展属性
        bizExtra.setTailEffect(xmCommentExtra.getTailEffect());
        if (CollectionUtils.isNotEmpty(xmCommentExtra.getAtUsers())) {
            bizExtra.setAtUsers(transientComment.getXmCommentExtra().getAtUsers().stream()
                    .map(v -> new AtUserVO().setUserId(v.getUserId()).setName(v.getName()))
                    .collect(Collectors.toList())
            );
        }

        extra.setBizOtherExtra(bizExtra);
        commentVO.setExtra(extra);
        commentVO.setXmCommonBizExtra(
                XmCommentComBizExtra.builder().anonymous(transientComment.getXmCommentExtra().isAnonymous()).build());
    }

    @Override
    public List<ICommentBean> processSpecialEnterMsg(List<TransientCommentDTO> transientCommentDTOList, List<CommentVO> commentVOList) {
        return new ArrayList<>(commentVOList);
    }


    /**
     * 构建财富等级图片地址
     *
     * @param wealthInfo 财富等级信息
     * @return 图片地址
     */
    private String buildWealthUrl(WealthLevelDTO wealthInfo) {
        if (wealthInfo.getCover().startsWith("http")) {
            return wealthInfo.getCover();
        }

        String cdn = commonProviderConfig.getBusinessConfig(BusinessEvnEnum.XIMI.getAppId()).getCdnHost();
        return UrlUtils.addCdnHost(cdn, wealthInfo.getCover());
    }
}
