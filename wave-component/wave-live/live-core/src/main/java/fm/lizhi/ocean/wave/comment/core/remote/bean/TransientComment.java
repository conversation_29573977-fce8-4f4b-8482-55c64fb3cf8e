package fm.lizhi.ocean.wave.comment.core.remote.bean;

import fm.lizhi.ocean.wave.comment.core.model.dto.WealthLevelDTO;
import fm.lizhi.ocean.wave.comment.core.model.vo.ComeSourceVO;
import fm.lizhi.ocean.wave.comment.core.model.vo.TailEffectVo;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 评论信息
 *
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Deprecated
public class TransientComment {
    /**
     * 评论id
     */
    private long commentId;
    /**
     * 评论用户id
     */
    private long userId;

    /**
     * 触发评论场景的用户ID
     */
    private long operatorUserId;

    /**
     * 评论内容
     */
    private String content;
    /**
     * 评论时间
     */
    private long time;
    /**
     * ios颜色
     */
    private String iosColor;
    /**
     * android颜色
     */
    private String androidColor;
    /**
     * 用户角色
     */
    private List<Integer> userRoles;
    /**
     * 用户信息
     */
    private SimpleUser simpleUser;
    /**
     * 群马甲
     */
    @Deprecated
    private QunVest qunVest;
    /**
     * 用户财富等级
     */
    @Deprecated
    private SimpleUserWealthLevel level;
    /**
     * 用户守护勋章
     */
    @Deprecated
    private GuardUserBadge guardUserBadge;
    /**
     * 主播守护勋章
     */
    @Deprecated
    private GuardNjBadge guardNjBadge;
    /**
     * 评论类型
     */
    private int commentType;
    /**
     * 评论类型扩展
     */
    private int commentTypeExtension;
    /**
     * 图片高度
     */
    private int imageHeight;
    /**
     * 图片宽度
     */
    private int imageWidth;
    /**
     * 图片大小
     */
    private int imageSize;
    /**
     * 图片url
     */
    private String imageUrl;
    /**
     * 图片宽高比
     */
    private float aspectRatio;
    /**
     * 是否原图
     */
    private boolean imageOriginal;
    /**
     * 用户佩戴的勋章集合
     */
    private List<SimpleMedal> simpleMedals;
    /**
     * 金牌主播勋章url
     */
    @Deprecated
    private String goldNjBadgeUrl;
    /**
     * 独家主播勋章url
     */
    @Deprecated
    private String exclusiveBadgeUrl;
    /**
     * 明星(主播)等级
     */
    @Deprecated
    private SimpleUserStarLevel starLevel;
    /**
     * 粉丝等级勋章
     */
    @Deprecated
    private FansLevelBadge fansLevelBadge;
    /**
     * 表情id
     */
    private long emotionId;
    /**
     * 停止图片序号
     */
    private int repeatStopImageIndex;
    /**
     * 指定发送用户
     */
    private List<Long> toUser;
    /**
     * 评论样式ID
     */
    private long styleId;
    /**
     * 子评论类型
     */
    private int bizType;
    /**
     * 财富等级
     */
    private WealthLevelDTO wealthLevel;
    /**
     * 新用户
     */
    private FreshUser freshUser;
    /**
     * 贵族等级
     */
    private VipLevel vipLevel;
    /**
     * 大客户勋章
     */
    private VipMedal vipMedal;
    /**
     * 用户勋章列表（拥有的）
     */
    private List<Medal> medal;
    /**
     * 评论中图片数据
     */
    @Deprecated
    private List<ImageData> imageData;
    /**
     * 房间互动卡片
     */
    @Deprecated
    private RoomInteractionCard roomInteractionCard;
    /**
     * 关系拍记录
     */
    @Deprecated
    private RelationPatRecord relationPatRecord;
    /**
     * 宝箱信息
     */
    @Deprecated
    private TreasureBox treasureBox;
    /**
     * 是否潜力用户
     */
    @Deprecated
    private boolean potential;
    /**
     * 目标用户userId
     */
    @Deprecated
    private long targetUserId;
    /**
     * 主播等级勋章
     */
    private AnthorLevel anthorLevel;

    /**
     * 成长关系等级勋章.pp才有
     */
    private GrowRelationLevel growRelationLevel;

    /**
     * 成长关系列表，发评论的用户和多人的成长关系，先带着走，后面再选出和查询的用户的成长关系，设置到growRelationLevel字段
     */
    private List<GrowRelationLevel> growRelationLevelList;

    /**
     * 守护勋章，PP独有
     */
    private GuardMedal guardMedal;

    /**
     * 评论标记，0：正常评论，1：空评论
     */
    private int commentMark;

    /**
     * 尾灯 黑叶独有的
     */
    private TailEffectVo tailEffect;

    /**
     * 多元素的评论内容
     *  黑叶独有的
     */
    private List<MultiContentItem> contentItems;

    /**
     * 曲库点唱歌曲信息
     */
    private SongInfo songInfo;

    /**
     * 房间精选歌单信息
     */
    private RoomPlaylistCommentCardBean roomPlaylistCommentCardBean;

    /**
     * 房间随机轮盘信息
     */
    private RoomRouletteWheelCommentCardBean rouletteWheelCommentCardBean;

    /**
     * 是否蒙面用户 0：否 1：是
     */
    private int maskStatus;

    /**
     * 新用户偏好信息
     */
    private FreshUserInterestBean freshUserInterestBean;

    /**
     * 艾特的用户信息
     */
    private List<AtUser> atUsers;

    /**
     * 进房来源 黑叶独有
     */
    private ComeSourceVO comeSource;

    /**
     * 是否匿名
     */
    private boolean anonymous;

    /**
     * 作品内容信息
     */
    private FeedContentInfoBean feedContentInfoBean;
}
