package fm.lizhi.ocean.wave.comment.core.extension.comment;

import fm.lizhi.ocean.wave.comment.core.config.CommentCommonConfig;
import fm.lizhi.ocean.wave.comment.core.extension.medal.ICommentMedalNode;
import fm.lizhi.ocean.wave.comment.core.manager.EnterNoticeExtra;
import fm.lizhi.ocean.wave.comment.core.model.vo.LiveCommentVO;
import fm.lizhi.ocean.wave.comment.core.model.vo.UserVO;
import fm.lizhi.ocean.wave.comment.core.remote.bean.*;
import fm.lizhi.ocean.wave.common.extension.BusinessEnvAwareProcessor;
import fm.lizhi.ocean.wave.server.common.vo.ResultVO;

import java.util.List;

/**
 * Description: 获取直播间消息处理类（评论和进房公告）
 */
@Deprecated
public interface IGetLiveMsgProcessor extends BusinessEnvAwareProcessor {

    @Override
    default Class<? extends BusinessEnvAwareProcessor> getBaseBusinessProcessor() {
        return IGetLiveMsgProcessor.class;
    }


    /**
     * 适配用户信息
     */
    void adaptUserVo(TransientComment transientComment, UserVO userVO);

    /**
     * 获取业务公共评论配置
     *
     * @return 评论配置
     */
    CommentCommonConfig getCommentConfig();

    /**
     * 获取评论勋章节点
     *
     * @return 勋章节点
     */
    List<ICommentMedalNode> getBuildCommentMedalNodes();

    /**
     * 获取进房公告勋章节点
     *
     * @return 勋章节点
     */
    List<ICommentMedalNode> getBuildEnterNoticeMedalNodes();

    /**
     * 是否有权限查询进房公告
     */
    boolean isCanGetEnterNotice(long liveId);

    /**
     * 重置评论用户属性信息
     *
     * @param transientComment 评论信息
     * @return 结果
     */
    ResultVO<TransientComment> resetCommentUserProperty(TransientComment transientComment);

    /**
     * 是否是进房评论
     *
     * @param transientComment 评论信息
     * @return true 是进房评论
     */
    boolean isEnterComment(TransientComment transientComment);

    /**
     * 补充进房公告勋章
     *
     * @param noticeEntry 进房公告消息
     * @return 结果
     */
    ResultVO<EnterNoticeEntry> fillEnterNoticeMedal(EnterNoticeEntry noticeEntry);

    /**
     * 是否展示按钮
     *
     * @param liveId           直播节目ID
     * @param enterNoticeEntry 进房公告
     * @param userId           请求的用户id
     * @return
     */
    boolean isShowButton(long liveId, long userId, EnterNoticeEntry enterNoticeEntry);

    /**
     * 过滤进房公告特效
     *
     * @param enterNoticeEntries 用户ID
     * @return 过滤后的进房公告
     */
    List<EnterNoticeEntry> filterHideEnterNotice(List<EnterNoticeEntry> enterNoticeEntries);

    /**
     * 构建曲库点唱歌曲信息
     *
     * @param songInfo 歌曲信息
     * @return 结果
     */
    void fillSongInfoVO(LiveCommentVO liveCommentVO, SongInfo songInfo, int commentType);

    /**
     * 构建房间歌单信息
     *
     * @param roomPlaylistCommentCardBean 房间歌单信息
     * @return 结果
     */
    void fillRoomPlaylistVO(LiveCommentVO liveCommentVO, RoomPlaylistCommentCardBean roomPlaylistCommentCardBean, int commentType);

    /**
     * 大主播屏蔽座驾
     *
     * @param njId 主播ID
     * @return 结果
     */
    boolean isBigStarNjHideMount(long njId);
    /**
     * 构建房间轮盘信息
     *
     */
    void fillRoomRouletteWheelVO(LiveCommentVO liveCommentVO, RoomRouletteWheelCommentCardBean rouletteWheelCommentCardBean, int commentType);

    /**
     * 构建新用户偏好信息
     *
     */
    void fillFreshUserInterestInfoVO(LiveCommentVO liveCommentVO, FreshUserInterestBean freshUserInterestBean, int commentType);

    /**
     * 获取进房公告额外信息
     * @param userId
     * @param enterNoticeEntry
     * @return
     */
    EnterNoticeExtra getEnterNoticeExtra(long userId, EnterNoticeEntry enterNoticeEntry);

    /**
     * 作品内容信息
     *
     */
    void fillFeedContentInfoVO(LiveCommentVO liveCommentVO, FeedContentInfoBean feedContentInfoBean, int commentType);
}
