<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>fm.lizhi.ocean.wave</groupId>
        <artifactId>wave-activity</artifactId>
        <version>1.5.26</version>
    </parent>

    <artifactId>activity-core</artifactId>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencies>
        <!-- ====================        创作者内部依赖        ==================== -->

        <dependency>
            <groupId>fm.lizhi.ocean.wave</groupId>
            <artifactId>wave-common</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>fm.lizhi.ocean.wave</groupId>
            <artifactId>amusement-api</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>fm.lizhi.ocean.wave</groupId>
            <artifactId>amusement-export-api</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>fm.lizhi.ocean.wave</groupId>
            <artifactId>amusement-export-pojo-api</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>fm.lizhi.ocean.wave</groupId>
            <artifactId>live-api</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>fm.lizhi.ocean.wave</groupId>
            <artifactId>permission-api</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>fm.lizhi.ocean.wave</groupId>
            <artifactId>user-api</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>fm.lizhi.ocean.wave</groupId>
            <artifactId>live-core</artifactId>
            <version>${project.version}</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>fm.lizhi.ocean.wave</groupId>
            <artifactId>activity-api</artifactId>
            <version>${project.version}</version>
        </dependency>


        <!-- ====================        基础架构的依赖        ==================== -->
        <!-- 大部分由wave-common引入, 不足部分在这里补全 -->



        <!-- ====================        第三方框架依赖        ==================== -->

        <!-- ====================         交易服务            ==================== -->
        <dependency>
            <groupId>fm.lizhi</groupId>
            <artifactId>lz-payment-api</artifactId>
        </dependency>

        <!-- ====================        风控服务依赖        ==================== -->


        <!-- ====================        黑叶服务依赖        ==================== -->
        <dependency>
            <groupId>fm.lizhi.hy</groupId>
            <artifactId>lz-hy-content-api</artifactId>
        </dependency>
        <dependency>
            <groupId>fm.lizhi.hy</groupId>
            <artifactId>lz-hy-gift-api</artifactId>
        </dependency>
        <dependency>
            <groupId>fm.lizhi.hy</groupId>
            <artifactId>lz-hy-room-api</artifactId>
        </dependency>
        <dependency>
            <groupId>fm.lizhi.hy</groupId>
            <artifactId>lz-hy-activity-api</artifactId>
        </dependency>
        <dependency>
            <groupId>fm.hy.family</groupId>
            <artifactId>lz-hy-family-api</artifactId>
        </dependency>
        <dependency>
            <groupId>fm.lizhi.ocean.wave</groupId>
            <artifactId>wave-relocation-hy-api</artifactId>
        </dependency>
        <dependency>
            <groupId>fm.lizhi.amusement.commons</groupId>
            <artifactId>lz-commons-structs-blackwidow-api</artifactId>
        </dependency>

        <!-- ====================        pp服务依赖        ==================== -->
<!--        <dependency>-->
<!--            <groupId>fm.lizhi.pp</groupId>-->
<!--            <artifactId>lz-pp-activity-api</artifactId>-->
<!--        </dependency>-->
        <dependency>
            <groupId>fm.lizhi.pp</groupId>
            <artifactId>lz-pp-vip-api</artifactId>
        </dependency>
        <dependency>
            <groupId>fm.lizhi.ocean.wave</groupId>
            <artifactId>wave-relocation-pp-api</artifactId>
        </dependency>

        <!-- ====================        西米服务依赖        ==================== -->
        <dependency>
            <groupId>fm.lizhi.ocean.wave</groupId>
            <artifactId>wave-relocation-xm-api</artifactId>
        </dependency>
        <dependency>
            <groupId>fm.lizhi.xm</groupId>
            <artifactId>lz-xm-content-api</artifactId>
        </dependency>

        <dependency>
            <groupId>fm.lizhi.xm</groupId>
            <artifactId>lz-xm-common-util</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>lz-app-client-protobuf</artifactId>
                    <groupId>fm.lizhi.app</groupId>
                </exclusion>
            </exclusions>
        </dependency>

    </dependencies>

</project>