package fm.lizhi.ocean.wave.activity.core.api.impl;

import api.activity.api.ServiceOrderService;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.commons.service.client.rcode.GeneralRCode;
import fm.lizhi.ocean.wave.activity.core.remote.param.CheckPermissionRequest;
import fm.lizhi.ocean.wave.activity.core.remote.service.GiftServiceOrderRemote;
import groovy.util.logging.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class ServiceOrderServiceImpl implements ServiceOrderService {

    @Autowired
    private GiftServiceOrderRemote giftServiceOrderRemote;

    @Override
    public Result<Boolean> checkUserHasServiceOrderPermission(long userId, long liveId) {
        CheckPermissionRequest param = new CheckPermissionRequest();
        param.setUserId(userId);
        param.setLiveId(liveId);
        Result<Boolean> result = giftServiceOrderRemote.checkPermission(param);
        if(result.rCode() != 0){
            return new Result<>(result.rCode(), false);
        }
        return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, result.target());
    }
}