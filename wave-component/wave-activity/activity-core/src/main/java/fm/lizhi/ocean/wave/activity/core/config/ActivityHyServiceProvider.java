package fm.lizhi.ocean.wave.activity.core.config;

import fm.lizhi.amusement.commons.blackwidow.api.BwOrderService;
import fm.lizhi.hy.content.api.BannerManagementService;
import fm.lizhi.hy.content.api.HotWordsManagementService;
import fm.lizhi.hy.content.api.PendantService;
import fm.lizhi.hy.gift.idl.effect.api.GiftServiceOrderService;
import fm.lizhi.hy.rank.api.GlobalLiveRoomRankingService;
import fm.lizhi.hy.rank.api.LiveRoomRankingService;
import hy.fm.lizhi.live.data.api.LiveStateService;
import fm.lizhi.ocean.wave.common.auto.route.common.annotation.ScanBusinessProviderAPI;
import org.springframework.context.annotation.Configuration;

@Configuration
@ScanBusinessProviderAPI(values = {
        @ScanBusinessProviderAPI.RegisterProvider(interfaceClass = HotWordsManagementService.class),
        @ScanBusinessProviderAPI.RegisterProvider(interfaceClass = GlobalLiveRoomRankingService.class),
        @ScanBusinessProviderAPI.RegisterProvider(interfaceClass = PendantService.class),
        @ScanBusinessProviderAPI.RegisterProvider(interfaceClass = LiveRoomRankingService.class),
        @ScanBusinessProviderAPI.RegisterProvider(interfaceClass = LiveStateService.class),
        @ScanBusinessProviderAPI.RegisterProvider(interfaceClass = BwOrderService.class),
        @ScanBusinessProviderAPI.RegisterProvider(interfaceClass = BannerManagementService.class),
        @ScanBusinessProviderAPI.RegisterProvider(interfaceClass = GiftServiceOrderService.class),
}
)
public class ActivityHyServiceProvider {

}
