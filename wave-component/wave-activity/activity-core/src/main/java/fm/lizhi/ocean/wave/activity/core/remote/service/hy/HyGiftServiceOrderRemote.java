package fm.lizhi.ocean.wave.activity.core.remote.service.hy;

import fm.lizhi.commons.service.client.rcode.GeneralRCode;
import fm.lizhi.hy.gift.idl.effect.api.GiftServiceOrderService;
import fm.lizhi.ocean.wave.activity.core.remote.param.CheckPermissionRequest;
import fm.lizhi.ocean.wave.activity.core.remote.service.GiftServiceOrderRemote;
import fm.lizhi.ocean.wave.common.auto.route.common.facade.RemoteServiceInvokeFacade;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import fm.lizhi.commons.service.client.pojo.Result;

/**
 * 服务单接口
 */
@Component
@Slf4j
public class HyGiftServiceOrderRemote extends RemoteServiceInvokeFacade implements GiftServiceOrderRemote {

    @Autowired
    private GiftServiceOrderService giftServiceOrderService;

    @Override
    public boolean support(BusinessEvnEnum evnEnum) {
        return BusinessEvnEnum.HEI_YE == evnEnum;
    }

    @Override
    public Result<Boolean> checkPermission(CheckPermissionRequest param) {
        long liveId = param.getLiveId();
        Long userId = param.getUserId();
        Result<Boolean> result = giftServiceOrderService.checkPermission(liveId, userId);

        if (result.rCode() != 0) {
            log.info("checkPermission fail, userId:{}, liveId:{}, rCode:{}", userId, liveId, result.rCode());
            return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, false);
        }
        Boolean hasPermission = result.target();
        log.info("HyGiftServiceOrderRemote , liveId:{},userId:{} hasPermission:{}", liveId, userId, hasPermission);
        return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, hasPermission);
    }
}