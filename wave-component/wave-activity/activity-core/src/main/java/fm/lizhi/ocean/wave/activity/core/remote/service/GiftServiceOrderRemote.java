package fm.lizhi.ocean.wave.activity.core.remote.service;


import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wave.activity.core.remote.param.CheckPermissionRequest;
import fm.lizhi.ocean.wave.common.auto.route.common.facade.IBaseRemoteServiceInvoker;

public interface GiftServiceOrderRemote extends IBaseRemoteServiceInvoker {
    Result<Boolean> checkPermission(CheckPermissionRequest param);
}
