package fm.lizhi.ocean.wave.amusement.core.dao.redis;

import fm.lizhi.common.datastore.redis.client.RedisClient;
import fm.lizhi.commons.util.DateUtil;
import fm.lizhi.ocean.wave.common.manager.RedisClientManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2023/9/20
 */
@Slf4j
@Component
public class AmusementRedisDao {

    @Autowired
    private RedisClientManager redisClientManager;

    /**
     * 2分钟秒数
     */
    private final int TWO_MIN_SEC = 120;

    /**
     * 增加并返回操作次数
     *
     * @param appId  应用ID
     * @param liveId 直播节目ID
     * @param userId 用户ID
     * @param type   操作时的时间
     * @return 累计操作次数
     */
    public long increaseOperationCount(int appId, long liveId, long userId, int type) {
        RedisClient redisClient = redisClientManager.redisCacheClient();
        String time = DateUtil.formatCurrentTime("HHmm");
        String key = getLimitingKey(appId, liveId, userId, type, time);
        Long count = redisClient.incr(key);
        redisClient.expire(key, TWO_MIN_SEC);
        return count;
    }


    /**
     * 获取限制频率key
     *
     * @param appId  应用ID
     * @param liveId 直播节目ID
     * @param userId 用户ID
     * @param time   时间
     * @return 结果
     */
    public String getLimitingKey(int appId, long liveId, long userId, int type, String time) {
        return AmusementRedisKey.OPERATION_LIMITING_STR.getKey(appId, liveId, userId, type, time);
    }

}
