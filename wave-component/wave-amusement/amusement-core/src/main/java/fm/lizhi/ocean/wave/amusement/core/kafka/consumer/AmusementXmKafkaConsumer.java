package fm.lizhi.ocean.wave.amusement.core.kafka.consumer;

import com.alibaba.fastjson.JSONObject;
import fm.lizhi.common.kafka.common.strategy.AutoOffsetReset;
import fm.lizhi.common.kafka.ioc.api.annotation.KafkaHandler;
import fm.lizhi.common.kafka.ioc.api.annotation.KafkaListener;
import fm.lizhi.common.kafka.ioc.api.annotation.SubscriptionOn;
import fm.lizhi.common.kafka.ioc.api.strategy.annotation.AutoOffsetResetStrategy;
import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.ocean.wave.amusement.core.constant.VoiceCallBizTypeEnum;
import fm.lizhi.ocean.wave.amusement.core.constant.VoiceCallMatchModelEnum;
import fm.lizhi.ocean.wave.amusement.core.manager.AmusementPushManager;
import fm.lizhi.ocean.wave.amusement.core.model.vo.PushCallMatchResultEventVo;
import fm.lizhi.ocean.wave.amusement.core.model.vo.PushCallStatusChangeEventVo;
import fm.lizhi.ocean.wave.amusement.core.model.vo.VoiceCallUserEventVo;
import fm.lizhi.ocean.wave.common.config.CommonProviderConfig;
import fm.lizhi.ocean.wave.common.util.JsonUtils;
import fm.lizhi.ocean.wave.common.util.KafkaMsgUtils;
import fm.lizhi.ocean.wave.common.util.UrlUtils;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wave.server.common.util.ContextUtils;
import fm.lizhi.xm.social.bean.VoiceCallAccessEventMsg;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import xm.fm.lizhi.live.amusement.pp.dto.AmusementPushEvent;
import xm.fm.lizhi.live.amusement.pp.dto.WaitingUserEvent;

/**
 * Description:
 *
 * <AUTHOR>
 * Created in  2023/5/19
 */
@Slf4j
@Component
@KafkaListener(clusterNamespace = "xm-kafka250-bootstrap-server")
@SubscriptionOn(enable = "${xm.kafka.consumer.enable}")
public class AmusementXmKafkaConsumer {

    @Autowired
    private AmusementPushManager amusementPushManager;

    @Autowired
    private CommonProviderConfig commonProviderConfig;

    @KafkaHandler(topic = "lz_topic_xm_amusement_push_msg",
            group = "lz_ocean_wave_amusement_push_msg_group")
    @AutoOffsetResetStrategy(AutoOffsetReset.Latest)
    public void handleAmusementSync(String body) {
        String msg = null;

        try {
            msg = KafkaMsgUtils.removeKafkaMsgCICDHead(body);
            AmusementPushEvent event = JsonUtil.loads(msg, AmusementPushEvent.class);
            amusementPushManager.pushAmusementSync(event.getLiveId(), BusinessEvnEnum.XIMI.getAppId());
        } catch (Exception e) {
            log.error("AmusementXmKafkaConsumer handleAmusementData json parse error, msg:{}, orgMsg:{}", msg, body, e);
        }

    }

    @KafkaHandler(topic = "lz_topic_xm_waiting_user_msg",
            group = "lz_ocean_wave_amusement_push_msg_group")
    @AutoOffsetResetStrategy(AutoOffsetReset.Latest)
    public void handleAmusementApply(String body) {
        String msg = null;
        try {
            msg = KafkaMsgUtils.removeKafkaMsgCICDHead(body);
            WaitingUserEvent event = JsonUtil.loads(msg, WaitingUserEvent.class);
            amusementPushManager.pushAmusementApplySync(event.getLiveId(), BusinessEvnEnum.XIMI.getAppId());
        } catch (Exception e) {
            log.error("AmusementXmKafkaConsumer handleAmusementApply json parse error,  msg:{}, orgMsg:{}", msg, body, e);
        }
    }

    /**
     * 语音通话匹配结果
     *
     * @param body 消息体
     */
    @KafkaHandler(topic = "xm_topic_voice_call_match_result",
            group = "lz_ocean_wave_amusement_push_msg_group")
    @AutoOffsetResetStrategy(AutoOffsetReset.Latest)
    public void voiceCallMatchResult(String body) {
        try {
            String msg = KafkaMsgUtils.removeKafkaMsgCICDHead(body);
            log.info("xm.voiceCallMatchResult msg:{}", msg);
            PushCallMatchResultEventVo event = JsonUtil.loads(msg, PushCallMatchResultEventVo.class);
            ContextUtils.setBusinessEvnEnum(BusinessEvnEnum.XIMI);
            fillData(event);
            amusementPushManager.pushVoiceCallMatchResult(event, BusinessEvnEnum.XIMI.getAppId());
        } catch (Exception e) {
            log.error("AmusementXmKafkaConsumer.voiceCallMatchResult json parse error,  body:{}", body, e);
        }
    }

    /**
     * 语音通话状态变更事件
     *
     * @param body 消息体
     */
    @KafkaHandler(topic = "xm_topic_call_status_event",
            group = "lz_ocean_wave_amusement_push_msg_group")
    @AutoOffsetResetStrategy(AutoOffsetReset.Latest)
    public void handleCallStatusChangeEvent(String body) {
        try {
            String msg = KafkaMsgUtils.removeKafkaMsgCICDHead(body);
            log.info("xm.handleCallStatusChangeEvent msg:{}", msg);
            ContextUtils.setBusinessEvnEnum(BusinessEvnEnum.XIMI);
            PushCallStatusChangeEventVo eventVo = JsonUtils.fromJsonString(msg, PushCallStatusChangeEventVo.class);
            int businessType = eventVo.getBusinessType();
            if (businessType == 3) {
                //免费通话
                amusementPushManager.pushMatchStatusEventSync(eventVo, BusinessEvnEnum.XIMI.getAppId());
            } else {
                amusementPushManager.pushCallStatusChangeEventSync(msg, BusinessEvnEnum.XIMI.getAppId());
            }
        } catch (Exception e) {
            log.error("AmusementXmKafkaConsumer.handleCallStatusChangeEvent json parse error,  body:{}", body, e);
        }
    }


    /**
     * 语音通话库存变更事件
     *
     * @param body 消息体
     */
    @KafkaHandler(topic = "xm_topic_voice_call_access_event",
            group = "lz_ocean_wave_amusement_push_msg_group")
    @AutoOffsetResetStrategy(AutoOffsetReset.Latest)
    public void handleVoiceCallAccessEvent(String body) {
        try {
            String msg = KafkaMsgUtils.removeKafkaMsgCICDHead(body);
            log.info("xm.handleVoiceCallAccessEvent msg:{}", msg);
            ContextUtils.setBusinessEvnEnum(BusinessEvnEnum.XIMI);
            VoiceCallAccessEventMsg event = JSONObject.parseObject(msg, VoiceCallAccessEventMsg.class);
            amusementPushManager.pushVoiceCallAccessEventSync(event, BusinessEvnEnum.XIMI.getAppId());
        } catch (Exception e) {
            log.error("AmusementXmKafkaConsumer.handleVoiceCallAccessEvent json parse error,  body:{}", body, e);
        }
    }

    /**
     * 填充数据
     *
     * @param event 事件消息
     */
    private void fillData(PushCallMatchResultEventVo event) {
        event.setCallBizType(VoiceCallBizTypeEnum.bizValue2Wave(event.getCallBizType()).getWaveType());
        event.setMode(VoiceCallMatchModelEnum.bizValue2Wave(event.getMode()).getModel());
        VoiceCallUserEventVo targetUser = event.getTargetUser();
        if (targetUser != null) {
            String cdnHost = commonProviderConfig.getBusinessConfig(BusinessEvnEnum.XIMI.getAppId()).getCdnHost();
            targetUser.setPortrait(UrlUtils.addCdnHost(cdnHost, targetUser.getPortrait()));
            event.setTargetUser(targetUser);
        }
    }

}
