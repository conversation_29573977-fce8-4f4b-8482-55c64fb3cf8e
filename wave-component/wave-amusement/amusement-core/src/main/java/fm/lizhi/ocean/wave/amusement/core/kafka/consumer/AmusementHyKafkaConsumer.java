package fm.lizhi.ocean.wave.amusement.core.kafka.consumer;

import fm.lizhi.common.kafka.common.strategy.AutoOffsetReset;
import fm.lizhi.common.kafka.ioc.api.annotation.KafkaHandler;
import fm.lizhi.common.kafka.ioc.api.annotation.KafkaListener;
import fm.lizhi.common.kafka.ioc.api.annotation.SubscriptionOn;
import fm.lizhi.common.kafka.ioc.api.strategy.annotation.AutoOffsetResetStrategy;
import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.live.amusement.hy.dto.AmusementPushEvent;
import fm.lizhi.live.amusement.hy.dto.WaitingUserEvent;
import fm.lizhi.ocean.wave.amusement.core.manager.AmusementPushManager;
import fm.lizhi.ocean.wave.amusement.core.model.vo.PushInviteOnSeatEventVo;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wave.server.common.util.ContextUtils;
import fm.lizhi.ocean.wave.common.util.KafkaMsgUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * Description:
 *
 * <AUTHOR>
 * Created in  2023/5/19
 */
@Slf4j
@Component
@KafkaListener(clusterNamespace = "hy-kafka250-bootstrap-server")
@SubscriptionOn(enable = "${hy.kafka.consumer.enable}")
public class AmusementHyKafkaConsumer {

    @Autowired
    private AmusementPushManager amusementPushManager;

    @KafkaHandler(topic = "lz_topic_hy_amusement_push_msg",
            group = "lz_ocean_wave_amusement_push_msg_group")
    @AutoOffsetResetStrategy(AutoOffsetReset.Latest)
    public void handleAmusementSync(String body) {
        String msg = null;

        try {
            msg = KafkaMsgUtils.removeKafkaMsgCICDHead(body);
            AmusementPushEvent event = JsonUtil.loads(msg, AmusementPushEvent.class);
            amusementPushManager.pushAmusementSync(event.getLiveId(), BusinessEvnEnum.HEI_YE.getAppId());
        } catch (Exception e) {
            log.error("handleAmusementData json parse error, msg:{}, orgMsg:{}", msg, body, e);
        }

    }

    @KafkaHandler(topic = "lz_topic_hy_waiting_user_msg",
            group = "lz_ocean_wave_amusement_push_msg_group")
    @AutoOffsetResetStrategy(AutoOffsetReset.Latest)
    public void handleAmusementApply(String body) {
        String msg = null;
        try {
            msg = KafkaMsgUtils.removeKafkaMsgCICDHead(body);
            WaitingUserEvent event = JsonUtil.loads(msg, WaitingUserEvent.class);
            amusementPushManager.pushAmusementApplySync(event.getLiveId(), BusinessEvnEnum.HEI_YE.getAppId());
        } catch (Exception e) {
            log.error("handleAmusementApply json parse error,  msg:{}, orgMsg:{}", msg, body, e);
        }
    }

    /**
     * 邀请上麦事件
     * @param body 消息体
     */
    @KafkaHandler(topic = "hy_topic_invite_on_seat_event",
            group = "lz_ocean_wave_amusement_push_msg_group")
    @AutoOffsetResetStrategy(AutoOffsetReset.Latest)
    public void handleInviteOnSeatEvent(String body) {
        try {
            String msg = KafkaMsgUtils.removeKafkaMsgCICDHead(body);
            log.info("hy.inviteOnSeatEvent msg:{}", msg);
            PushInviteOnSeatEventVo event = JsonUtil.loads(msg, PushInviteOnSeatEventVo.class);
            ContextUtils.setBusinessEvnEnum(BusinessEvnEnum.HEI_YE);
            amusementPushManager.pushInviteOnSeatEvent(event, BusinessEvnEnum.HEI_YE.getAppId());
        } catch (Exception e) {
            log.error("AmusementPpKafkaConsumer.voiceCallMatchResult json parse error,  body:{}", body, e);
        }
    }

}
