package fm.lizhi.ocean.wave.common.disaster.listener;

import com.ctrip.framework.apollo.ConfigChangeListener;
import com.ctrip.framework.apollo.model.ConfigChangeEvent;
import com.lizhi.commons.config.core.util.ConfigUtils;
import fm.lizhi.ocean.wave.common.disaster.constants.ServerConstant;
import fm.lizhi.ocean.wave.common.disaster.manager.ApolloConfigSyncManager;
import fm.lizhi.ocean.wave.common.manager.CommonPushManager;
import fm.lizhi.ocean.wave.common.util.ThreadUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;


/**
 * Apollo配置变动监听器
 */
@Slf4j
@Component
public class ApolloConfigChangeListener implements ConfigChangeListener {

    @Autowired
    private ApolloConfigSyncManager syncManager;

    @Autowired
    private CommonPushManager commonPushManager;

    /**
     * 默认激活的apollo namespace列表
     */
    private static final String DEFAULT_APOLLO_NAMESPACES_ACTIVE = "application";

    /**
     * 线程池
     */
    private static final ExecutorService THREAD_POOL_EXECUTOR = ThreadUtils.getTtlExecutors(
            "ApolloConfigChangeListener", 2, 5, new LinkedBlockingQueue<Runnable>(100));


    @Override
    public void onChange(ConfigChangeEvent changeEvent) {
        if (!ConfigUtils.getServiceName().equalsIgnoreCase(ServerConstant.MASTER_SERVER_NAME)) {
            //不是主集群，不做操作，只有主集群才处理配置变动
            return;
        }
        //异步处理轮询时间变动
        asyncAnalyzeRoundIntervalChange(changeEvent);
        //配置变动后，同步更新到灾备集群
        syncManager.syncChangeEventConfig(changeEvent);
    }

    /**
     * 轮询配置变动，需要发送通知
     *
     *
     * @param changeEvent 配置变动事件
     */
    private void asyncAnalyzeRoundIntervalChange(ConfigChangeEvent changeEvent) {
        THREAD_POOL_EXECUTOR.execute(new Runnable() {
            @Override
            public void run() {
                try {
                    if (changeEvent.getNamespace().equals(DEFAULT_APOLLO_NAMESPACES_ACTIVE)
                            && changeEvent.isChanged("serverConfig")) {
                        commonPushManager.sendRoundIntervalChangeMsg();
                    }
                } catch (Exception e) {
                    log.error("pushRoundIntervalChangeMsg: ", e);
                }
            }
        });
    }
}
