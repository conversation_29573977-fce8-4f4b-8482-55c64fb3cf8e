package fm.lizhi.ocean.wave.common.util;


import lombok.NonNull;
import org.joda.time.DateTime;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;

import java.util.Calendar;
import java.util.Date;
import java.util.TimeZone;
import java.util.concurrent.TimeUnit;

public class TimeUtil {

    private static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormat.forPattern("yyyy-MM-dd HH:mm:ss");
    private static final long MILLI_SECONDS_OF_ONE_YEAR = 365 * 24 * 3600 * 1000L;

    /**
     * 解析成时间格式
     *
     * @param instant 毫秒
     * @return
     */
    public static String format(long instant) {
        return new DateTime(instant).toString(DATE_TIME_FORMATTER);
    }

    /**
     * 获取时间戳
     *
     * @param time 时间
     * @return
     */
    public static long parse(@NonNull String time) {
        return DateTime.parse(time, DATE_TIME_FORMATTER).getMillis();
    }

    /**
     * 计算年龄
     *
     * @param birthday 生日
     * @return
     */
    public static int calAge(long birthday) {
        if (birthday <= 0) {
            return 1;
        }
        Calendar uc = Calendar.getInstance();
        uc.setTimeInMillis(birthday);
        int year = uc.get(Calendar.YEAR);
        int month = uc.get(Calendar.MONTH) + 1;
        int day = uc.get(Calendar.DAY_OF_MONTH);

        Calendar c = Calendar.getInstance();
        int curYear = c.get(Calendar.YEAR);
        int curMonth = c.get(Calendar.MONTH) + 1;
        int curDay = c.get(Calendar.DAY_OF_MONTH);

        int age = curYear - year - 1;
        if (curMonth > month) {
            age += 1;
        } else if (curMonth == month) {
            if (curDay >= day) {
                age += 1;
            }
        }
        if (age <= 0) {
            age = 1;
        }
        return age;
    }

    /**
     * 计算当前时间本周开始时间
     *
     * @return 本周开始时间
     */
    public static Date getThisWeek() {
        Calendar c = Calendar.getInstance();
        c.setFirstDayOfWeek(Calendar.MONDAY);
        c.set(Calendar.DAY_OF_WEEK, Calendar.MONDAY);
        c.set(Calendar.HOUR_OF_DAY, 0);
        c.set(Calendar.MINUTE, 0);
        c.set(Calendar.SECOND, 0);
        c.set(Calendar.MILLISECOND, 0);
        return c.getTime();
    }

    /**
     * 获取小时的开始时间
     *
     * @param currentDate 当前时间
     * @return 开始时间
     */
    public static Date getHourStartTime(Date currentDate) {
        // 设置时区为东八区
        TimeZone tz = TimeZone.getTimeZone("GMT+8:00");
        // 创建一个 Calendar 实例，并设置其时区
        Calendar calendar = Calendar.getInstance(tz);
        // 设置当前时间
        calendar.setTime(currentDate);

        // 重置分钟、秒和毫秒
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        // 返回小时开始时间
        return calendar.getTime();
    }

    /**
     * 获取小时的结束时间
     *
     * @param currentDate 当前时间
     * @return 结束时间
     */
    public static Date getHourEndTime(Date currentDate) {
        // 设置时区为东八区
        TimeZone tz = TimeZone.getTimeZone("GMT+8:00");
        // 创建一个 Calendar 实例，并设置其时区
        Calendar calendar = Calendar.getInstance(tz);
        // 设置当前时间
        calendar.setTime(currentDate);

        // 设置分钟、秒和毫秒为最大值
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);

        // 返回小时结束时间
        return calendar.getTime();
    }

    /**
     * 将时间戳向上取整到指定的时间单位
     *
     * @param timestamp 原始时间戳（毫秒）
     * @param unit      时间单位（TimeUnit枚举）
     * @param value     单位数量（例如5表示5个单位）
     * @return 向上取整后的时间戳
     */
    public static long ceilTimestamp(long timestamp, TimeUnit unit, int value) {
        // 将单位转换为毫秒
        long unitInMillis = unit.toMillis(value);

        // 计算余数
        long remainder = timestamp % unitInMillis;

        // 如果余数为0，则不需要取整
        if (remainder == 0) {
            return timestamp;
        }

        // 向上取整
        return timestamp + (unitInMillis - remainder);
    }
}