<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>fm.lizhi.ocean.wave</groupId>
    <artifactId>web-ocean-wave</artifactId>
    <packaging>pom</packaging>
    <version>1.5.26</version>

    <modules>
        <module>wave-common</module>
        <module>wave-component</module>
        <module>wave-server</module>
        <module>wave-generic</module>
    </modules>

    <properties>
        <!-- ====================        项目构建的属性        ==================== -->

        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <maven-compiler-plugin.version>3.10.1</maven-compiler-plugin.version>
        <maven.compiler.parameters>true</maven.compiler.parameters>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <maven-source-plugin.version>3.2.1</maven-source-plugin.version>
        <maven-shade-plugin.version>3.5.0</maven-shade-plugin.version>
        <maven-install-plugin.version>3.1.0</maven-install-plugin.version>
        <maven-deploy-plugin.version>3.0.0</maven-deploy-plugin.version>
        <maven-assembly-plugin.version>2.6</maven-assembly-plugin.version>
        <maven-site-plugin.version>3.12.1</maven-site-plugin.version>
        <autoapi-maven-plugin.version>1.0.0.RC</autoapi-maven-plugin.version>
        <mybatis-generator-maven-plugin.version>1.3.5</mybatis-generator-maven-plugin.version>
        <datastore-generator.version>1.4.5</datastore-generator.version>


        <!-- ====================        基础架构的属性        ==================== -->

        <!-- 基础架构BOM -->
        <lz-common-dependencies-bom.version>2.0.42</lz-common-dependencies-bom.version>
        <!-- xxl-job -->
        <dispatcher-executor.version>2.0.11</dispatcher-executor.version>
        <sentinel-all.version>1.7.2.13</sentinel-all.version>


        <!-- ====================        第二方框架属性        ==================== -->

        <lamp-common.version>1.1.5</lamp-common.version>
        <amusement-websecurity.version>1.0.6</amusement-websecurity.version>
        <lz-content-review.version>4.2.8</lz-content-review.version>


        <!-- ====================        第三方框架属性        ==================== -->

        <lombok.version>1.18.36</lombok.version>
        <lombok-mapstruct-binding.version>0.2.0</lombok-mapstruct-binding.version>
        <mapstruct.version>1.5.5.Final</mapstruct.version>
        <transmittable-thread-local.version>2.14.2</transmittable-thread-local.version>
        <pinyin4j.version>2.5.1</pinyin4j.version>
        <protobuf-java-util.version>3.25.2</protobuf-java-util.version>
        <hutool.version>5.8.27</hutool.version>


        <!-- ====================        基础架构服务依赖        ==================== -->
        <basecomp-upload-sdk.version>2.1.1</basecomp-upload-sdk.version>
        <apm-toolkit-trace.version>5.0.0-RC2</apm-toolkit-trace.version>
        <commons-collections4.version>4.4</commons-collections4.version>
        <commons-collections.version>3.2.2</commons-collections.version>
        <lz-commons-rome-push-cm-api.version>1.4.3</lz-commons-rome-push-cm-api.version>


        <!-- ====================        平台服务依赖        ==================== -->

        <lz-commons-verify-api.version>1.0.9</lz-commons-verify-api.version>
        <lz-common-accountcenter-api.version>1.0.14</lz-common-accountcenter-api.version>
        <wave-server-common-starter.version>1.0.11-SNAPSHOT</wave-server-common-starter.version>


        <!-- ====================        交易服务依赖        ==================== -->

        <lz-common-trade-withdrawal-api.version>1.0.4</lz-common-trade-withdrawal-api.version>
        <lz-payment-api.version>7.1.8</lz-payment-api.version>
        <lz-common-trade-query-center.version>1.5.0</lz-common-trade-query-center.version>

        <!-- ====================        风控服务依赖        ==================== -->

        <lz-account-security-api.version>2.2.3</lz-account-security-api.version>

        <!-- ====================        AI平台依赖        ==================== -->
        <lz-ocean-godzilla-api.version>1.2.6</lz-ocean-godzilla-api.version>


        <!-- ====================        重定位API依赖        ==================== -->

        <wave-relocation.version>2.0.9</wave-relocation.version>


        <!-- ====================        黑叶服务依赖        ==================== -->

        <lz-hy-common-util.version>1.6.9</lz-hy-common-util.version>
        <lz-hy-content-api.version>1.0.17</lz-hy-content-api.version>
        <lz-hy-room-api.version>3.7.1-SNAPSHOT</lz-hy-room-api.version>
        <lz-hy-gift-api.version>2.6.27</lz-hy-gift-api.version>
        <lz-hy-activity-api.version>2.4.4-SNAPSHOT</lz-hy-activity-api.version>
        <lz-hy-family-api.version>1.3.22</lz-hy-family-api.version>
        <lz-hy-security-api.version>0.0.8</lz-hy-security-api.version>
        <lz-hy-amusement-api.version>1.4.35</lz-hy-amusement-api.version>
        <lz-hy-vip-api.version>1.0.19-SNAPSHOT</lz-hy-vip-api.version>
        <lz-hy-social-api.version>1.1.10</lz-hy-social-api.version>
        <lz-hy-cmpt-behavior-msg-protocol.version>0.0.3</lz-hy-cmpt-behavior-msg-protocol.version>
        <lz-hy-user-account-api.version>1.0.24-SNAPSHOT</lz-hy-user-account-api.version>
        <lz-hy-app-heiyeprotocol.version>4.8.6</lz-hy-app-heiyeprotocol.version>
        <lz-hy-push-api.version>1.6.0</lz-hy-push-api.version>
        <lz-hy-data-common.version>1.13.17</lz-hy-data-common.version>
        <app-heiyeprotocol.version>5.0.1</app-heiyeprotocol.version>
        <modelmapper.version>2.3.0</modelmapper.version>
        <cloning.version>1.9.2</cloning.version>
        <lz-commons-structs-blackwidow-api.version>1.0.3</lz-commons-structs-blackwidow-api.version>
        <lz-commons-structs-beeper-api.version>1.0.3</lz-commons-structs-beeper-api.version>

        <!-- ====================        pp服务依赖        ==================== -->

        <lz-pp-room-api.version>3.4.4</lz-pp-room-api.version>
        <lz-pp-vip-api.version>2.5.8</lz-pp-vip-api.version>
        <lz-pp-security-api.version>1.10.1</lz-pp-security-api.version>
        <lz-pp-user-account-api.version>1.9.7</lz-pp-user-account-api.version>
        <lz-pp-social-api.version>2.2.1</lz-pp-social-api.version>
        <lz-pp-family-api.version>1.5.4</lz-pp-family-api.version>
        <lz-pp-oss-api.version>3.0.3-SNAPSHOT</lz-pp-oss-api.version>
        <lz-pp-content-api.version>1.6.0</lz-pp-content-api.version>
        <lz-pp-decorate-api.version>1.4.4</lz-pp-decorate-api.version>
        <lz-app-upload-api-version>1.3.0</lz-app-upload-api-version>
<!--        <lz-pp-activity-api.version>2.7.0</lz-pp-activity-api.version>-->
        <lz-pp-push-api.version>1.2.6-SNAPSHOT</lz-pp-push-api.version>
        <pp-rec-recommend-platform-api.version>0.0.2-SNAPSHOT</pp-rec-recommend-platform-api.version>
        <lz-pp-common-util.version>1.9.3</lz-pp-common-util.version>
        <lz-pp-rcmd-api.version>1.0.0</lz-pp-rcmd-api.version>

        <!-- ====================        西米服务依赖        ==================== -->
        <lz-xm-content-api.version>1.0.10</lz-xm-content-api.version>
        <lz-xm-room-api.version>1.3.7</lz-xm-room-api.version>
        <lz-xm-user-account-api.version>1.3.0</lz-xm-user-account-api.version>
        <lz-xm-family-api.version>1.1.13</lz-xm-family-api.version>
        <lz-xm-decorate-api.version>1.0.3</lz-xm-decorate-api.version>
        <lz-xm-security-api.version>1.0.12</lz-xm-security-api.version>
        <lz-xm-social-api.version>1.1.7</lz-xm-social-api.version>
        <lz-xm-core-api.version>1.1.8</lz-xm-core-api.version>
        <lz-xm-vip-api.version>1.6.4</lz-xm-vip-api.version>
        <lz-xm-push-api.version>1.0.1</lz-xm-push-api.version>
        <lz-xm-trade-api.version>1.5.1</lz-xm-trade-api.version>
        <lz-xm-common-util.version>2.2.5</lz-xm-common-util.version>
        <lz-xm-common-flow-api.version>1.0.1</lz-xm-common-flow-api.version>
        <lz-xm-live-flow-api.version>1.0.7</lz-xm-live-flow-api.version>

        <!-- ====================        创作者 DC        ==================== -->
        <lz-ocean-wave-api.version>1.0.19-SNAPSHOT</lz-ocean-wave-api.version>

        <!-- ====================        创作中心 DC        ==================== -->
        <lz-ocean-wavecenter-api.version>1.5.4.RC1-SNAPSHOT</lz-ocean-wavecenter-api.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <!-- ====================        基础架构的依赖        ==================== -->

            <!-- 基础架构bom -->
            <dependency>
                <groupId>fm.lizhi.common</groupId>
                <artifactId>lz-common-dependencies-bom</artifactId>
                <version>${lz-common-dependencies-bom.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

<!--            上传文件-->
            <dependency>
                <groupId>fm.lizhi.sdk</groupId>
                <artifactId>lz-basecomp-upload-sdk</artifactId>
                <version>${basecomp-upload-sdk.version}</version>
            </dependency>

            <dependency>
                <groupId>fm.lizhi.commons</groupId>
                <artifactId>lz-commons-rome-push-cm-api</artifactId>
                <version>${lz-commons-rome-push-cm-api.version}</version>
            </dependency>

            <!-- ====================        第二方框架依赖        ==================== -->

            <!-- 神灯通用组件 -->
            <dependency>
                <groupId>fm.lizhi.ocean.lamp</groupId>
                <artifactId>lamp-common-config</artifactId>
                <version>${lamp-common.version}</version>
            </dependency>
            <dependency>
                <groupId>fm.lizhi.ocean.lamp</groupId>
                <artifactId>lamp-common-context</artifactId>
                <version>${lamp-common.version}</version>
            </dependency>
            <dependency>
                <groupId>fm.lizhi.ocean.lamp</groupId>
                <artifactId>lamp-common-util</artifactId>
                <version>${lamp-common.version}</version>
            </dependency>
            <dependency>
                <groupId>fm.lizhi.ocean.lamp</groupId>
                <artifactId>lamp-common-logging</artifactId>
                <version>${lamp-common.version}</version>
            </dependency>
            <!-- 互娱web安全组件 -->
            <dependency>
                <groupId>fm.lizhi.amusement.commons</groupId>
                <artifactId>amusement-websecurity</artifactId>
                <version>${amusement-websecurity.version}</version>
            </dependency>
            <!-- 内容审核组件 -->
            <dependency>
                <groupId>fm.lizhi</groupId>
                <artifactId>lz-content-review-api</artifactId>
                <version>${lz-content-review.version}</version>
            </dependency>

            <dependency>
                <groupId>fm.lizhi.ocean.wave</groupId>
                <artifactId>wave-relocation-sentinel</artifactId>
                <version>${wave-relocation.version}</version>
            </dependency>

            <!-- ====================        第三方框架依赖        ==================== -->

            <!--  Hutool工具类 -->
            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-all</artifactId>
                <version>${hutool.version}</version>
            </dependency>

            <!--  飞书sdk          -->
            <dependency>
                <groupId>com.larksuite.oapi</groupId>
                <artifactId>oapi-sdk</artifactId>
                <version>2.1.1</version>
            </dependency>

            <!-- lombok -->
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>${lombok.version}</version>
            </dependency>
            <!-- lombok-mapstruct注解处理器 -->
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok-mapstruct-binding</artifactId>
                <version>${lombok-mapstruct-binding.version}</version>
            </dependency>
            <!-- 运行时类型映射 -->
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct</artifactId>
                <version>${mapstruct.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct-processor</artifactId>
                <version>${mapstruct.version}</version>
            </dependency>
            <!-- 灯塔支持跨线程路由 -->
            <dependency>
                <groupId>org.apache.skywalking</groupId>
                <artifactId>apm-toolkit-trace</artifactId>
                <version>${apm-toolkit-trace.version}</version>
            </dependency>
            <!-- 支持ThreadLocal的线程池 -->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>transmittable-thread-local</artifactId>
                <version>${transmittable-thread-local.version}</version>
            </dependency>
            <!-- apache集合类新版, 推荐 -->
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-collections4</artifactId>
                <version>${commons-collections4.version}</version>
            </dependency>
            <!-- apache集合类, 不推荐 -->
            <dependency>
                <groupId>commons-collections</groupId>
                <artifactId>commons-collections</artifactId>
                <version>${commons-collections.version}</version>
            </dependency>
            <dependency>
                <groupId>org.modelmapper</groupId>
                <artifactId>modelmapper</artifactId>
                <version>${modelmapper.version}</version>
            </dependency>

            <!--深拷贝工具，性能高-->
            <dependency>
                <groupId>uk.com.robust-it</groupId>
                <artifactId>cloning</artifactId>
                <version>${cloning.version}</version>
            </dependency>

            <dependency>
                <groupId>com.google.protobuf</groupId>
                <artifactId>protobuf-java-util</artifactId>
                <version>${protobuf-java-util.version}</version>
            </dependency>

            <!-- ====================        基础架构服务依赖        ==================== -->



            <!-- ====================        平台服务依赖        ==================== -->

            <dependency>
                <groupId>fm.lizhi.common.verify</groupId>
                <artifactId>lz-commons-verify-api</artifactId>
                <version>${lz-commons-verify-api.version}</version>
            </dependency>
            <dependency>
                <groupId>fm.lizhi</groupId>
                <artifactId>lz-common-accountcenter-api</artifactId>
                <version>${lz-common-accountcenter-api.version}</version>
            </dependency>

            <dependency>
                <groupId>fm.lizhi.ocean.wave</groupId>
                <artifactId>wave-server-common-starter</artifactId>
                <version>${wave-server-common-starter.version}</version>
            </dependency>


            <!-- ====================        交易服务依赖        ==================== -->

            <dependency>
                <groupId>fm.lizhi</groupId>
                <artifactId>lz-common-trade-withdrawal-api</artifactId>
                <version>${lz-common-trade-withdrawal-api.version}</version>
            </dependency>

            <dependency>
                <groupId>fm.lizhi</groupId>
                <artifactId>lz-payment-api</artifactId>
                <version>${lz-payment-api.version}</version>
            </dependency>
            <dependency>
                <groupId>fm.lizhi</groupId>
                <artifactId>lz-common-trade-query-center-api</artifactId>
                <version>${lz-common-trade-query-center.version}</version>
            </dependency>


            <!-- ====================        风控服务依赖        ==================== -->

            <dependency>
                <groupId>fm.lizhi</groupId>
                <artifactId>lz-account-security-api</artifactId>
                <version>${lz-account-security-api.version}</version>
            </dependency>

            <!-- ====================        AI平台依赖        ==================== -->
            <dependency>
                <groupId>fm.lizhi.ocean.godzilla</groupId>
                <artifactId>lz-ocean-godzilla-api</artifactId>
                <version>${lz-ocean-godzilla-api.version}</version>
            </dependency>

            <!-- ====================        黑叶服务依赖        ==================== -->

            <dependency>
                <groupId>fm.lizhi.ocean.wave</groupId>
                <artifactId>wave-relocation-hy-api</artifactId>
                <version>${wave-relocation.version}</version>
            </dependency>
            <dependency>
                <groupId>fm.lizhi.hy</groupId>
                <artifactId>lz-hy-common-util</artifactId>
                <version>${lz-hy-common-util.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>app-ppliveprotocol</artifactId>
                        <groupId>com.lizhi.pplive</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>lz-app-client-protobuf</artifactId>
                        <groupId>fm.lizhi.app</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>lz-hy-user-account-api</artifactId>
                        <groupId>fm.lizhi.hy</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>fm.lizhi.hy</groupId>
                <artifactId>lz-hy-content-api</artifactId>
                <version>${lz-hy-content-api.version}</version>
            </dependency>
            <dependency>
                <groupId>fm.lizhi.hy</groupId>
                <artifactId>lz-hy-gift-api</artifactId>
                <version>${lz-hy-gift-api.version}</version>
            </dependency>
            <dependency>
                <groupId>fm.lizhi.hy</groupId>
                <artifactId>lz-hy-room-api</artifactId>
                <version>${lz-hy-room-api.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>fm.lizhi.hy</groupId>
                        <artifactId>lz-hy-content-api</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>fm.lizhi.hy</groupId>
                <artifactId>lz-hy-activity-api</artifactId>
                <version>${lz-hy-activity-api.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>fm.lizhi.security</groupId>
                        <artifactId>lz-hy-security-api</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>fm.hy.family</groupId>
                <artifactId>lz-hy-family-api</artifactId>
                <version>${lz-hy-family-api.version}</version>
            </dependency>
            <dependency>
                <groupId>fm.lizhi.security</groupId>
                <artifactId>lz-hy-security-api</artifactId>
                <version>${lz-hy-security-api.version}</version>
            </dependency>
            <dependency>
                <groupId>fm.lizhi.hy</groupId>
                <artifactId>lz-hy-data-common</artifactId>
                <version>${lz-hy-data-common.version}</version>
            </dependency>
            <dependency>
                <groupId>fm.lizhi</groupId>
                <artifactId>lz-hy-amusement-api</artifactId>
                <version>${lz-hy-amusement-api.version}</version>
            </dependency>
            <dependency>
                <groupId>fm.lizhi.hy</groupId>
                <artifactId>lz-hy-vip-api</artifactId>
                <version>${lz-hy-vip-api.version}</version>
            </dependency>
            <dependency>
                <groupId>fm.lizhi.hy</groupId>
                <artifactId>lz-hy-social-api</artifactId>
                <version>${lz-hy-social-api.version}</version>
            </dependency>
            <dependency>
                <groupId>fm.lizhi.live.cmpt.behavior.msg</groupId>
                <artifactId>lz-hy-cmpt-behavior-msg-protocol</artifactId>
                <version>${lz-hy-cmpt-behavior-msg-protocol.version}</version>
            </dependency>
            <dependency>
                <groupId>fm.lizhi.hy</groupId>
                <artifactId>lz-hy-user-account-api</artifactId>
                <version>${lz-hy-user-account-api.version}</version>
            </dependency>

            <dependency>
                <groupId>fm.lizhi.hy</groupId>
                <artifactId>lz-hy-push-api</artifactId>
                <version>${lz-hy-push-api.version}</version>
            </dependency>

            <dependency>
                <groupId>com.lizhi.heiye</groupId>
                <artifactId>app-heiyeprotocol</artifactId>
                <version>${app-heiyeprotocol.version}</version>
            </dependency>

            <dependency>
                <groupId>fm.lizhi.amusement.commons</groupId>
                <artifactId>lz-commons-structs-blackwidow-api</artifactId>
                <version>${lz-commons-structs-blackwidow-api.version}</version>
            </dependency>

            <dependency>
                <groupId>fm.lizhi.amusement.commons</groupId>
                <artifactId>lz-commons-structs-beeper-api</artifactId>
                <version>${lz-commons-structs-blackwidow-api.version}</version>
            </dependency>


            <!-- ====================        pp服务依赖        ==================== -->

            <dependency>
                <groupId>fm.lizhi.ocean.wave</groupId>
                <artifactId>wave-relocation-pp-api</artifactId>
                <version>${wave-relocation.version}</version>
            </dependency>

            <dependency>
                <groupId>fm.lizhi.live</groupId>
                <artifactId>lz-pp-room-api</artifactId>
                <version>${lz-pp-room-api.version}</version>
            </dependency>
            <dependency>
                <groupId>fm.lizhi.pp</groupId>
                <artifactId>lz-pp-vip-api</artifactId>
                <version>${lz-pp-vip-api.version}</version>
            </dependency>
            <dependency>
                <groupId>fm.lizhi.pp</groupId>
                <artifactId>lz-pp-security-api</artifactId>
                <version>${lz-pp-security-api.version}</version>
            </dependency>

            <dependency>
                <groupId>fm.lizhi.pp</groupId>
                <artifactId>lz-pp-user-account-api</artifactId>
                <version>${lz-pp-user-account-api.version}</version>
            </dependency>

<!--            <dependency>-->
<!--                <groupId>fm.lizhi.pp</groupId>-->
<!--                <artifactId>lz-pp-activity-api</artifactId>-->
<!--                <version>${lz-pp-activity-api.version}</version>-->
<!--            </dependency>-->

            <dependency>
                <groupId>fm.lizhi.pp</groupId>
                <artifactId>lz-pp-social-api</artifactId>
                <version>${lz-pp-social-api.version}</version>
            </dependency>

            <dependency>
                <groupId>fm.pp.family</groupId>
                <artifactId>lz-pp-family-api</artifactId>
                <version>${lz-pp-family-api.version}</version>
            </dependency>

            <dependency>
                <groupId>fm.lizhi.live</groupId>
                <artifactId>lz-pp-oss-api</artifactId>
                <version>${lz-pp-oss-api.version}</version>
            </dependency>

            <dependency>
                <groupId>fm.lizhi.pp</groupId>
                <artifactId>lz-pp-decorate-api</artifactId>
                <version>${lz-pp-decorate-api.version}</version>
            </dependency>


            <dependency>
                <groupId>fm.lizhi.pp</groupId>
                <artifactId>lz-pp-content-api</artifactId>
                <version>${lz-pp-content-api.version}</version>
            </dependency>

            <dependency>
                <groupId>fm.lizhi.pp</groupId>
                <artifactId>lz-pp-rcmd-api</artifactId>
                <version>${lz-pp-rcmd-api.version}</version>
            </dependency>

            <dependency>
                <groupId>fm.lizhi</groupId>
                <artifactId>lz-app-upload-api</artifactId>
                <version>${lz-app-upload-api-version}</version>
            </dependency>

            <dependency>
                <groupId>fm.lizhi.pp</groupId>
                <artifactId>lz-pp-push-api</artifactId>
                <version>${lz-pp-push-api.version}</version>
            </dependency>

            <dependency>
                <groupId>fm.lizhi.datamining</groupId>
                <artifactId>pp_rec_recommend_platform_api</artifactId>
                <version>${pp-rec-recommend-platform-api.version}</version>
            </dependency>

            <dependency>
                <groupId>fm.lizhi.pp</groupId>
                <artifactId>lz-pp-common-util</artifactId>
                <version>${lz-pp-common-util.version}</version>
            </dependency>

            <!-- ====================        西米服务依赖        ==================== -->

            <dependency>
                <groupId>fm.lizhi.xm</groupId>
                <artifactId>lz-xm-common-flow-api</artifactId>
                <version>${lz-xm-common-flow-api.version}</version>
            </dependency>

            <dependency>
                <groupId>fm.lizhi.xm</groupId>
                <artifactId>lz-xm-security-api</artifactId>
                <version>${lz-xm-security-api.version}</version>
            </dependency>

            <dependency>
                <groupId>fm.lizhi.ocean.wave</groupId>
                <artifactId>wave-relocation-xm-api</artifactId>
                <version>${wave-relocation.version}</version>
            </dependency>
            <dependency>
                <groupId>fm.lizhi.xm</groupId>
                <artifactId>lz-xm-content-api</artifactId>
                <version>${lz-xm-content-api.version}</version>
            </dependency>
            <dependency>
                <groupId>fm.lizhi.xm</groupId>
                <artifactId>lz-xm-room-api</artifactId>
                <version>${lz-xm-room-api.version}</version>
            </dependency>
            <dependency>
                <groupId>fm.lizhi.xm</groupId>
                <artifactId>lz-xm-user-account-api</artifactId>
                <version>${lz-xm-user-account-api.version}</version>
            </dependency>
            <dependency>
                <groupId>fm.lizhi.xm</groupId>
                <artifactId>lz-xm-family-api</artifactId>
                <version>${lz-xm-family-api.version}</version>
            </dependency>
            <dependency>
                <groupId>fm.lizhi.xm</groupId>
                <artifactId>lz-xm-decorate-api</artifactId>
                <version>${lz-xm-decorate-api.version}</version>
            </dependency>

            <dependency>
                <groupId>fm.lizhi.xm</groupId>
                <artifactId>lz-xm-social-api</artifactId>
                <version>${lz-xm-social-api.version}</version>
            </dependency>
            <dependency>
                <groupId>fm.lizhi.xm</groupId>
                <artifactId>lz-xm-live-flow-api</artifactId>
                <version>${lz-xm-live-flow-api.version}</version>
            </dependency>
            <dependency>
                <groupId>fm.lizhi.xm</groupId>
                <artifactId>lz-xm-trade-api</artifactId>
                <version>${lz-xm-trade-api.version}</version>
            </dependency>

            <dependency>
                <groupId>fm.lizhi.xm</groupId>
                <artifactId>lz-xm-core-api</artifactId>
                <version>${lz-xm-core-api.version}</version>
            </dependency>

            <dependency>
                <groupId>fm.lizhi.xm</groupId>
                <artifactId>lz-xm-vip-api</artifactId>
                <version>${lz-xm-vip-api.version}</version>
            </dependency>

            <dependency>
                <groupId>fm.lizhi.xm</groupId>
                <artifactId>lz-xm-push-api</artifactId>
                <version>${lz-xm-push-api.version}</version>
            </dependency>

            <dependency>
                <groupId>fm.lizhi.xm</groupId>
                <artifactId>lz-xm-cmpt-behavior-msg-protocol</artifactId>
                <version>1.0.0-SNAPSHOT</version>
            </dependency>

            <dependency>
                <groupId>fm.lizhi.xm</groupId>
                <artifactId>lz-xm-common-util</artifactId>
                <version>${lz-xm-common-util.version}</version>
            </dependency>

            <!-- ====================        测试服务依赖        ==================== -->
            <dependency>
                <groupId>fm.lizhi.ocean.wave</groupId>
                <artifactId>wave-relocation-test-api</artifactId>
                <version>${wave-relocation.version}</version>
            </dependency>

<!--            <dependency>-->
<!--                <groupId>fm.lizhi.pp</groupId>-->
<!--                <artifactId>lz-pp-activity-api</artifactId>-->
<!--                <version>${lz-pp-activity-api.version}</version>-->
<!--            </dependency>-->

            <dependency>
                <groupId>fm.lizhi.common</groupId>
                <artifactId>dispatcher-executor</artifactId>
                <version>${dispatcher-executor.version}</version>
            </dependency>
            <dependency>
                <groupId>com.belerweb</groupId>
                <artifactId>pinyin4j</artifactId>
                <version>${pinyin4j.version}</version>
            </dependency>

            <!-- ====================        实名组件依赖服务        ==================== -->
            <dependency>
                <groupId>com.alipay.sdk</groupId>
                <artifactId>alipay-sdk-java</artifactId>
                <version>4.39.31.ALL</version>
            </dependency>

            <!-- ====================        创作者 DC        ==================== -->
            <dependency>
                <groupId>fm.lizhi.ocean.wave</groupId>
                <artifactId>lz-ocean-wave-api</artifactId>
                <version>${lz-ocean-wave-api.version}</version>
            </dependency>

            <!-- ====================        创作中心 DC        ==================== -->
            <dependency>
                <groupId>fm.lizhi.ocean.wavecenter</groupId>
                <artifactId>wavecenter-api</artifactId>
                <version>${lz-ocean-wavecenter-api.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>fm.lizhi.ocean.wave</groupId>
                        <artifactId>wave-server-common-context</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

        </dependencies>
    </dependencyManagement>

    <dependencies>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok-mapstruct-binding</artifactId>
            <scope>provided</scope>
        </dependency>
    </dependencies>

    <build>
        <pluginManagement>
            <plugins>
                <!-- 编译插件 -->
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-compiler-plugin</artifactId>
                    <version>${maven-compiler-plugin.version}</version>
                </plugin>
                <!-- 源码插件 -->
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-source-plugin</artifactId>
                    <version>${maven-source-plugin.version}</version>
                    <executions>
                        <execution>
                            <id>make-source</id>
                            <phase>package</phase>
                            <goals>
                                <goal>jar-no-fork</goal>
                            </goals>
                        </execution>
                    </executions>
                </plugin>

                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-shade-plugin</artifactId>
                    <version>${maven-shade-plugin.version}</version>
                </plugin>
                <!-- 安装插件 -->
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-install-plugin</artifactId>
                    <version>${maven-install-plugin.version}</version>
                </plugin>
                <!-- 部署插件 -->
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-deploy-plugin</artifactId>
                    <version>${maven-deploy-plugin.version}</version>
                </plugin>
                <!-- 装配插件 -->
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-assembly-plugin</artifactId>
                    <version>${maven-assembly-plugin.version}</version>
                    <configuration>
                        <descriptors>
                            <descriptor>src/main/assembly/assembly.xml</descriptor>
                        </descriptors>
                    </configuration>
                    <executions>
                        <execution>
                            <id>make-assembly</id>
                            <phase>package</phase>
                            <goals>
                                <goal>single</goal>
                            </goals>
                        </execution>
                    </executions>
                </plugin>
                <!-- 站点插件 -->
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-site-plugin</artifactId>
                    <version>${maven-site-plugin.version}</version>
                </plugin>
                <!-- protobuf dc 插件编译 -->
                <plugin>
                    <groupId>fm.lizhi.commons</groupId>
                    <artifactId>autoapi-maven-plugin</artifactId>
                    <version>${autoapi-maven-plugin.version}</version>
                </plugin>
                <!-- mybatis生成插件 -->
                <plugin>
                    <groupId>org.mybatis.generator</groupId>
                    <artifactId>mybatis-generator-maven-plugin</artifactId>
                    <version>${mybatis-generator-maven-plugin.version}</version>
                    <configuration>
                        <verbose>true</verbose>
                        <overwrite>true</overwrite>
                        <configurationFile>generator/mybatisGeneratorConfig.xml</configurationFile>
                    </configuration>
                    <dependencies>
                        <!-- datastore生成插件 -->
                        <dependency>
                            <groupId>fm.lizhi.common</groupId>
                            <artifactId>datastore-generator</artifactId>
                            <version>1.4.5</version>
                        </dependency>
                    </dependencies>
                </plugin>
            </plugins>
        </pluginManagement>

        <plugins>
            <!-- 编译插件 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
            </plugin>
            <!-- 源码插件 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
            </plugin>
            <!-- 安装插件 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-install-plugin</artifactId>
            </plugin>
            <!-- 部署插件 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-deploy-plugin</artifactId>
            </plugin>
        </plugins>
    </build>

    <repositories>
        <repository>
            <id>central</id>
            <name>Central</name>
            <url>http://maven.lizhi.fm:8081/nexus/content/groups/public/</url>
            <snapshots>
                <enabled>true</enabled>
            </snapshots>
            <releases>
                <enabled>true</enabled>
                <updatePolicy>never</updatePolicy>
            </releases>
        </repository>
        <repository>
            <id>codehaus-snapshots</id>
            <name>Codehaus Snapshots</name>
            <url>http://maven.lizhi.fm:8081/nexus/content/groups/public/</url>
            <snapshots>
                <enabled>true</enabled>
            </snapshots>
            <releases>
                <enabled>false</enabled>
                <updatePolicy>never</updatePolicy>
            </releases>
        </repository>
    </repositories>

    <pluginRepositories>
        <pluginRepository>
            <id>central</id>
            <name>Maven Plugin Repository</name>
            <url>http://maven.lizhi.fm:8081/nexus/content/groups/public/</url>
            <layout>default</layout>
            <snapshots>
                <enabled>true</enabled>
            </snapshots>
            <releases>
                <updatePolicy>never</updatePolicy>
            </releases>
        </pluginRepository>
    </pluginRepositories>

    <distributionManagement>
        <repository>
            <id>release</id>
            <url>http://maven.lizhi.fm:8081/nexus/content/repositories/releases</url>
        </repository>
        <snapshotRepository>
            <id>snapshots</id>
            <url>http://maven.lizhi.fm:8081/nexus/content/repositories/snapshots</url>
        </snapshotRepository>
    </distributionManagement>
</project>
